{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingPanel.tsx\";\nimport React from 'react';\nimport { Plus, Mic2, Square } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RecordingPanel = ({\n  recordingState,\n  onStartRecording,\n  onStopRecording\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col justify-center items-center space-y-8 p-6 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/4 left-1/4 w-1 h-1 bg-blue-400/15 rounded-full animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-3/4 right-1/4 w-0.5 h-0.5 bg-purple-400/20 rounded-full animate-ping\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 left-3/4 w-1 h-1 bg-indigo-400/15 rounded-full animate-bounce\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative group\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute inset-0 rounded-full transition-all duration-2000 ease-out ${recordingState.isRecording ? 'bg-gradient-to-br from-red-500/10 via-pink-500/8 to-red-600/5 animate-pulse scale-125' : 'bg-gradient-to-br from-blue-500/6 via-indigo-500/4 to-purple-600/3 scale-110 group-hover:scale-125'}`,\n        style: {\n          width: '140px',\n          height: '140px',\n          marginLeft: '-70px',\n          marginTop: '-70px',\n          left: '50%',\n          top: '50%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute inset-0 rounded-full transition-all duration-1500 ease-out ${recordingState.isRecording ? 'bg-gradient-to-br from-red-400/15 via-pink-400/12 to-red-500/8 scale-115' : 'bg-gradient-to-br from-blue-400/10 via-indigo-400/8 to-purple-500/6 scale-105 group-hover:scale-115'}`,\n        style: {\n          width: '110px',\n          height: '110px',\n          marginLeft: '-55px',\n          marginTop: '-55px',\n          left: '50%',\n          top: '50%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `relative w-20 h-20 sm:w-24 sm:h-24 rounded-full flex items-center justify-center transition-all duration-1000 ease-out transform backdrop-blur-2xl border ${recordingState.isRecording ? 'bg-gradient-to-br from-red-500/25 via-pink-500/20 to-red-600/15 border-red-300/30 shadow-xl shadow-red-500/30 scale-105' : 'bg-gradient-to-br from-slate-800/25 via-slate-700/20 to-slate-600/15 border-slate-300/25 shadow-xl shadow-blue-500/15 scale-100 hover:scale-105 group-hover:border-blue-300/40'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `absolute inset-2 rounded-full transition-all duration-700 backdrop-blur-lg ${recordingState.isRecording ? 'bg-gradient-to-br from-red-400/20 via-pink-400/15 to-red-500/12' : 'bg-gradient-to-br from-slate-400/12 via-slate-500/10 to-slate-600/8 group-hover:from-blue-400/15 group-hover:to-indigo-500/12'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `relative w-10 h-10 rounded-full flex items-center justify-center transition-all duration-500 ${recordingState.isRecording ? 'bg-gradient-to-br from-red-200/15 to-red-400/12 shadow-inner' : 'bg-gradient-to-br from-slate-200/12 to-slate-400/10 shadow-inner group-hover:from-blue-200/15 group-hover:to-blue-400/12'}`,\n          children: /*#__PURE__*/_jsxDEV(Mic2, {\n            className: `h-5 w-5 sm:h-6 sm:w-6 transition-all duration-500 ${recordingState.isRecording ? 'text-red-100 animate-pulse drop-shadow-lg' : 'text-slate-100 group-hover:text-blue-100 group-hover:scale-110 drop-shadow-md'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-2 left-3 w-3 h-3 bg-white/12 rounded-full blur-sm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1 left-2 w-2 h-2 bg-white/20 rounded-full blur-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-4 max-w-lg mx-auto relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-lg sm:text-xl font-bold transition-all duration-700 leading-tight tracking-tight ${recordingState.isRecording ? 'bg-gradient-to-r from-red-200 via-pink-100 to-red-200 bg-clip-text text-transparent' : 'bg-gradient-to-r from-slate-100 via-blue-50 to-indigo-100 bg-clip-text text-transparent'}`,\n          children: recordingState.isRecording ? 'Įrašoma pokalbis' : 'Pradėkite naują pokalbį'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `h-0.5 w-16 mx-auto rounded-full transition-all duration-700 ${recordingState.isRecording ? 'bg-gradient-to-r from-red-400 to-pink-400' : 'bg-gradient-to-r from-blue-400 to-indigo-400'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-slate-300/80 max-w-md mx-auto leading-relaxed font-medium\",\n        children: recordingState.isRecording ? 'Pokalbis įrašomas su AI transkribavimo technologija' : 'Profesionalus garso įrašymas su automatine transkribavimo technologija'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: !recordingState.isRecording ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`),\n        className: \"group relative inline-flex items-center justify-center space-x-3 px-6 py-3 text-sm font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-102 active:scale-98 focus:outline-none\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-blue-500/20 backdrop-blur-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-[1px] bg-gradient-to-t from-white/15 via-white/8 to-transparent rounded-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-blue-500/40 to-transparent rounded-b-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-2xl border border-blue-300/25 transition-all duration-500 group-hover:border-blue-200/40 group-hover:shadow-lg group-hover:shadow-blue-400/20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 rounded-xl bg-blue-400/30 backdrop-blur-lg flex items-center justify-center transition-all duration-300 group-hover:scale-105 border border-blue-300/15 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-blue-400/40 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 text-blue-100 transition-all duration-300 group-hover:rotate-90 group-hover:text-white drop-shadow-sm relative z-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-semibold text-blue-100\",\n            children: \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onStopRecording,\n        className: \"group relative inline-flex items-center justify-center space-x-3 px-6 py-3 text-sm font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-102 active:scale-98 focus:outline-none\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-red-500/15 via-pink-600/12 to-red-700/8 backdrop-blur-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-[1px] bg-gradient-to-t from-white/15 via-white/8 to-transparent rounded-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-2xl border border-red-300/30 transition-all duration-500 group-hover:border-red-200/50 group-hover:shadow-lg group-hover:shadow-red-400/25 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 rounded-lg bg-gradient-to-t from-red-400/25 to-pink-500/15 backdrop-blur-lg flex items-center justify-center transition-all duration-300 group-hover:scale-105 border border-red-300/15\",\n            children: /*#__PURE__*/_jsxDEV(Square, {\n              className: \"h-4 w-4 text-red-100 transition-all duration-300 group-hover:text-white drop-shadow-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-semibold bg-gradient-to-t from-red-100 to-pink-100 bg-clip-text text-transparent\",\n            children: \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), recordingState.isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex items-center space-x-3 px-4 py-2 bg-gradient-to-r from-red-500/12 via-pink-500/10 to-red-600/8 backdrop-blur-xl rounded-xl border border-red-300/20 shadow-lg shadow-red-500/15\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-gradient-to-br from-red-400 to-red-500 rounded-full shadow-md shadow-red-500/50 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 w-3 h-3 bg-red-400/50 rounded-full animate-ping\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-0.5 left-0.5 w-1 h-1 bg-white/60 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-bold bg-gradient-to-r from-red-200 to-pink-100 bg-clip-text text-transparent tracking-wide\",\n            children: \"\\u012ERA\\u0160OMA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-0.5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-0.5 bg-red-300/60 rounded-full animate-pulse\",\n            style: {\n              height: '8px',\n              animationDelay: '0ms'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-0.5 bg-red-300/80 rounded-full animate-pulse\",\n            style: {\n              height: '12px',\n              animationDelay: '150ms'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-0.5 bg-red-300/60 rounded-full animate-pulse\",\n            style: {\n              height: '10px',\n              animationDelay: '300ms'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-0.5 bg-red-300/90 rounded-full animate-pulse\",\n            style: {\n              height: '14px',\n              animationDelay: '450ms'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-0.5 bg-red-300/70 rounded-full animate-pulse\",\n            style: {\n              height: '9px',\n              animationDelay: '600ms'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-red-500/8 to-pink-500/8 rounded-xl blur-lg animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = RecordingPanel;\nvar _c;\n$RefreshReg$(_c, \"RecordingPanel\");", "map": {"version": 3, "names": ["React", "Plus", "Mic2", "Square", "jsxDEV", "_jsxDEV", "RecordingPanel", "recordingState", "onStartRecording", "onStopRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isRecording", "style", "width", "height", "marginLeft", "marginTop", "left", "top", "onClick", "Date", "toLocaleString", "animationDelay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPanel.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { Plus, Mic2, Square, Pause, Play, AlertCircle } from 'lucide-react';\r\nimport { RecordingButton } from './RecordingButton';\r\nimport { RecordingIndicator } from './RecordingIndicator';\r\nimport { Meeting, RecordingState } from '../types/meeting';\r\n\r\ninterface RecordingPanelProps {\r\n  recordingState: RecordingState;\r\n  currentMeeting: Meeting | null;\r\n  onStartRecording: (title: string) => Promise<void>;\r\n  onStopRecording: () => Promise<void>;\r\n  onPauseRecording: () => void;\r\n  onResumeRecording: () => void;\r\n}\r\n\r\nexport const RecordingPanel: React.FC<RecordingPanelProps> = ({\r\n  recordingState,\r\n  onStartRecording,\r\n  onStopRecording,\r\n}) => {\r\n  return (\r\n    <div className=\"flex-1 flex flex-col justify-center items-center space-y-8 p-6 relative overflow-hidden\">\r\n      {/* Subtle Floating Particles */}\r\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n        <div className=\"absolute top-1/4 left-1/4 w-1 h-1 bg-blue-400/15 rounded-full animate-pulse\"></div>\r\n        <div className=\"absolute top-3/4 right-1/4 w-0.5 h-0.5 bg-purple-400/20 rounded-full animate-ping\"></div>\r\n        <div className=\"absolute top-1/2 left-3/4 w-1 h-1 bg-indigo-400/15 rounded-full animate-bounce\"></div>\r\n      </div>\r\n\r\n      {/* Compact Modern Recording Orb */}\r\n      <div className=\"relative group\">\r\n        {/* Subtle Outer Glow Rings */}\r\n        <div className={`absolute inset-0 rounded-full transition-all duration-2000 ease-out ${\r\n          recordingState.isRecording\r\n            ? 'bg-gradient-to-br from-red-500/10 via-pink-500/8 to-red-600/5 animate-pulse scale-125'\r\n            : 'bg-gradient-to-br from-blue-500/6 via-indigo-500/4 to-purple-600/3 scale-110 group-hover:scale-125'\r\n        }`} style={{ width: '140px', height: '140px', marginLeft: '-70px', marginTop: '-70px', left: '50%', top: '50%' }}></div>\r\n\r\n        <div className={`absolute inset-0 rounded-full transition-all duration-1500 ease-out ${\r\n          recordingState.isRecording\r\n            ? 'bg-gradient-to-br from-red-400/15 via-pink-400/12 to-red-500/8 scale-115'\r\n            : 'bg-gradient-to-br from-blue-400/10 via-indigo-400/8 to-purple-500/6 scale-105 group-hover:scale-115'\r\n        }`} style={{ width: '110px', height: '110px', marginLeft: '-55px', marginTop: '-55px', left: '50%', top: '50%' }}></div>\r\n\r\n        {/* Compact Main Orb */}\r\n        <div className={`relative w-20 h-20 sm:w-24 sm:h-24 rounded-full flex items-center justify-center transition-all duration-1000 ease-out transform backdrop-blur-2xl border ${\r\n          recordingState.isRecording\r\n            ? 'bg-gradient-to-br from-red-500/25 via-pink-500/20 to-red-600/15 border-red-300/30 shadow-xl shadow-red-500/30 scale-105'\r\n            : 'bg-gradient-to-br from-slate-800/25 via-slate-700/20 to-slate-600/15 border-slate-300/25 shadow-xl shadow-blue-500/15 scale-100 hover:scale-105 group-hover:border-blue-300/40'\r\n        }`}>\r\n          {/* Compact Inner Glass Layers */}\r\n          <div className={`absolute inset-2 rounded-full transition-all duration-700 backdrop-blur-lg ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-br from-red-400/20 via-pink-400/15 to-red-500/12'\r\n              : 'bg-gradient-to-br from-slate-400/12 via-slate-500/10 to-slate-600/8 group-hover:from-blue-400/15 group-hover:to-indigo-500/12'\r\n          }`}></div>\r\n\r\n          {/* Compact Microphone Container */}\r\n          <div className={`relative w-10 h-10 rounded-full flex items-center justify-center transition-all duration-500 ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-br from-red-200/15 to-red-400/12 shadow-inner'\r\n              : 'bg-gradient-to-br from-slate-200/12 to-slate-400/10 shadow-inner group-hover:from-blue-200/15 group-hover:to-blue-400/12'\r\n          }`}>\r\n            <Mic2 className={`h-5 w-5 sm:h-6 sm:w-6 transition-all duration-500 ${\r\n              recordingState.isRecording\r\n                ? 'text-red-100 animate-pulse drop-shadow-lg'\r\n                : 'text-slate-100 group-hover:text-blue-100 group-hover:scale-110 drop-shadow-md'\r\n            }`} />\r\n          </div>\r\n\r\n          {/* Subtle Light Reflections */}\r\n          <div className=\"absolute top-2 left-3 w-3 h-3 bg-white/12 rounded-full blur-sm\"></div>\r\n          <div className=\"absolute top-1 left-2 w-2 h-2 bg-white/20 rounded-full blur-xs\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Compact Modern Typography */}\r\n      <div className=\"text-center space-y-4 max-w-lg mx-auto relative z-10\">\r\n        <div className=\"space-y-2\">\r\n          <h1 className={`text-lg sm:text-xl font-bold transition-all duration-700 leading-tight tracking-tight ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-r from-red-200 via-pink-100 to-red-200 bg-clip-text text-transparent'\r\n              : 'bg-gradient-to-r from-slate-100 via-blue-50 to-indigo-100 bg-clip-text text-transparent'\r\n          }`}>\r\n            {recordingState.isRecording ? 'Įrašoma pokalbis' : 'Pradėkite naują pokalbį'}\r\n          </h1>\r\n\r\n          <div className={`h-0.5 w-16 mx-auto rounded-full transition-all duration-700 ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-r from-red-400 to-pink-400'\r\n              : 'bg-gradient-to-r from-blue-400 to-indigo-400'\r\n          }`}></div>\r\n        </div>\r\n\r\n        <p className=\"text-sm text-slate-300/80 max-w-md mx-auto leading-relaxed font-medium\">\r\n          {recordingState.isRecording\r\n            ? 'Pokalbis įrašomas su AI transkribavimo technologija'\r\n            : 'Profesionalus garso įrašymas su automatine transkribavimo technologija'\r\n          }\r\n        </p>\r\n      </div>\r\n\r\n      {/* Compact Modern Action Button */}\r\n      <div className=\"relative z-10\">\r\n        {!recordingState.isRecording ? (\r\n          <button\r\n            onClick={() => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}\r\n            className=\"group relative inline-flex items-center justify-center space-x-3 px-6 py-3 text-sm font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-102 active:scale-98 focus:outline-none\"\r\n          >\r\n            {/* Compact Glassmorphic Background */}\r\n            <div className=\"absolute inset-0 bg-blue-500/20 backdrop-blur-xl\"></div>\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/8\"></div>\r\n            <div className=\"absolute inset-[1px] bg-gradient-to-t from-white/15 via-white/8 to-transparent rounded-2xl\"></div>\r\n\r\n            {/* Half-height gradient overlay */}\r\n            <div className=\"absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-blue-500/40 to-transparent rounded-b-2xl\"></div>\r\n\r\n            {/* Subtle Border with Glow */}\r\n            <div className=\"absolute inset-0 rounded-2xl border border-blue-300/25 transition-all duration-500 group-hover:border-blue-200/40 group-hover:shadow-lg group-hover:shadow-blue-400/20\"></div>\r\n\r\n            {/* Compact Icon Container */}\r\n            <div className=\"relative z-10 flex items-center space-x-3\">\r\n              <div className=\"w-8 h-8 rounded-xl bg-blue-400/30 backdrop-blur-lg flex items-center justify-center transition-all duration-300 group-hover:scale-105 border border-blue-300/15 relative overflow-hidden\">\r\n                <div className=\"absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-blue-400/40 to-transparent\"></div>\r\n                <Plus className=\"h-4 w-4 text-blue-100 transition-all duration-300 group-hover:rotate-90 group-hover:text-white drop-shadow-sm relative z-10\" />\r\n              </div>\r\n              <span className=\"text-sm font-semibold text-blue-100\">\r\n                Pradėti įrašymą\r\n              </span>\r\n            </div>\r\n\r\n            {/* Subtle Shine Animation */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out\"></div>\r\n          </button>\r\n        ) : (\r\n          <button\r\n            onClick={onStopRecording}\r\n            className=\"group relative inline-flex items-center justify-center space-x-3 px-6 py-3 text-sm font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-102 active:scale-98 focus:outline-none\"\r\n          >\r\n            {/* Compact Glassmorphic Background */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-red-500/15 via-pink-600/12 to-red-700/8 backdrop-blur-xl\"></div>\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/8\"></div>\r\n            <div className=\"absolute inset-[1px] bg-gradient-to-t from-white/15 via-white/8 to-transparent rounded-2xl\"></div>\r\n\r\n            {/* Subtle Border with Glow */}\r\n            <div className=\"absolute inset-0 rounded-2xl border border-red-300/30 transition-all duration-500 group-hover:border-red-200/50 group-hover:shadow-lg group-hover:shadow-red-400/25 animate-pulse\"></div>\r\n\r\n            {/* Compact Icon Container */}\r\n            <div className=\"relative z-10 flex items-center space-x-3\">\r\n              <div className=\"w-8 h-8 rounded-lg bg-gradient-to-t from-red-400/25 to-pink-500/15 backdrop-blur-lg flex items-center justify-center transition-all duration-300 group-hover:scale-105 border border-red-300/15\">\r\n                <Square className=\"h-4 w-4 text-red-100 transition-all duration-300 group-hover:text-white drop-shadow-sm\" />\r\n              </div>\r\n              <span className=\"text-sm font-semibold bg-gradient-to-t from-red-100 to-pink-100 bg-clip-text text-transparent\">\r\n                Sustabdyti įrašymą\r\n              </span>\r\n            </div>\r\n\r\n            {/* Subtle Shine Animation */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out\"></div>\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Compact Recording Status Badge */}\r\n      {recordingState.isRecording && (\r\n        <div className=\"relative\">\r\n          {/* Compact Status Container */}\r\n          <div className=\"relative flex items-center space-x-3 px-4 py-2 bg-gradient-to-r from-red-500/12 via-pink-500/10 to-red-600/8 backdrop-blur-xl rounded-xl border border-red-300/20 shadow-lg shadow-red-500/15\">\r\n            {/* Compact Recording Indicator */}\r\n            <div className=\"relative flex items-center space-x-2\">\r\n              {/* Simple Recording Dot */}\r\n              <div className=\"relative\">\r\n                <div className=\"w-3 h-3 bg-gradient-to-br from-red-400 to-red-500 rounded-full shadow-md shadow-red-500/50 animate-pulse\"></div>\r\n                <div className=\"absolute inset-0 w-3 h-3 bg-red-400/50 rounded-full animate-ping\"></div>\r\n                <div className=\"absolute top-0.5 left-0.5 w-1 h-1 bg-white/60 rounded-full\"></div>\r\n              </div>\r\n\r\n              {/* Compact Typography */}\r\n              <span className=\"text-sm font-bold bg-gradient-to-r from-red-200 to-pink-100 bg-clip-text text-transparent tracking-wide\">\r\n                ĮRAŠOMA\r\n              </span>\r\n            </div>\r\n\r\n            {/* Compact Waveform Visualization */}\r\n            <div className=\"flex items-center space-x-0.5\">\r\n              <div className=\"w-0.5 bg-red-300/60 rounded-full animate-pulse\" style={{ height: '8px', animationDelay: '0ms' }}></div>\r\n              <div className=\"w-0.5 bg-red-300/80 rounded-full animate-pulse\" style={{ height: '12px', animationDelay: '150ms' }}></div>\r\n              <div className=\"w-0.5 bg-red-300/60 rounded-full animate-pulse\" style={{ height: '10px', animationDelay: '300ms' }}></div>\r\n              <div className=\"w-0.5 bg-red-300/90 rounded-full animate-pulse\" style={{ height: '14px', animationDelay: '450ms' }}></div>\r\n              <div className=\"w-0.5 bg-red-300/70 rounded-full animate-pulse\" style={{ height: '9px', animationDelay: '600ms' }}></div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Subtle Glow Effect */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-red-500/8 to-pink-500/8 rounded-xl blur-lg animate-pulse\"></div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};"], "mappings": ";AAAA,OAAOA,KAAK,MAAiC,OAAO;AACpD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAkC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc5E,OAAO,MAAMC,cAA6C,GAAGA,CAAC;EAC5DC,cAAc;EACdC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EACJ,oBACEJ,OAAA;IAAKK,SAAS,EAAC,yFAAyF;IAAAC,QAAA,gBAEtGN,OAAA;MAAKK,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEN,OAAA;QAAKK,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnGV,OAAA;QAAKK,SAAS,EAAC;MAAmF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzGV,OAAA;QAAKK,SAAS,EAAC;MAAgF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnG,CAAC,eAGNV,OAAA;MAAKK,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE7BN,OAAA;QAAKK,SAAS,EAAE,uEACdH,cAAc,CAACS,WAAW,GACtB,uFAAuF,GACvF,oGAAoG,EACvG;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,MAAM,EAAE,OAAO;UAAEC,UAAU,EAAE,OAAO;UAAEC,SAAS,EAAE,OAAO;UAAEC,IAAI,EAAE,KAAK;UAAEC,GAAG,EAAE;QAAM;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAExHV,OAAA;QAAKK,SAAS,EAAE,uEACdH,cAAc,CAACS,WAAW,GACtB,0EAA0E,GAC1E,qGAAqG,EACxG;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,MAAM,EAAE,OAAO;UAAEC,UAAU,EAAE,OAAO;UAAEC,SAAS,EAAE,OAAO;UAAEC,IAAI,EAAE,KAAK;UAAEC,GAAG,EAAE;QAAM;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGxHV,OAAA;QAAKK,SAAS,EAAE,6JACdH,cAAc,CAACS,WAAW,GACtB,yHAAyH,GACzH,gLAAgL,EACnL;QAAAL,QAAA,gBAEDN,OAAA;UAAKK,SAAS,EAAE,8EACdH,cAAc,CAACS,WAAW,GACtB,iEAAiE,GACjE,+HAA+H;QAClI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGVV,OAAA;UAAKK,SAAS,EAAE,gGACdH,cAAc,CAACS,WAAW,GACtB,8DAA8D,GAC9D,0HAA0H,EAC7H;UAAAL,QAAA,eACDN,OAAA,CAACH,IAAI;YAACQ,SAAS,EAAE,qDACfH,cAAc,CAACS,WAAW,GACtB,2CAA2C,GAC3C,+EAA+E;UAClF;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtFV,OAAA;UAAKK,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNV,OAAA;MAAKK,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEN,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBN,OAAA;UAAIK,SAAS,EAAE,yFACbH,cAAc,CAACS,WAAW,GACtB,qFAAqF,GACrF,yFAAyF,EAC5F;UAAAL,QAAA,EACAJ,cAAc,CAACS,WAAW,GAAG,kBAAkB,GAAG;QAAyB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAELV,OAAA;UAAKK,SAAS,EAAE,+DACdH,cAAc,CAACS,WAAW,GACtB,2CAA2C,GAC3C,8CAA8C;QACjD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAENV,OAAA;QAAGK,SAAS,EAAC,wEAAwE;QAAAC,QAAA,EAClFJ,cAAc,CAACS,WAAW,GACvB,qDAAqD,GACrD;MAAwE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE3E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNV,OAAA;MAAKK,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3B,CAACJ,cAAc,CAACS,WAAW,gBAC1BX,OAAA;QACEmB,OAAO,EAAEA,CAAA,KAAMhB,gBAAgB,CAAC,YAAY,IAAIiB,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAE;QAClFhB,SAAS,EAAC,kOAAkO;QAAAC,QAAA,gBAG5ON,OAAA;UAAKK,SAAS,EAAC;QAAkD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxEV,OAAA;UAAKK,SAAS,EAAC;QAA4E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClGV,OAAA;UAAKK,SAAS,EAAC;QAA4F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGlHV,OAAA;UAAKK,SAAS,EAAC;QAAkG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGxHV,OAAA;UAAKK,SAAS,EAAC;QAAwK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG9LV,OAAA;UAAKK,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDN,OAAA;YAAKK,SAAS,EAAC,0LAA0L;YAAAC,QAAA,gBACvMN,OAAA;cAAKK,SAAS,EAAC;YAAoF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1GV,OAAA,CAACJ,IAAI;cAACS,SAAS,EAAC;YAA6H;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7I,CAAC,eACNV,OAAA;YAAMK,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC;QAAyL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzM,CAAC,gBAETV,OAAA;QACEmB,OAAO,EAAEf,eAAgB;QACzBC,SAAS,EAAC,kOAAkO;QAAAC,QAAA,gBAG5ON,OAAA;UAAKK,SAAS,EAAC;QAAiG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvHV,OAAA;UAAKK,SAAS,EAAC;QAA4E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClGV,OAAA;UAAKK,SAAS,EAAC;QAA4F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGlHV,OAAA;UAAKK,SAAS,EAAC;QAAmL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGzMV,OAAA;UAAKK,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDN,OAAA;YAAKK,SAAS,EAAC,iMAAiM;YAAAC,QAAA,eAC9MN,OAAA,CAACF,MAAM;cAACO,SAAS,EAAC;YAAwF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eACNV,OAAA;YAAMK,SAAS,EAAC,+FAA+F;YAAAC,QAAA,EAAC;UAEhH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC;QAAyL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzM;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLR,cAAc,CAACS,WAAW,iBACzBX,OAAA;MAAKK,SAAS,EAAC,UAAU;MAAAC,QAAA,gBAEvBN,OAAA;QAAKK,SAAS,EAAC,+LAA+L;QAAAC,QAAA,gBAE5MN,OAAA;UAAKK,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBAEnDN,OAAA;YAAKK,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBN,OAAA;cAAKK,SAAS,EAAC;YAA0G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChIV,OAAA;cAAKK,SAAS,EAAC;YAAkE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxFV,OAAA;cAAKK,SAAS,EAAC;YAA4D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAGNV,OAAA;YAAMK,SAAS,EAAC,yGAAyG;YAAAC,QAAA,EAAC;UAE1H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CN,OAAA;YAAKK,SAAS,EAAC,gDAAgD;YAACO,KAAK,EAAE;cAAEE,MAAM,EAAE,KAAK;cAAEQ,cAAc,EAAE;YAAM;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvHV,OAAA;YAAKK,SAAS,EAAC,gDAAgD;YAACO,KAAK,EAAE;cAAEE,MAAM,EAAE,MAAM;cAAEQ,cAAc,EAAE;YAAQ;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1HV,OAAA;YAAKK,SAAS,EAAC,gDAAgD;YAACO,KAAK,EAAE;cAAEE,MAAM,EAAE,MAAM;cAAEQ,cAAc,EAAE;YAAQ;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1HV,OAAA;YAAKK,SAAS,EAAC,gDAAgD;YAACO,KAAK,EAAE;cAAEE,MAAM,EAAE,MAAM;cAAEQ,cAAc,EAAE;YAAQ;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1HV,OAAA;YAAKK,SAAS,EAAC,gDAAgD;YAACO,KAAK,EAAE;cAAEE,MAAM,EAAE,KAAK;cAAEQ,cAAc,EAAE;YAAQ;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNV,OAAA;QAAKK,SAAS,EAAC;MAAiG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACa,EAAA,GAxLWtB,cAA6C;AAAA,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}