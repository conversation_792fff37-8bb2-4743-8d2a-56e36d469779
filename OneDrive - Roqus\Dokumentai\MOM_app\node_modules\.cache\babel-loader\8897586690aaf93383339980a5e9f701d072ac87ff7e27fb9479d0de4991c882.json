{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\TodosPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Plus, Edit, Trash2, X, Check } from 'lucide-react';\nimport { Search, ChevronDown, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TodosPage = () => {\n  _s();\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: '<PERSON>sir<PERSON><PERSON><PERSON> prezentaci<PERSON>i',\n    completed: false,\n    date: '2024-01-20',\n    priority: 'high',\n    project: 'Aexn CRM analitika',\n    owner: '<PERSON>'\n  }, {\n    id: 2,\n    text: 'Susitikimas su klientu',\n    completed: true,\n    date: '2024-01-18',\n    priority: 'medium',\n    project: 'Aexn | <PERSON><PERSON><PERSON>',\n    owner: '<PERSON><PERSON>'\n  }, {\n    id: 3,\n    text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokumentus',\n    completed: false,\n    date: '2024-01-22',\n    priority: 'low',\n    project: 'Aexn_analitika',\n    owner: 'Ona Onaitytė'\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false,\n    priority: 'medium',\n    project: 'Aexn_analitika',\n    owner: 'Jonas Jonaitis'\n  }]);\n  const [editingTodo, setEditingTodo] = useState(null);\n  const [editingText, setEditingText] = useState('');\n  const [editingProject, setEditingProject] = useState('');\n  const [editingOwner, setEditingOwner] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState('medium');\n  const [newTodoProject, setNewTodoProject] = useState('');\n  const [newTodoOwner, setNewTodoOwner] = useState('');\n  const [search, setSearch] = useState('');\n  const [filterProject, setFilterProject] = useState('');\n  const [collapsedProjects, setCollapsedProjects] = useState({});\n  const toggleTodo = id => {\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const deleteTodo = id => {\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n  const startEditingTodo = todo => {\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n    setEditingProject(todo.project || '');\n    setEditingOwner(todo.owner || '');\n  };\n  const saveEditedTodo = () => {\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => todo.id === editingTodo ? {\n        ...todo,\n        text: editingText.trim(),\n        project: editingProject.trim() || undefined,\n        owner: editingOwner.trim() || undefined\n      } : todo));\n      cancelEditing();\n    }\n  };\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n    setEditingProject('');\n    setEditingOwner('');\n  };\n  const addNewTodo = () => {\n    if (newTodoText.trim()) {\n      const newTodo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority,\n        project: newTodoProject.trim() || undefined,\n        owner: newTodoOwner.trim() || undefined\n      };\n      setTodos(prev => [...prev, newTodo]);\n      cancelNewTodo();\n    }\n  };\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n    setNewTodoProject('');\n    setNewTodoOwner('');\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-400';\n      case 'medium':\n        return 'text-yellow-400';\n      case 'low':\n        return 'text-green-400';\n      default:\n        return 'text-white/60';\n    }\n  };\n  const getPriorityLabel = priority => {\n    switch (priority) {\n      case 'high':\n        return 'Aukštas';\n      case 'medium':\n        return 'Vidutinis';\n      case 'low':\n        return 'Žemas';\n      default:\n        return 'Nežinomas';\n    }\n  };\n\n  // Gaunam unikalų projektų sąrašą\n  const uniqueProjects = useMemo(() => {\n    const projects = todos.map(todo => todo.project).filter((project, index, array) => project && array.indexOf(project) === index).sort();\n    return projects;\n  }, [todos]);\n\n  // Filtruotos ir ieškomos užduotys\n  const filteredTodos = useMemo(() => {\n    return todos.filter(todo => {\n      const matchesSearch = search === '' || todo.text.toLowerCase().includes(search.toLowerCase()) || todo.owner && todo.owner.toLowerCase().includes(search.toLowerCase()) || todo.project && todo.project.toLowerCase().includes(search.toLowerCase());\n      const matchesProject = filterProject === '' || todo.project === filterProject;\n      return matchesSearch && matchesProject;\n    });\n  }, [todos, search, filterProject]);\n\n  // Grupavimas pagal projektą\n  const groupedTodos = useMemo(() => {\n    return filteredTodos.reduce((groups, todo) => {\n      const key = todo.project || 'Kiti projektai';\n      if (!groups[key]) groups[key] = [];\n      groups[key].push(todo);\n      return groups;\n    }, {});\n  }, [filteredTodos]);\n\n  // Statistika\n  const stats = useMemo(() => {\n    const projectCount = Object.keys(groupedTodos).length;\n    const totalCount = filteredTodos.length;\n    const doneCount = filteredTodos.filter(t => t.completed).length;\n    const undoneCount = totalCount - doneCount;\n    const progress = totalCount > 0 ? Math.round(doneCount / totalCount * 100) : 0;\n    return {\n      projectCount,\n      totalCount,\n      doneCount,\n      undoneCount,\n      progress\n    };\n  }, [filteredTodos, groupedTodos]);\n\n  // Collapse/expand projektų grupes\n  const toggleCollapse = project => {\n    setCollapsedProjects(prev => ({\n      ...prev,\n      [project]: !prev[project]\n    }));\n  };\n\n  // Atlikti visas užduotis projekte\n  const completeAllInProject = project => {\n    setTodos(prev => prev.map(todo => todo.project === project ? {\n      ...todo,\n      completed: true\n    } : todo));\n  };\n\n  // Klaviatūros shortcut'ai\n  const handleKeyPress = (e, action) => {\n    if (e.key === 'Enter') {\n      action();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-6 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white text-2xl font-bold\",\n            children: stats.projectCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Projekt\\u0173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white text-2xl font-bold\",\n            children: stats.totalCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"U\\u017Eduo\\u010Di\\u0173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-green-400 text-2xl font-bold\",\n            children: stats.doneCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Atlikta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-red-400 text-2xl font-bold\",\n            children: stats.undoneCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Neatlikta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col gap-2 min-w-[200px]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Progresas:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white font-semibold\",\n            children: [stats.progress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-white/20 rounded-full h-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full transition-all duration-500\",\n            style: {\n              width: `${stats.progress}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col md:flex-row gap-4 items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2 items-center w-full md:w-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative w-full md:w-64\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 pointer-events-none z-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: search,\n            onChange: e => setSearch(e.target.value),\n            placeholder: \"Ie\\u0161koti u\\u017Eduoties, asmens ar projekto...\",\n            className: \"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterProject,\n          onChange: e => setFilterProject(e.target.value),\n          className: \"bg-white/10 border border-white/20 rounded-xl px-3 py-3 text-white focus:outline-none focus:border-blue-400 min-w-[150px]\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Visi projektai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), uniqueProjects.map(project => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: project,\n            children: project\n          }, project, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowNewTodoForm(true),\n        className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), \"Prid\\u0117ti u\\u017Eduot\\u012F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: Object.keys(groupedTodos).length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-12 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/60 text-lg mb-2\",\n          children: \"U\\u017Eduo\\u010Di\\u0173 nerasta\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/40 text-sm\",\n          children: search || filterProject ? 'Pakeiskite paieškos kriterijus' : 'Pridėkite pirmą užduotį'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this) : Object.entries(groupedTodos).map(([project, projectTodos]) => {\n        const projectDone = projectTodos.filter(t => t.completed).length;\n        const projectTotal = projectTodos.length;\n        const projectProgress = projectTotal > 0 ? Math.round(projectDone / projectTotal * 100) : 0;\n        const isCollapsed = collapsedProjects[project];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-2 mt-4 border-b border-white/20 pb-2 pl-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleCollapse(project),\n                className: \"focus:outline-none hover:bg-white/10 p-1 rounded-lg transition-colors\",\n                \"aria-label\": isCollapsed ? 'Išplėsti projektą' : 'Suskleisti projektą',\n                children: isCollapsed ? /*#__PURE__*/_jsxDEV(ChevronRight, {\n                  className: \"h-5 w-5 text-white/60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                  className: \"h-5 w-5 text-white/60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white/80 text-base font-semibold\",\n                children: project\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-white/50 bg-white/10 px-2 py-1 rounded-full\",\n                children: [projectDone, \" / \", projectTotal, \" atlikta\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-32 bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-500\",\n                  style: {\n                    width: `${projectProgress}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this), projectTotal > projectDone && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => completeAllInProject(project),\n              className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md text-sm\",\n              children: \"Atlikti visas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full text-sm text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"bg-white/10\",\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"U\\u017Eduotis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"Atsakingas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"Prioritetas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"B\\u016Bsena\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"Veiksmai\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: projectTodos.map(todo => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: `border-b border-white/5 hover:bg-white/5 transition-colors ${todo.completed ? 'bg-green-500/5 text-white/70' : ''}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: `px-4 py-3 ${todo.completed ? 'line-through' : ''}`,\n                    children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingText,\n                      onChange: e => setEditingText(e.target.value),\n                      onKeyPress: e => handleKeyPress(e, saveEditedTodo),\n                      className: \"w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400\",\n                      autoFocus: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"block py-1\",\n                      children: todo.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingOwner,\n                      onChange: e => setEditingOwner(e.target.value),\n                      onKeyPress: e => handleKeyPress(e, saveEditedTodo),\n                      className: \"w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-white/80\",\n                      children: todo.owner || '—'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-white/60\",\n                      children: todo.date || '—'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`,\n                      children: getPriorityLabel(todo.priority)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleTodo(todo.id),\n                      className: `relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${todo.completed ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg focus:ring-green-400' : 'bg-white/20 focus:ring-white/40'}`,\n                      \"aria-label\": \"Pa\\u017Eym\\u0117ti kaip atlikt\\u0105\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center ${todo.completed ? 'translate-x-4 bg-green-400' : ''}`,\n                        children: todo.completed && /*#__PURE__*/_jsxDEV(Check, {\n                          className: \"h-3 w-3 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 377,\n                          columnNumber: 54\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 372,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-2\",\n                      children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: saveEditedTodo,\n                          className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white p-2 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\",\n                          title: \"I\\u0161saugoti pakeitimus\",\n                          children: /*#__PURE__*/_jsxDEV(Check, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 390,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 385,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: cancelEditing,\n                          className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\",\n                          title: \"At\\u0161aukti redagavim\\u0105\",\n                          children: /*#__PURE__*/_jsxDEV(X, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 397,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 392,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => startEditingTodo(todo),\n                          className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white p-2 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\",\n                          title: \"Redaguoti u\\u017Eduot\\u012F\",\n                          children: /*#__PURE__*/_jsxDEV(Edit, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 407,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 402,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => deleteTodo(todo.id),\n                          className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\",\n                          title: \"I\\u0161trinti u\\u017Eduot\\u012F\",\n                          children: /*#__PURE__*/_jsxDEV(Trash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 414,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 409,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 29\n                  }, this)]\n                }, todo.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 19\n          }, this)]\n        }, project, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), stats.totalCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-white/60 text-sm\",\n        children: [\"Rodoma: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white font-medium\",\n          children: stats.totalCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 21\n        }, this), \" u\\u017Eduo\\u010Di\\u0173 \\u2022 U\\u017Ebaigta: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-green-400 font-medium\",\n          children: stats.doneCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 23\n        }, this), \" \\u2022 Liko: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-400 font-medium\",\n          children: stats.undoneCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 19\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 9\n    }, this), showNewTodoForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white mb-4\",\n          children: \"Prid\\u0117ti nauj\\u0105 u\\u017Eduot\\u012F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-white/70 mb-2\",\n              children: \"U\\u017Eduoties pavadinimas *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoText,\n              onChange: e => setNewTodoText(e.target.value),\n              onKeyPress: e => handleKeyPress(e, addNewTodo),\n              placeholder: \"\\u012Eveskite u\\u017Eduoties pavadinim\\u0105...\",\n              className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-white/70 mb-2\",\n              children: \"Atsakingas asmuo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoOwner,\n              onChange: e => setNewTodoOwner(e.target.value),\n              placeholder: \"\\u012Eveskite atsakingo asmens vard\\u0105...\",\n              className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-white/70 mb-2\",\n              children: \"Projektas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoProject,\n              onChange: e => setNewTodoProject(e.target.value),\n              placeholder: \"\\u012Eveskite projekto pavadinim\\u0105...\",\n              className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n              list: \"existing-projects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"datalist\", {\n              id: \"existing-projects\",\n              children: uniqueProjects.map(project => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: project\n              }, project, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-white/70 mb-2\",\n                children: \"Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: newTodoDate,\n                onChange: e => setNewTodoDate(e.target.value),\n                className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-white/70 mb-2\",\n                children: \"Prioritetas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newTodoPriority,\n                onChange: e => setNewTodoPriority(e.target.value),\n                className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"low\",\n                  children: \"\\u017Demas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"medium\",\n                  children: \"Vidutinis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high\",\n                  children: \"Auk\\u0161tas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3 mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: cancelNewTodo,\n            className: \"flex-1 bg-white/10 text-white px-4 py-3 rounded-xl font-medium hover:bg-white/20 transition-all duration-200 border border-white/20\",\n            children: \"At\\u0161aukti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: addNewTodo,\n            disabled: !newTodoText.trim(),\n            className: \"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: \"Prid\\u0117ti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(TodosPage, \"ft9RMlYBEXBUotah2qK6YVT8jro=\");\n_c = TodosPage;\nexport default TodosPage;\nvar _c;\n$RefreshReg$(_c, \"TodosPage\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "Plus", "Edit", "Trash2", "X", "Check", "Search", "ChevronDown", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TodosPage", "_s", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "date", "priority", "project", "owner", "editingTodo", "setEditingTodo", "editingText", "setEditingText", "editingProject", "setEditingProject", "<PERSON><PERSON><PERSON><PERSON>", "setEditingOwner", "newTodoText", "setNewTodoText", "showNewTodoForm", "setShowNewTodoForm", "newTodoDate", "setNewTodoDate", "newTodoPriority", "setNewTodoPriority", "newTodoProject", "setNewTodoProject", "newTodoOwner", "setNewTodoOwner", "search", "setSearch", "filterProject", "setFilterProject", "collapsedProjects", "setCollapsedProjects", "toggleTodo", "prev", "map", "todo", "deleteTodo", "filter", "startEditingTodo", "saveEditedTodo", "trim", "undefined", "cancelEditing", "addNewTodo", "newTodo", "Date", "now", "cancelNewTodo", "getPriorityColor", "getPriorityLabel", "uniqueProjects", "projects", "index", "array", "indexOf", "sort", "filteredTodos", "matchesSearch", "toLowerCase", "includes", "matchesProject", "groupedTodos", "reduce", "groups", "key", "push", "stats", "projectCount", "Object", "keys", "length", "totalCount", "doneCount", "t", "undoneCount", "progress", "Math", "round", "toggleCollapse", "completeAllInProject", "handleKeyPress", "e", "action", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "type", "value", "onChange", "target", "placeholder", "onClick", "entries", "projectTodos", "projectDone", "projectTotal", "projectProgress", "isCollapsed", "onKeyPress", "autoFocus", "title", "list", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TodosPage.tsx"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\r\nimport { Plus, Edit, Trash2, Calendar, X, Check } from 'lucide-react';\r\nimport { Search, ChevronDown, ChevronRight } from 'lucide-react';\r\n\r\ninterface Todo {\r\n  id: number;\r\n  text: string;\r\n  completed: boolean;\r\n  date?: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n  project?: string;\r\n  owner?: string;\r\n}\r\n\r\nconst TodosPage: React.FC = () => {\r\n  const [todos, setTodos] = useState<Todo[]>([\r\n    { id: 1, text: 'Pasir<PERSON><PERSON><PERSON> prezentacijai', completed: false, date: '2024-01-20', priority: 'high', project: 'Aexn CRM analitika', owner: '<PERSON>' },\r\n    { id: 2, text: 'Susiti<PERSON><PERSON> su klientu', completed: true, date: '2024-01-18', priority: 'medium', project: 'Aexn | Analitika', owner: '<PERSON><PERSON>' },\r\n    { id: 3, text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokument<PERSON>', completed: false, date: '2024-01-22', priority: 'low', project: 'Aexn_analitika', owner: '<PERSON><PERSON>' },\r\n    { id: 4, text: 'Planuoti kitą savaitę', completed: false, priority: 'medium', project: 'Aexn_analitika', owner: '<PERSON> <PERSON>aitis' }\r\n  ]);\r\n  const [editingTodo, setEditingTodo] = useState<number | null>(null);\r\n  const [editingText, setEditingText] = useState('');\r\n  const [editingProject, setEditingProject] = useState('');\r\n  const [editingOwner, setEditingOwner] = useState('');\r\n  const [newTodoText, setNewTodoText] = useState('');\r\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\r\n  const [newTodoDate, setNewTodoDate] = useState('');\r\n  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');\r\n  const [newTodoProject, setNewTodoProject] = useState('');\r\n  const [newTodoOwner, setNewTodoOwner] = useState('');\r\n  const [search, setSearch] = useState('');\r\n  const [filterProject, setFilterProject] = useState('');\r\n  const [collapsedProjects, setCollapsedProjects] = useState<Record<string, boolean>>({});\r\n\r\n  const toggleTodo = (id: number) => {\r\n    setTodos(prev => prev.map(todo => \r\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\r\n    ));\r\n  };\r\n\r\n  const deleteTodo = (id: number) => {\r\n    setTodos(prev => prev.filter(todo => todo.id !== id));\r\n  };\r\n\r\n  const startEditingTodo = (todo: Todo) => {\r\n    setEditingTodo(todo.id);\r\n    setEditingText(todo.text);\r\n    setEditingProject(todo.project || '');\r\n    setEditingOwner(todo.owner || '');\r\n  };\r\n\r\n  const saveEditedTodo = () => {\r\n    if (editingTodo && editingText.trim()) {\r\n      setTodos(prev => prev.map(todo => \r\n        todo.id === editingTodo ? { \r\n          ...todo, \r\n          text: editingText.trim(), \r\n          project: editingProject.trim() || undefined, \r\n          owner: editingOwner.trim() || undefined \r\n        } : todo\r\n      ));\r\n      cancelEditing();\r\n    }\r\n  };\r\n\r\n  const cancelEditing = () => {\r\n    setEditingTodo(null);\r\n    setEditingText('');\r\n    setEditingProject('');\r\n    setEditingOwner('');\r\n  };\r\n\r\n  const addNewTodo = () => {\r\n    if (newTodoText.trim()) {\r\n      const newTodo: Todo = {\r\n        id: Date.now(),\r\n        text: newTodoText.trim(),\r\n        completed: false,\r\n        date: newTodoDate || undefined,\r\n        priority: newTodoPriority,\r\n        project: newTodoProject.trim() || undefined,\r\n        owner: newTodoOwner.trim() || undefined\r\n      };\r\n      setTodos(prev => [...prev, newTodo]);\r\n      cancelNewTodo();\r\n    }\r\n  };\r\n\r\n  const cancelNewTodo = () => {\r\n    setShowNewTodoForm(false);\r\n    setNewTodoText('');\r\n    setNewTodoDate('');\r\n    setNewTodoPriority('medium');\r\n    setNewTodoProject('');\r\n    setNewTodoOwner('');\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'text-red-400';\r\n      case 'medium': return 'text-yellow-400';\r\n      case 'low': return 'text-green-400';\r\n      default: return 'text-white/60';\r\n    }\r\n  };\r\n\r\n  const getPriorityLabel = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'Aukštas';\r\n      case 'medium': return 'Vidutinis';\r\n      case 'low': return 'Žemas';\r\n      default: return 'Nežinomas';\r\n    }\r\n  };\r\n\r\n  // Gaunam unikalų projektų sąrašą\r\n  const uniqueProjects = useMemo(() => {\r\n    const projects = todos\r\n      .map(todo => todo.project)\r\n      .filter((project, index, array) => project && array.indexOf(project) === index)\r\n      .sort();\r\n    return projects as string[];\r\n  }, [todos]);\r\n\r\n  // Filtruotos ir ieškomos užduotys\r\n  const filteredTodos = useMemo(() => {\r\n    return todos.filter(todo => {\r\n      const matchesSearch = search === '' || \r\n        todo.text.toLowerCase().includes(search.toLowerCase()) ||\r\n        (todo.owner && todo.owner.toLowerCase().includes(search.toLowerCase())) ||\r\n        (todo.project && todo.project.toLowerCase().includes(search.toLowerCase()));\r\n      \r\n      const matchesProject = filterProject === '' || todo.project === filterProject;\r\n      \r\n      return matchesSearch && matchesProject;\r\n    });\r\n  }, [todos, search, filterProject]);\r\n\r\n  // Grupavimas pagal projektą\r\n  const groupedTodos = useMemo(() => {\r\n    return filteredTodos.reduce((groups, todo) => {\r\n      const key = todo.project || 'Kiti projektai';\r\n      if (!groups[key]) groups[key] = [];\r\n      groups[key].push(todo);\r\n      return groups;\r\n    }, {} as Record<string, Todo[]>);\r\n  }, [filteredTodos]);\r\n\r\n  // Statistika\r\n  const stats = useMemo(() => {\r\n    const projectCount = Object.keys(groupedTodos).length;\r\n    const totalCount = filteredTodos.length;\r\n    const doneCount = filteredTodos.filter(t => t.completed).length;\r\n    const undoneCount = totalCount - doneCount;\r\n    const progress = totalCount > 0 ? Math.round((doneCount / totalCount) * 100) : 0;\r\n    \r\n    return { projectCount, totalCount, doneCount, undoneCount, progress };\r\n  }, [filteredTodos, groupedTodos]);\r\n\r\n  // Collapse/expand projektų grupes\r\n  const toggleCollapse = (project: string) => {\r\n    setCollapsedProjects(prev => ({ ...prev, [project]: !prev[project] }));\r\n  };\r\n\r\n  // Atlikti visas užduotis projekte\r\n  const completeAllInProject = (project: string) => {\r\n    setTodos(prev => prev.map(todo => \r\n      todo.project === project ? { ...todo, completed: true } : todo\r\n    ));\r\n  };\r\n\r\n  // Klaviatūros shortcut'ai\r\n  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {\r\n    if (e.key === 'Enter') {\r\n      action();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Apžvalgos panelė */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6\">\r\n        <div className=\"flex flex-wrap gap-6 items-center\">\r\n          <div>\r\n            <div className=\"text-white text-2xl font-bold\">{stats.projectCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Projektų</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-white text-2xl font-bold\">{stats.totalCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Užduočių</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-green-400 text-2xl font-bold\">{stats.doneCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Atlikta</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-red-400 text-2xl font-bold\">{stats.undoneCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Neatlikta</div>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex-1 flex flex-col gap-2 min-w-[200px]\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-white/60 text-sm\">Progresas:</span>\r\n            <span className=\"text-white font-semibold\">{stats.progress}%</span>\r\n          </div>\r\n          <div className=\"w-full bg-white/20 rounded-full h-3\">\r\n            <div \r\n              className=\"bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full transition-all duration-500\" \r\n              style={{ width: `${stats.progress}%` }}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filtravimas ir paieška */}\r\n      <div className=\"flex flex-col md:flex-row gap-4 items-center justify-between\">\r\n        <div className=\"flex gap-2 items-center w-full md:w-auto\">\r\n          {/* Paieškos laukas su ikona */}\r\n          <div className=\"relative w-full md:w-64\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 pointer-events-none z-10\" />\r\n            <input\r\n              type=\"text\"\r\n              value={search}\r\n              onChange={e => setSearch(e.target.value)}\r\n              placeholder=\"Ieškoti užduoties, asmens ar projekto...\"\r\n              className=\"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n            />\r\n          </div>\r\n          \r\n          {/* Projektų filtras */}\r\n          <select\r\n            value={filterProject}\r\n            onChange={e => setFilterProject(e.target.value)}\r\n            className=\"bg-white/10 border border-white/20 rounded-xl px-3 py-3 text-white focus:outline-none focus:border-blue-400 min-w-[150px]\"\r\n          >\r\n            <option value=\"\">Visi projektai</option>\r\n            {uniqueProjects.map(project => (\r\n              <option key={project} value={project}>{project}</option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n        \r\n        {/* Pridėti užduotį mygtukas */}\r\n        <button\r\n          onClick={() => setShowNewTodoForm(true)}\r\n          className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md\"\r\n        >\r\n          <Plus className=\"h-4 w-4\" />\r\n          Pridėti užduotį\r\n        </button>\r\n      </div>\r\n\r\n      {/* Užduočių grupės */}\r\n      <div className=\"overflow-x-auto\">\r\n        {Object.keys(groupedTodos).length === 0 ? (\r\n          <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-12 text-center\">\r\n            <div className=\"text-white/60 text-lg mb-2\">Užduočių nerasta</div>\r\n            <div className=\"text-white/40 text-sm\">\r\n              {search || filterProject ? 'Pakeiskite paieškos kriterijus' : 'Pridėkite pirmą užduotį'}\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          Object.entries(groupedTodos).map(([project, projectTodos]) => {\r\n            const projectDone = projectTodos.filter(t => t.completed).length;\r\n            const projectTotal = projectTodos.length;\r\n            const projectProgress = projectTotal > 0 ? Math.round((projectDone / projectTotal) * 100) : 0;\r\n            const isCollapsed = collapsedProjects[project];\r\n\r\n            return (\r\n              <div key={project} className=\"mb-8\">\r\n                <div className=\"flex items-center justify-between mb-2 mt-4 border-b border-white/20 pb-2 pl-1\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <button \r\n                      onClick={() => toggleCollapse(project)} \r\n                      className=\"focus:outline-none hover:bg-white/10 p-1 rounded-lg transition-colors\"\r\n                      aria-label={isCollapsed ? 'Išplėsti projektą' : 'Suskleisti projektą'}\r\n                    >\r\n                      {isCollapsed ? \r\n                        <ChevronRight className=\"h-5 w-5 text-white/60\" /> : \r\n                        <ChevronDown className=\"h-5 w-5 text-white/60\" />\r\n                      }\r\n                    </button>\r\n                    <span className=\"text-white/80 text-base font-semibold\">{project}</span>\r\n                    <span className=\"text-xs text-white/50 bg-white/10 px-2 py-1 rounded-full\">\r\n                      {projectDone} / {projectTotal} atlikta\r\n                    </span>\r\n                    <div className=\"w-32 bg-white/20 rounded-full h-2\">\r\n                      <div \r\n                        className=\"bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-500\" \r\n                        style={{ width: `${projectProgress}%` }}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {projectTotal > projectDone && (\r\n                    <button\r\n                      onClick={() => completeAllInProject(project)}\r\n                      className=\"bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md text-sm\"\r\n                    >\r\n                      Atlikti visas\r\n                    </button>\r\n                  )}\r\n                </div>\r\n\r\n                {!isCollapsed && (\r\n                  <div className=\"bg-white/5 rounded-xl overflow-hidden\">\r\n                    <table className=\"min-w-full text-sm text-white\">\r\n                      <thead>\r\n                        <tr className=\"bg-white/10\">\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Užduotis</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Atsakingas</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Data</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Prioritetas</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Būsena</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Veiksmai</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {projectTodos.map((todo) => (\r\n                          <tr\r\n                            key={todo.id}\r\n                            className={`border-b border-white/5 hover:bg-white/5 transition-colors ${\r\n                              todo.completed ? 'bg-green-500/5 text-white/70' : ''\r\n                            }`}\r\n                          >\r\n                            <td className={`px-4 py-3 ${todo.completed ? 'line-through' : ''}`}>\r\n                              {editingTodo === todo.id ? (\r\n                                <input\r\n                                  type=\"text\"\r\n                                  value={editingText}\r\n                                  onChange={e => setEditingText(e.target.value)}\r\n                                  onKeyPress={e => handleKeyPress(e, saveEditedTodo)}\r\n                                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                                  autoFocus\r\n                                />\r\n                              ) : (\r\n                                <span className=\"block py-1\">{todo.text}</span>\r\n                              )}\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              {editingTodo === todo.id ? (\r\n                                <input\r\n                                  type=\"text\"\r\n                                  value={editingOwner}\r\n                                  onChange={e => setEditingOwner(e.target.value)}\r\n                                  onKeyPress={e => handleKeyPress(e, saveEditedTodo)}\r\n                                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                                />\r\n                              ) : (\r\n                                <span className=\"text-white/80\">{todo.owner || '—'}</span>\r\n                              )}\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              <span className=\"text-white/60\">{todo.date || '—'}</span>\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`}>\r\n                                {getPriorityLabel(todo.priority)}\r\n                              </span>\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              <button\r\n                                onClick={() => toggleTodo(todo.id)}\r\n                                className={`relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${\r\n                                  todo.completed \r\n                                    ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg focus:ring-green-400' \r\n                                    : 'bg-white/20 focus:ring-white/40'\r\n                                }`}\r\n                                aria-label=\"Pažymėti kaip atliktą\"\r\n                              >\r\n                                <span\r\n                                  className={`absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center ${\r\n                                    todo.completed ? 'translate-x-4 bg-green-400' : ''\r\n                                  }`}\r\n                                >\r\n                                  {todo.completed && <Check className=\"h-3 w-3 text-white\" />}\r\n                                </span>\r\n                              </button>\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              <div className=\"flex gap-2\">\r\n                                {editingTodo === todo.id ? (\r\n                                  <>\r\n                                    <button \r\n                                      onClick={saveEditedTodo} \r\n                                      className=\"bg-gradient-to-r from-green-500 to-emerald-500 text-white p-2 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\"\r\n                                      title=\"Išsaugoti pakeitimus\"\r\n                                    >\r\n                                      <Check className=\"h-4 w-4\" />\r\n                                    </button>\r\n                                    <button \r\n                                      onClick={cancelEditing} \r\n                                      className=\"bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\"\r\n                                      title=\"Atšaukti redagavimą\"\r\n                                    >\r\n                                      <X className=\"h-4 w-4\" />\r\n                                    </button>\r\n                                  </>\r\n                                ) : (\r\n                                  <>\r\n                                    <button \r\n                                      onClick={() => startEditingTodo(todo)} \r\n                                      className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white p-2 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\"\r\n                                      title=\"Redaguoti užduotį\"\r\n                                    >\r\n                                      <Edit className=\"h-4 w-4\" />\r\n                                    </button>\r\n                                    <button \r\n                                      onClick={() => deleteTodo(todo.id)} \r\n                                      className=\"bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\"\r\n                                      title=\"Ištrinti užduotį\"\r\n                                    >\r\n                                      <Trash2 className=\"h-4 w-4\" />\r\n                                    </button>\r\n                                  </>\r\n                                )}\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })\r\n        )}\r\n      </div>\r\n\r\n      {/* Suvestinė */}\r\n      {stats.totalCount > 0 && (\r\n        <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n          <div className=\"text-white/60 text-sm\">\r\n            Rodoma: <span className=\"text-white font-medium\">{stats.totalCount}</span> užduočių • \r\n            Užbaigta: <span className=\"text-green-400 font-medium\">{stats.doneCount}</span> • \r\n            Liko: <span className=\"text-red-400 font-medium\">{stats.undoneCount}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Nauja užduoties forma */}\r\n      {showNewTodoForm && (\r\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\">\r\n          <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 w-full max-w-md mx-4\">\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Pridėti naują užduotį</h3>\r\n            \r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-white/70 mb-2\">Užduoties pavadinimas *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoText}\r\n                  onChange={e => setNewTodoText(e.target.value)}\r\n                  onKeyPress={e => handleKeyPress(e, addNewTodo)}\r\n                  placeholder=\"Įveskite užduoties pavadinimą...\"\r\n                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                  autoFocus\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-white/70 mb-2\">Atsakingas asmuo</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoOwner}\r\n                  onChange={e => setNewTodoOwner(e.target.value)}\r\n                  placeholder=\"Įveskite atsakingo asmens vardą...\"\r\n                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-white/70 mb-2\">Projektas</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoProject}\r\n                  onChange={e => setNewTodoProject(e.target.value)}\r\n                  placeholder=\"Įveskite projekto pavadinimą...\"\r\n                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                  list=\"existing-projects\"\r\n                />\r\n                <datalist id=\"existing-projects\">\r\n                  {uniqueProjects.map(project => (\r\n                    <option key={project} value={project} />\r\n                  ))}\r\n                </datalist>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-white/70 mb-2\">Data</label>\r\n                  <input\r\n                    type=\"date\"\r\n                    value={newTodoDate}\r\n                    onChange={e => setNewTodoDate(e.target.value)}\r\n                    className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-white/70 mb-2\">Prioritetas</label>\r\n                  <select\r\n                    value={newTodoPriority}\r\n                    onChange={e => setNewTodoPriority(e.target.value as 'low' | 'medium' | 'high')}\r\n                    className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                  >\r\n                    <option value=\"low\">Žemas</option>\r\n                    <option value=\"medium\">Vidutinis</option>\r\n                    <option value=\"high\">Aukštas</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex gap-3 mt-6\">\r\n              <button\r\n                onClick={cancelNewTodo}\r\n                className=\"flex-1 bg-white/10 text-white px-4 py-3 rounded-xl font-medium hover:bg-white/20 transition-all duration-200 border border-white/20\"\r\n              >\r\n                Atšaukti\r\n              </button>\r\n              <button\r\n                onClick={addNewTodo}\r\n                disabled={!newTodoText.trim()}\r\n                className=\"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Pridėti\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TodosPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAYC,CAAC,EAAEC,KAAK,QAAQ,cAAc;AACrE,SAASC,MAAM,EAAEC,WAAW,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAYjE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAS,CACzC;IAAEkB,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,MAAM;IAAEC,OAAO,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC3J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC1J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,KAAK;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAe,CAAC,EACjJ;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEE,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACnI,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAA4B,QAAQ,CAAC;EAC3F,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAA0B,CAAC,CAAC,CAAC;EAEvF,MAAMmD,UAAU,GAAIjC,EAAU,IAAK;IACjCD,QAAQ,CAACmC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACpC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGoC,IAAI;MAAElC,SAAS,EAAE,CAACkC,IAAI,CAAClC;IAAU,CAAC,GAAGkC,IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIrC,EAAU,IAAK;IACjCD,QAAQ,CAACmC,IAAI,IAAIA,IAAI,CAACI,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACpC,EAAE,KAAKA,EAAE,CAAC,CAAC;EACvD,CAAC;EAED,MAAMuC,gBAAgB,GAAIH,IAAU,IAAK;IACvC5B,cAAc,CAAC4B,IAAI,CAACpC,EAAE,CAAC;IACvBU,cAAc,CAAC0B,IAAI,CAACnC,IAAI,CAAC;IACzBW,iBAAiB,CAACwB,IAAI,CAAC/B,OAAO,IAAI,EAAE,CAAC;IACrCS,eAAe,CAACsB,IAAI,CAAC9B,KAAK,IAAI,EAAE,CAAC;EACnC,CAAC;EAED,MAAMkC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIjC,WAAW,IAAIE,WAAW,CAACgC,IAAI,CAAC,CAAC,EAAE;MACrC1C,QAAQ,CAACmC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACpC,EAAE,KAAKO,WAAW,GAAG;QACxB,GAAG6B,IAAI;QACPnC,IAAI,EAAEQ,WAAW,CAACgC,IAAI,CAAC,CAAC;QACxBpC,OAAO,EAAEM,cAAc,CAAC8B,IAAI,CAAC,CAAC,IAAIC,SAAS;QAC3CpC,KAAK,EAAEO,YAAY,CAAC4B,IAAI,CAAC,CAAC,IAAIC;MAChC,CAAC,GAAGN,IACN,CAAC,CAAC;MACFO,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1BnC,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;IAClBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAM8B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI7B,WAAW,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACtB,MAAMI,OAAa,GAAG;QACpB7C,EAAE,EAAE8C,IAAI,CAACC,GAAG,CAAC,CAAC;QACd9C,IAAI,EAAEc,WAAW,CAAC0B,IAAI,CAAC,CAAC;QACxBvC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAEgB,WAAW,IAAIuB,SAAS;QAC9BtC,QAAQ,EAAEiB,eAAe;QACzBhB,OAAO,EAAEkB,cAAc,CAACkB,IAAI,CAAC,CAAC,IAAIC,SAAS;QAC3CpC,KAAK,EAAEmB,YAAY,CAACgB,IAAI,CAAC,CAAC,IAAIC;MAChC,CAAC;MACD3C,QAAQ,CAACmC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEW,OAAO,CAAC,CAAC;MACpCG,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1B9B,kBAAkB,CAAC,KAAK,CAAC;IACzBF,cAAc,CAAC,EAAE,CAAC;IAClBI,cAAc,CAAC,EAAE,CAAC;IAClBE,kBAAkB,CAAC,QAAQ,CAAC;IAC5BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAMuB,gBAAgB,GAAI7C,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAM8C,gBAAgB,GAAI9C,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,WAAW;MACjC,KAAK,KAAK;QAAE,OAAO,OAAO;MAC1B;QAAS,OAAO,WAAW;IAC7B;EACF,CAAC;;EAED;EACA,MAAM+C,cAAc,GAAGpE,OAAO,CAAC,MAAM;IACnC,MAAMqE,QAAQ,GAAGtD,KAAK,CACnBqC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC/B,OAAO,CAAC,CACzBiC,MAAM,CAAC,CAACjC,OAAO,EAAEgD,KAAK,EAAEC,KAAK,KAAKjD,OAAO,IAAIiD,KAAK,CAACC,OAAO,CAAClD,OAAO,CAAC,KAAKgD,KAAK,CAAC,CAC9EG,IAAI,CAAC,CAAC;IACT,OAAOJ,QAAQ;EACjB,CAAC,EAAE,CAACtD,KAAK,CAAC,CAAC;;EAEX;EACA,MAAM2D,aAAa,GAAG1E,OAAO,CAAC,MAAM;IAClC,OAAOe,KAAK,CAACwC,MAAM,CAACF,IAAI,IAAI;MAC1B,MAAMsB,aAAa,GAAG/B,MAAM,KAAK,EAAE,IACjCS,IAAI,CAACnC,IAAI,CAAC0D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,MAAM,CAACgC,WAAW,CAAC,CAAC,CAAC,IACrDvB,IAAI,CAAC9B,KAAK,IAAI8B,IAAI,CAAC9B,KAAK,CAACqD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,MAAM,CAACgC,WAAW,CAAC,CAAC,CAAE,IACtEvB,IAAI,CAAC/B,OAAO,IAAI+B,IAAI,CAAC/B,OAAO,CAACsD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,MAAM,CAACgC,WAAW,CAAC,CAAC,CAAE;MAE7E,MAAME,cAAc,GAAGhC,aAAa,KAAK,EAAE,IAAIO,IAAI,CAAC/B,OAAO,KAAKwB,aAAa;MAE7E,OAAO6B,aAAa,IAAIG,cAAc;IACxC,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/D,KAAK,EAAE6B,MAAM,EAAEE,aAAa,CAAC,CAAC;;EAElC;EACA,MAAMiC,YAAY,GAAG/E,OAAO,CAAC,MAAM;IACjC,OAAO0E,aAAa,CAACM,MAAM,CAAC,CAACC,MAAM,EAAE5B,IAAI,KAAK;MAC5C,MAAM6B,GAAG,GAAG7B,IAAI,CAAC/B,OAAO,IAAI,gBAAgB;MAC5C,IAAI,CAAC2D,MAAM,CAACC,GAAG,CAAC,EAAED,MAAM,CAACC,GAAG,CAAC,GAAG,EAAE;MAClCD,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC9B,IAAI,CAAC;MACtB,OAAO4B,MAAM;IACf,CAAC,EAAE,CAAC,CAA2B,CAAC;EAClC,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMU,KAAK,GAAGpF,OAAO,CAAC,MAAM;IAC1B,MAAMqF,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACR,YAAY,CAAC,CAACS,MAAM;IACrD,MAAMC,UAAU,GAAGf,aAAa,CAACc,MAAM;IACvC,MAAME,SAAS,GAAGhB,aAAa,CAACnB,MAAM,CAACoC,CAAC,IAAIA,CAAC,CAACxE,SAAS,CAAC,CAACqE,MAAM;IAC/D,MAAMI,WAAW,GAAGH,UAAU,GAAGC,SAAS;IAC1C,MAAMG,QAAQ,GAAGJ,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAEL,SAAS,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IAEhF,OAAO;MAAEJ,YAAY;MAAEI,UAAU;MAAEC,SAAS;MAAEE,WAAW;MAAEC;IAAS,CAAC;EACvE,CAAC,EAAE,CAACnB,aAAa,EAAEK,YAAY,CAAC,CAAC;;EAEjC;EACA,MAAMiB,cAAc,GAAI1E,OAAe,IAAK;IAC1C2B,oBAAoB,CAACE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC7B,OAAO,GAAG,CAAC6B,IAAI,CAAC7B,OAAO;IAAE,CAAC,CAAC,CAAC;EACxE,CAAC;;EAED;EACA,MAAM2E,oBAAoB,GAAI3E,OAAe,IAAK;IAChDN,QAAQ,CAACmC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAAC/B,OAAO,KAAKA,OAAO,GAAG;MAAE,GAAG+B,IAAI;MAAElC,SAAS,EAAE;IAAK,CAAC,GAAGkC,IAC5D,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM6C,cAAc,GAAGA,CAACC,CAAsB,EAAEC,MAAkB,KAAK;IACrE,IAAID,CAAC,CAACjB,GAAG,KAAK,OAAO,EAAE;MACrBkB,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EAED,oBACE1F,OAAA;IAAK2F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5F,OAAA;MAAK2F,SAAS,EAAC,wIAAwI;MAAAC,QAAA,gBACrJ5F,OAAA;QAAK2F,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5F,OAAA;UAAA4F,QAAA,gBACE5F,OAAA;YAAK2F,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAElB,KAAK,CAACC;UAAY;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEhG,OAAA;YAAK2F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNhG,OAAA;UAAA4F,QAAA,gBACE5F,OAAA;YAAK2F,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAElB,KAAK,CAACK;UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvEhG,OAAA;YAAK2F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNhG,OAAA;UAAA4F,QAAA,gBACE5F,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAElB,KAAK,CAACM;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1EhG,OAAA;YAAK2F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNhG,OAAA;UAAA4F,QAAA,gBACE5F,OAAA;YAAK2F,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAElB,KAAK,CAACQ;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1EhG,OAAA;YAAK2F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhG,OAAA;QAAK2F,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvD5F,OAAA;UAAK2F,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC5F,OAAA;YAAM2F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzDhG,OAAA;YAAM2F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAElB,KAAK,CAACS,QAAQ,EAAC,GAAC;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACNhG,OAAA;UAAK2F,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClD5F,OAAA;YACE2F,SAAS,EAAC,6FAA6F;YACvGM,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAGxB,KAAK,CAACS,QAAQ;YAAI;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhG,OAAA;MAAK2F,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAC3E5F,OAAA;QAAK2F,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBAEvD5F,OAAA;UAAK2F,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC5F,OAAA,CAACJ,MAAM;YAAC+F,SAAS,EAAC;UAAmG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxHhG,OAAA;YACEmG,IAAI,EAAC,MAAM;YACXC,KAAK,EAAElE,MAAO;YACdmE,QAAQ,EAAEZ,CAAC,IAAItD,SAAS,CAACsD,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;YACzCG,WAAW,EAAC,oDAA0C;YACtDZ,SAAS,EAAC;UAAqL;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhG,OAAA;UACEoG,KAAK,EAAEhE,aAAc;UACrBiE,QAAQ,EAAEZ,CAAC,IAAIpD,gBAAgB,CAACoD,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;UAChDT,SAAS,EAAC,2HAA2H;UAAAC,QAAA,gBAErI5F,OAAA;YAAQoG,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACvCtC,cAAc,CAAChB,GAAG,CAAC9B,OAAO,iBACzBZ,OAAA;YAAsBoG,KAAK,EAAExF,OAAQ;YAAAgF,QAAA,EAAEhF;UAAO,GAAjCA,OAAO;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmC,CACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNhG,OAAA;QACEwG,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAAC,IAAI,CAAE;QACxCkE,SAAS,EAAC,gNAAgN;QAAAC,QAAA,gBAE1N5F,OAAA,CAACT,IAAI;UAACoG,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAE9B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhG,OAAA;MAAK2F,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BhB,MAAM,CAACC,IAAI,CAACR,YAAY,CAAC,CAACS,MAAM,KAAK,CAAC,gBACrC9E,OAAA;QAAK2F,SAAS,EAAC,kFAAkF;QAAAC,QAAA,gBAC/F5F,OAAA;UAAK2F,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClEhG,OAAA;UAAK2F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACnC1D,MAAM,IAAIE,aAAa,GAAG,gCAAgC,GAAG;QAAyB;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GAENpB,MAAM,CAAC6B,OAAO,CAACpC,YAAY,CAAC,CAAC3B,GAAG,CAAC,CAAC,CAAC9B,OAAO,EAAE8F,YAAY,CAAC,KAAK;QAC5D,MAAMC,WAAW,GAAGD,YAAY,CAAC7D,MAAM,CAACoC,CAAC,IAAIA,CAAC,CAACxE,SAAS,CAAC,CAACqE,MAAM;QAChE,MAAM8B,YAAY,GAAGF,YAAY,CAAC5B,MAAM;QACxC,MAAM+B,eAAe,GAAGD,YAAY,GAAG,CAAC,GAAGxB,IAAI,CAACC,KAAK,CAAEsB,WAAW,GAAGC,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC;QAC7F,MAAME,WAAW,GAAGxE,iBAAiB,CAAC1B,OAAO,CAAC;QAE9C,oBACEZ,OAAA;UAAmB2F,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjC5F,OAAA;YAAK2F,SAAS,EAAC,gFAAgF;YAAAC,QAAA,gBAC7F5F,OAAA;cAAK2F,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC5F,OAAA;gBACEwG,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAAC1E,OAAO,CAAE;gBACvC+E,SAAS,EAAC,uEAAuE;gBACjF,cAAYmB,WAAW,GAAG,mBAAmB,GAAG,qBAAsB;gBAAAlB,QAAA,EAErEkB,WAAW,gBACV9G,OAAA,CAACF,YAAY;kBAAC6F,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAClDhG,OAAA,CAACH,WAAW;kBAAC8F,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE7C,CAAC,eACThG,OAAA;gBAAM2F,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEhF;cAAO;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxEhG,OAAA;gBAAM2F,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,GACvEe,WAAW,EAAC,KAAG,EAACC,YAAY,EAAC,UAChC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPhG,OAAA;gBAAK2F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChD5F,OAAA;kBACE2F,SAAS,EAAC,6FAA6F;kBACvGM,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGW,eAAe;kBAAI;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELY,YAAY,GAAGD,WAAW,iBACzB3G,OAAA;cACEwG,OAAO,EAAEA,CAAA,KAAMjB,oBAAoB,CAAC3E,OAAO,CAAE;cAC7C+E,SAAS,EAAC,oMAAoM;cAAAC,QAAA,EAC/M;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL,CAACc,WAAW,iBACX9G,OAAA;YAAK2F,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD5F,OAAA;cAAO2F,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC9C5F,OAAA;gBAAA4F,QAAA,eACE5F,OAAA;kBAAI2F,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACzB5F,OAAA;oBAAI2F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7DhG,OAAA;oBAAI2F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/DhG,OAAA;oBAAI2F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzDhG,OAAA;oBAAI2F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChEhG,OAAA;oBAAI2F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DhG,OAAA;oBAAI2F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRhG,OAAA;gBAAA4F,QAAA,EACGc,YAAY,CAAChE,GAAG,CAAEC,IAAI,iBACrB3C,OAAA;kBAEE2F,SAAS,EAAE,8DACThD,IAAI,CAAClC,SAAS,GAAG,8BAA8B,GAAG,EAAE,EACnD;kBAAAmF,QAAA,gBAEH5F,OAAA;oBAAI2F,SAAS,EAAE,aAAahD,IAAI,CAAClC,SAAS,GAAG,cAAc,GAAG,EAAE,EAAG;oBAAAmF,QAAA,EAChE9E,WAAW,KAAK6B,IAAI,CAACpC,EAAE,gBACtBP,OAAA;sBACEmG,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAEpF,WAAY;sBACnBqF,QAAQ,EAAEZ,CAAC,IAAIxE,cAAc,CAACwE,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;sBAC9CW,UAAU,EAAEtB,CAAC,IAAID,cAAc,CAACC,CAAC,EAAE1C,cAAc,CAAE;sBACnD4C,SAAS,EAAC,oHAAoH;sBAC9HqB,SAAS;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,gBAEFhG,OAAA;sBAAM2F,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEjD,IAAI,CAACnC;oBAAI;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAC/C;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLhG,OAAA;oBAAI2F,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtB9E,WAAW,KAAK6B,IAAI,CAACpC,EAAE,gBACtBP,OAAA;sBACEmG,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAEhF,YAAa;sBACpBiF,QAAQ,EAAEZ,CAAC,IAAIpE,eAAe,CAACoE,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;sBAC/CW,UAAU,EAAEtB,CAAC,IAAID,cAAc,CAACC,CAAC,EAAE1C,cAAc,CAAE;sBACnD4C,SAAS,EAAC;oBAAoH;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/H,CAAC,gBAEFhG,OAAA;sBAAM2F,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEjD,IAAI,CAAC9B,KAAK,IAAI;oBAAG;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAC1D;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLhG,OAAA;oBAAI2F,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACvB5F,OAAA;sBAAM2F,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEjD,IAAI,CAACjC,IAAI,IAAI;oBAAG;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACLhG,OAAA;oBAAI2F,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACvB5F,OAAA;sBAAM2F,SAAS,EAAE,kCAAkCnC,gBAAgB,CAACb,IAAI,CAAChC,QAAQ,CAAC,cAAe;sBAAAiF,QAAA,EAC9FnC,gBAAgB,CAACd,IAAI,CAAChC,QAAQ;oBAAC;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLhG,OAAA;oBAAI2F,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACvB5F,OAAA;sBACEwG,OAAO,EAAEA,CAAA,KAAMhE,UAAU,CAACG,IAAI,CAACpC,EAAE,CAAE;sBACnCoF,SAAS,EAAE,mJACThD,IAAI,CAAClC,SAAS,GACV,+EAA+E,GAC/E,iCAAiC,EACpC;sBACH,cAAW,sCAAuB;sBAAAmF,QAAA,eAElC5F,OAAA;wBACE2F,SAAS,EAAE,oIACThD,IAAI,CAAClC,SAAS,GAAG,4BAA4B,GAAG,EAAE,EACjD;wBAAAmF,QAAA,EAEFjD,IAAI,CAAClC,SAAS,iBAAIT,OAAA,CAACL,KAAK;0BAACgG,SAAS,EAAC;wBAAoB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACLhG,OAAA;oBAAI2F,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACvB5F,OAAA;sBAAK2F,SAAS,EAAC,YAAY;sBAAAC,QAAA,EACxB9E,WAAW,KAAK6B,IAAI,CAACpC,EAAE,gBACtBP,OAAA,CAAAE,SAAA;wBAAA0F,QAAA,gBACE5F,OAAA;0BACEwG,OAAO,EAAEzD,cAAe;0BACxB4C,SAAS,EAAC,2MAA2M;0BACrNsB,KAAK,EAAC,2BAAsB;0BAAArB,QAAA,eAE5B5F,OAAA,CAACL,KAAK;4BAACgG,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACThG,OAAA;0BACEwG,OAAO,EAAEtD,aAAc;0BACvByC,SAAS,EAAC,iMAAiM;0BAC3MsB,KAAK,EAAC,+BAAqB;0BAAArB,QAAA,eAE3B5F,OAAA,CAACN,CAAC;4BAACiG,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB,CAAC;sBAAA,eACT,CAAC,gBAEHhG,OAAA,CAAAE,SAAA;wBAAA0F,QAAA,gBACE5F,OAAA;0BACEwG,OAAO,EAAEA,CAAA,KAAM1D,gBAAgB,CAACH,IAAI,CAAE;0BACtCgD,SAAS,EAAC,uMAAuM;0BACjNsB,KAAK,EAAC,6BAAmB;0BAAArB,QAAA,eAEzB5F,OAAA,CAACR,IAAI;4BAACmG,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACThG,OAAA;0BACEwG,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAACD,IAAI,CAACpC,EAAE,CAAE;0BACnCoF,SAAS,EAAC,iMAAiM;0BAC3MsB,KAAK,EAAC,iCAAkB;0BAAArB,QAAA,eAExB5F,OAAA,CAACP,MAAM;4BAACkG,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA,eACT;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAjGArD,IAAI,CAACpC,EAAE;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkGV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA,GA1JOpF,OAAO;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2JZ,CAAC;MAEV,CAAC;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLtB,KAAK,CAACK,UAAU,GAAG,CAAC,iBACnB/E,OAAA;MAAK2F,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eAC/D5F,OAAA;QAAK2F,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC7B,eAAA5F,OAAA;UAAM2F,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAElB,KAAK,CAACK;QAAU;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,mDAChE,eAAAhG,OAAA;UAAM2F,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAElB,KAAK,CAACM;QAAS;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,kBACzE,eAAAhG,OAAA;UAAM2F,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAElB,KAAK,CAACQ;QAAW;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAxE,eAAe,iBACdxB,OAAA;MAAK2F,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC/F5F,OAAA;QAAK2F,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvG5F,OAAA;UAAI2F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhFhG,OAAA;UAAK2F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5F,OAAA;YAAA4F,QAAA,gBACE5F,OAAA;cAAO2F,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/FhG,OAAA;cACEmG,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE9E,WAAY;cACnB+E,QAAQ,EAAEZ,CAAC,IAAIlE,cAAc,CAACkE,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;cAC9CW,UAAU,EAAEtB,CAAC,IAAID,cAAc,CAACC,CAAC,EAAEtC,UAAU,CAAE;cAC/CoD,WAAW,EAAC,iDAAkC;cAC9CZ,SAAS,EAAC,gLAAgL;cAC1LqB,SAAS;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhG,OAAA;YAAA4F,QAAA,gBACE5F,OAAA;cAAO2F,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxFhG,OAAA;cACEmG,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEpE,YAAa;cACpBqE,QAAQ,EAAEZ,CAAC,IAAIxD,eAAe,CAACwD,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;cAC/CG,WAAW,EAAC,8CAAoC;cAChDZ,SAAS,EAAC;YAAgL;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3L,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhG,OAAA;YAAA4F,QAAA,gBACE5F,OAAA;cAAO2F,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjFhG,OAAA;cACEmG,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEtE,cAAe;cACtBuE,QAAQ,EAAEZ,CAAC,IAAI1D,iBAAiB,CAAC0D,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;cACjDG,WAAW,EAAC,2CAAiC;cAC7CZ,SAAS,EAAC,gLAAgL;cAC1LuB,IAAI,EAAC;YAAmB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFhG,OAAA;cAAUO,EAAE,EAAC,mBAAmB;cAAAqF,QAAA,EAC7BlC,cAAc,CAAChB,GAAG,CAAC9B,OAAO,iBACzBZ,OAAA;gBAAsBoG,KAAK,EAAExF;cAAQ,GAAxBA,OAAO;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmB,CACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC5F,OAAA;cAAA4F,QAAA,gBACE5F,OAAA;gBAAO2F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5EhG,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE1E,WAAY;gBACnB2E,QAAQ,EAAEZ,CAAC,IAAI9D,cAAc,CAAC8D,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;gBAC9CT,SAAS,EAAC;cAA2J;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhG,OAAA;cAAA4F,QAAA,gBACE5F,OAAA;gBAAO2F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFhG,OAAA;gBACEoG,KAAK,EAAExE,eAAgB;gBACvByE,QAAQ,EAAEZ,CAAC,IAAI5D,kBAAkB,CAAC4D,CAAC,CAACa,MAAM,CAACF,KAAkC,CAAE;gBAC/ET,SAAS,EAAC,2JAA2J;gBAAAC,QAAA,gBAErK5F,OAAA;kBAAQoG,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClChG,OAAA;kBAAQoG,KAAK,EAAC,QAAQ;kBAAAR,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzChG,OAAA;kBAAQoG,KAAK,EAAC,MAAM;kBAAAR,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhG,OAAA;UAAK2F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B5F,OAAA;YACEwG,OAAO,EAAEjD,aAAc;YACvBoC,SAAS,EAAC,qIAAqI;YAAAC,QAAA,EAChJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA;YACEwG,OAAO,EAAErD,UAAW;YACpBgE,QAAQ,EAAE,CAAC7F,WAAW,CAAC0B,IAAI,CAAC,CAAE;YAC9B2C,SAAS,EAAC,+OAA+O;YAAAC,QAAA,EAC1P;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5F,EAAA,CA1gBID,SAAmB;AAAAiH,EAAA,GAAnBjH,SAAmB;AA4gBzB,eAAeA,SAAS;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}