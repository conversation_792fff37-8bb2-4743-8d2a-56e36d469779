{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from 'react';\nimport { MeetingsList, ErrorBoundary, WhisperStatusIndicator, TranscriptionManager, ProfessionalTranscriptViewer, DynamicAudioVisualizer } from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { createDemoMeetings } from './utils/demoData';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List, Square, Menu, X } from 'lucide-react';\nimport './styles/background.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [meetings, setMeetings] = useState([]);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState(null);\n  const [activeView, setActiveView] = useState('recording');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const {\n    recordingState,\n    startRecording,\n    stopRecording,\n    pauseRecording,\n    resumeRecording\n  } = useAudioRecorder();\n  const {\n    transcript,\n    isTranscribing,\n    transcribeAudioEnhanced,\n    cancelTranscription,\n    editSegment,\n    clearTranscript,\n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n  const handleStartRecording = useCallback(async title => {\n    try {\n      await startRecording();\n      const newMeeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started'\n        }\n      };\n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      if (currentMeeting && audioBlob) {\n        const updatedMeeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started'\n          }\n        };\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m));\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n  const handleStartTranscription = useCallback(async meetingId => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date()\n      }\n    };\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: progress => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: {\n              ...m.transcriptionStatus,\n              progress,\n              state: 'processing'\n            }\n          } : m));\n        },\n        onStatusUpdate: status => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: status\n          } : m));\n        },\n        enhanceSpeakers: true\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date()\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n\n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence\n      });\n    } catch (error) {\n      console.error('❌ Transkribavimo klaida:', error);\n      const errorMeeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n  const handleCancelTranscription = useCallback(meetingId => {\n    cancelTranscription();\n    setMeetings(prev => prev.map(m => m.id === meetingId ? {\n      ...m,\n      transcriptionStatus: {\n        ...m.transcriptionStatus,\n        state: 'cancelled'\n      }\n    } : m));\n  }, [cancelTranscription]);\n  const handleEditSegment = useCallback((meetingId, segmentId, newText) => {\n    editSegment(segmentId, newText);\n\n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => {\n      var _meeting$transcript;\n      return meeting.id === meetingId ? {\n        ...meeting,\n        transcript: (_meeting$transcript = meeting.transcript) === null || _meeting$transcript === void 0 ? void 0 : _meeting$transcript.map(segment => segment.id === segmentId ? {\n          ...segment,\n          text: newText,\n          isEdited: true,\n          editedAt: new Date(),\n          editedBy: 'user'\n        } : segment)\n      } : meeting;\n    }));\n  }, [editSegment]);\n  const handleSelectMeeting = useCallback(meeting => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n  const handleDeleteMeeting = useCallback(meetingId => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if ((currentMeeting === null || currentMeeting === void 0 ? void 0 : currentMeeting.id) === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if ((selectedMeetingForTranscript === null || selectedMeetingForTranscript === void 0 ? void 0 : selectedMeetingForTranscript.id) === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n  const handleExportMeeting = useCallback(meeting => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus\n    };\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);\n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed',\n        progress: 100,\n        completedAt: meeting.date\n      }\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  // Close mobile menu when clicking outside or on escape\n  useEffect(() => {\n    const handleClickOutside = event => {\n      const target = event.target;\n      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n    const handleEscape = event => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n    if (isMobileMenuOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when mobile menu is open\n      document.body.classList.add('mobile-menu-open');\n    } else {\n      document.body.classList.remove('mobile-menu-open');\n    }\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('keydown', handleEscape);\n      document.body.classList.remove('mobile-menu-open');\n    };\n  }, [isMobileMenuOpen]);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen elegant-background font-inter\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"elegant-grid\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          className: \"fixed top-0 left-0 right-0 z-50 glassmorphic-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between h-16\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\",\n                  children: /*#__PURE__*/_jsxDEV(Mic2, {\n                    className: \"h-5 w-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-white font-semibold text-lg bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                    children: \"MOM App\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/60 text-xs font-medium hidden sm:block\",\n                    children: \"Meeting Recording & Transcription\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex bg-white/10 backdrop-blur-sm rounded-xl p-1 border border-white/20 shadow-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('recording'),\n                    className: `px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${activeView === 'recording' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10'}`,\n                    children: \"\\u012Era\\u0161ymas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('transcription'),\n                    className: `px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${activeView === 'transcription' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10'}`,\n                    children: \"Transkribavimas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('transcript'),\n                    className: `px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${activeView === 'transcript' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10'}`,\n                    children: \"Rezultatai\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:flex items-center gap-3\",\n                children: [meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: loadDemoData,\n                  className: \"px-4 py-2 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                    className: \"h-4 w-4 mr-2 inline\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this), \"Demo\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('recording');\n                    if (!recordingState.isRecording) {\n                      handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                    }\n                  },\n                  className: \"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20\",\n                  children: [/*#__PURE__*/_jsxDEV(Plus, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 21\n                  }, this), \"Naujas pokalbis\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:hidden flex items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n                  className: \"p-2 rounded-lg bg-white/10 hover:bg-white/20 border border-white/20 shadow-lg transition-all duration-300\",\n                  \"aria-expanded\": isMobileMenuOpen,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Open main menu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 21\n                  }, this), isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n                    className: \"h-6 w-6 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                    className: \"h-6 w-6 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 pt-2 pb-4 space-y-3 glassmorphic-card m-2 rounded-xl border border-white/20 shadow-lg mobile-menu-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('recording');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${activeView === 'recording' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Mic2, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this), \"\\u012Era\\u0161ymas\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('transcription');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${activeView === 'transcription' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Zap, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 21\n                  }, this), \"Transkribavimas\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('transcript');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${activeView === 'transcript' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Headphones, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 21\n                  }, this), \"Rezultatai\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pt-2 border-t border-white/10\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col space-y-2\",\n                  children: [meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      loadDemoData();\n                      setIsMobileMenuOpen(false);\n                    },\n                    className: \"px-4 py-3 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                      className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 25\n                    }, this), \"Demo\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setActiveView('recording');\n                      setIsMobileMenuOpen(false);\n                      if (!recordingState.isRecording) {\n                        handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                      }\n                    },\n                    className: \"px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20\",\n                    children: [/*#__PURE__*/_jsxDEV(Plus, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 23\n                    }, this), \"Naujas pokalbis\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"px-4 py-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white/70 text-sm mr-2\",\n                        children: \"Whisper Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 502,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 py-8 pt-24 md:pt-24\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-8 min-h-[calc(100vh-140px)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-3\",\n              children: [activeView === 'recording' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5 border-b border-white/15\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(Mic2, {\n                        className: \"h-5 w-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 529,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-base font-semibold text-white\",\n                        children: \"Pokalbio \\u012Fra\\u0161ymas\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 532,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-white/50 font-medium\",\n                        children: \"Prad\\u0117kite nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 533,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-8 flex-1 flex flex-col justify-center items-center min-h-[500px]\",\n                  children: !recordingState.isRecording ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"max-w-lg mx-auto text-center space-y-10 animate-fade-in\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-32 h-32 bg-gradient-to-br from-blue-500/20 via-indigo-500/15 to-purple-600/20 backdrop-blur-xl rounded-3xl flex items-center justify-center border border-white/10 shadow-2xl mx-auto\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-20 h-20 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-2xl flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Mic2, {\n                            className: \"h-10 w-10 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 546,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 545,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -inset-4 bg-gradient-to-r from-blue-500/10 via-transparent to-purple-600/10 rounded-full blur-xl opacity-60\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 549,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                          className: \"text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight tracking-tight\",\n                          children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 555,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-base sm:text-lg text-white/70 leading-relaxed font-medium max-w-2xl mx-auto\",\n                          children: \"Profesionalus garso \\u012Fra\\u0161ymas su automatine transkribavimo technologija\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 558,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 554,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-6 mt-8\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center space-y-3 group\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-14 h-14 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl flex items-center justify-center mx-auto border border-blue-400/20 group-hover:scale-110 transition-transform duration-300\",\n                            children: /*#__PURE__*/_jsxDEV(Zap, {\n                              className: \"h-6 w-6 text-blue-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 567,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 566,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-white/70 font-medium leading-tight\",\n                            children: [\"Automatinis\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 569,\n                              columnNumber: 105\n                            }, this), \"transkribavimas\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 569,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 565,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center space-y-3 group\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-14 h-14 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-xl flex items-center justify-center mx-auto border border-purple-400/20 group-hover:scale-110 transition-transform duration-300\",\n                            children: /*#__PURE__*/_jsxDEV(Headphones, {\n                              className: \"h-6 w-6 text-purple-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 573,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 572,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-white/70 font-medium leading-tight\",\n                            children: [\"Auk\\u0161ta garso\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 575,\n                              columnNumber: 106\n                            }, this), \"kokyb\\u0117\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 575,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 571,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center space-y-3 group\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-14 h-14 bg-gradient-to-br from-indigo-500/20 to-indigo-600/20 rounded-xl flex items-center justify-center mx-auto border border-indigo-400/20 group-hover:scale-110 transition-transform duration-300\",\n                            children: /*#__PURE__*/_jsxDEV(Settings, {\n                              className: \"h-6 w-6 text-indigo-400\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 579,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 578,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-white/70 font-medium leading-tight\",\n                            children: [\"Pa\\u017Eang\\u016Bs\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 581,\n                              columnNumber: 102\n                            }, this), \"nustatymai\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 581,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 577,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 564,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`),\n                      className: \"group relative px-10 py-4 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-500 hover:via-blue-600 hover:to-indigo-600 text-white rounded-2xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-2xl border border-blue-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] mx-auto\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Plus, {\n                        className: \"h-5 w-5 transition-transform duration-200 group-hover:rotate-90\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 593,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"max-w-lg mx-auto text-center space-y-10 animate-fade-in\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-32 h-32 bg-gradient-to-br from-red-500/30 via-red-600/20 to-red-700/30 backdrop-blur-xl rounded-3xl flex items-center justify-center border border-red-400/20 shadow-2xl mx-auto animate-pulse\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-20 h-20 bg-gradient-to-br from-red-500/40 to-red-600/40 rounded-2xl flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(Mic2, {\n                            className: \"h-10 w-10 text-white animate-bounce\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 602,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 601,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -inset-4 bg-gradient-to-r from-red-500/20 via-transparent to-red-600/20 rounded-full blur-xl opacity-80 animate-pulse\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 605,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-2 -right-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"relative\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-6 h-6 bg-red-500 rounded-full animate-pulse\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 610,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 w-6 h-6 bg-red-500/50 rounded-full animate-ping\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 611,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 609,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                          className: \"text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight tracking-tight\",\n                          children: \"Pokalbis \\u012Fra\\u0161omas\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 619,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-base sm:text-lg text-white/70 leading-relaxed font-medium max-w-2xl mx-auto\",\n                          children: \"J\\u016Bs\\u0173 pokalbis s\\u0117kmingai \\u012Fra\\u0161omas ir bus automati\\u0161kai transkribuojamas\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 622,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 618,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white/5 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"grid grid-cols-2 gap-6\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-2xl font-bold text-white mb-1\",\n                              children: [Math.floor(recordingState.duration / 60), \":\", String(recordingState.duration % 60).padStart(2, '0')]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 631,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-sm text-white/60 font-medium\",\n                              children: \"Trukm\\u0117\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 634,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 630,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center justify-center space-x-1 mb-1\",\n                              children: /*#__PURE__*/_jsxDEV(DynamicAudioVisualizer, {\n                                recordingState: recordingState\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 638,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 637,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-sm text-white/60 font-medium\",\n                              children: \"Garso lygis\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 640,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 636,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 629,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 617,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleStopRecording,\n                      className: \"group relative px-10 py-4 bg-gradient-to-r from-red-600 via-red-700 to-red-800 hover:from-red-500 hover:via-red-600 hover:to-red-700 text-white rounded-2xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-2xl border border-red-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] mx-auto\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-red-400/20 to-red-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Square, {\n                        className: \"h-5 w-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 652,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 653,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 647,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this), activeView === 'transcription' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6 border-b border-white/20\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(Zap, {\n                        className: \"h-6 w-6 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-semibold text-white\",\n                        children: \"Transkribavimas\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 671,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-white/60\",\n                        children: \"Audio fail\\u0173 konvertavimas \\u012F tekst\\u0105 naudojant AI\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 670,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: /*#__PURE__*/_jsxDEV(TranscriptionManager, {\n                    meetings: meetings,\n                    onStartTranscription: handleStartTranscription,\n                    onCancelTranscription: handleCancelTranscription,\n                    isTranscribing: isTranscribing,\n                    currentTranscriptionId: currentTranscriptionId,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onViewResults: () => setActiveView('transcript')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this), activeView === 'transcript' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6 border-b border-white/20\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(Headphones, {\n                        className: \"h-6 w-6 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 699,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-semibold text-white\",\n                        children: \"Rezultatai\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 702,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-white/60\",\n                        children: \"Per\\u017Ei\\u016Br\\u0117kite ir redaguokite transkribavimo rezultatus\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: /*#__PURE__*/_jsxDEV(ProfessionalTranscriptViewer, {\n                    meetings: meetings,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onGoToTranscription: () => setActiveView('transcription')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5 border-b border-white/15\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-gradient-to-br from-indigo-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(List, {\n                        className: \"h-5 w-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 727,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 726,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-base font-semibold text-white\",\n                        children: \"Pokalbiai\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-white/50 font-medium\",\n                        children: [\"Visi pokalbiai (\", meetings.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5\",\n                  children: meetings.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-10\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 bg-white/8 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-3 border border-white/15 shadow-lg\",\n                      children: /*#__PURE__*/_jsxDEV(List, {\n                        className: \"h-5 w-5 text-white/50\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 741,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-white font-medium mb-1.5 text-sm\",\n                      children: \"N\\u0117ra pokalbi\\u0173\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-white/50 text-xs leading-relaxed\",\n                      children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 744,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/_jsxDEV(MeetingsList, {\n                    meetings: meetings,\n                    currentMeeting: currentMeeting,\n                    onSelectMeeting: handleSelectMeeting,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onExportMeeting: () => {},\n                    activeView: \"list\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"/avb+Saz+4REjybQQZJiPi+i6Fo=\", false, function () {\n  return [useAudioRecorder, useTranscription];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "MeetingsList", "Error<PERSON>ou<PERSON><PERSON>", "WhisperStatusIndicator", "TranscriptionManager", "ProfessionalTranscriptViewer", "DynamicAudioVisualizer", "useAudioRecorder", "useTranscription", "createDemoMeetings", "Headphones", "Plus", "Mic2", "TestTube", "Zap", "Settings", "List", "Square", "<PERSON><PERSON>", "X", "jsxDEV", "_jsxDEV", "App", "_s", "meetings", "setMeetings", "currentMeeting", "setCurrentMeeting", "selectedMeetingForTranscript", "setSelectedMeetingForTranscript", "activeView", "setActiveView", "isMobileMenuOpen", "setIsMobileMenuOpen", "recordingState", "startRecording", "stopRecording", "pauseRecording", "resumeRecording", "transcript", "isTranscribing", "transcribeAudioEnhanced", "cancelTranscription", "editSegment", "clearTranscript", "clearError", "currentTranscriptionId", "progress", "isWhisperConfigured", "handleStartRecording", "title", "newMeeting", "id", "Date", "now", "toString", "date", "duration", "status", "transcriptionStatus", "state", "prev", "error", "console", "handleStopRecording", "audioBlob", "updatedMeeting", "Math", "floor", "getTime", "map", "m", "alert", "handleStartTranscription", "meetingId", "meeting", "find", "startedAt", "result", "onProgress", "onStatusUpdate", "enhanceSpeakers", "completedMeeting", "segments", "participants", "speakers", "metadata", "completedAt", "log", "length", "words", "totalWords", "confidence", "averageConfidence", "errorMeeting", "message", "handleCancelTranscription", "handleEditSegment", "segmentId", "newText", "_meeting$transcript", "segment", "text", "isEdited", "editedAt", "editedBy", "handleSelectMeeting", "handleDeleteMeeting", "filter", "handleExportMeeting", "exportData", "toISOString", "dataStr", "JSON", "stringify", "dataUri", "encodeURIComponent", "exportFileDefaultName", "replace", "toLowerCase", "split", "linkElement", "document", "createElement", "setAttribute", "click", "loadDemoData", "demoMeetings", "handleClickOutside", "event", "target", "closest", "handleEscape", "key", "addEventListener", "body", "classList", "add", "remove", "removeEventListener", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "isRecording", "toLocaleString", "String", "padStart", "onStartTranscription", "onCancelTranscription", "onDeleteMeeting", "onViewResults", "onGoToTranscription", "onSelectMeeting", "onExportMeeting", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from 'react';\nimport {\n  <PERSON>s<PERSON>ist,\n  <PERSON>rror<PERSON>ou<PERSON><PERSON>,\n  WhisperStatusIndicator,\n  TranscriptionManager,\n  ProfessionalTranscriptViewer,\n  DynamicAudioVisualizer\n} from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { Meeting, TranscriptionStatus, Speaker } from './types/meeting';\nimport { createDemoMeetings } from './utils/demoData';\nimport { identifySpeakers } from './services/speakerService';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List, Square, Menu, X } from 'lucide-react';\nimport './styles/background.css';\n\nfunction App() {\n  const [meetings, setMeetings] = useState<Meeting[]>([]);\n  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);\n  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();\n  const { \n    transcript, \n    isTranscribing, \n    transcribeAudioEnhanced, \n    cancelTranscription,\n    editSegment,\n    clearTranscript, \n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n\n  const handleStartRecording = useCallback(async (title: string) => {\n    try {\n      await startRecording();\n      \n      const newMeeting: Meeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started',\n        },\n      };\n      \n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      \n      if (currentMeeting && audioBlob) {\n        const updatedMeeting: Meeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started',\n          },\n        };\n\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => \n          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)\n        );\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n\n  const handleStartTranscription = useCallback(async (meetingId: string) => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting: Meeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date(),\n      },\n    };\n\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: (progress) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: { \n                    ...m.transcriptionStatus, \n                    progress,\n                    state: 'processing' \n                  } \n                }\n              : m\n          ));\n        },\n        onStatusUpdate: (status) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: status \n                }\n              : m\n          ));\n        },\n        enhanceSpeakers: true,\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting: Meeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date(),\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n      \n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence,\n      });\n\n    } catch (error: any) {\n      console.error('❌ Transkribavimo klaida:', error);\n      \n      const errorMeeting: Meeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n\n  const handleCancelTranscription = useCallback((meetingId: string) => {\n    cancelTranscription();\n    \n    setMeetings(prev => prev.map(m => \n      m.id === meetingId \n        ? { \n            ...m, \n            transcriptionStatus: { \n              ...m.transcriptionStatus, \n              state: 'cancelled' as const \n            } \n          }\n        : m\n    ));\n  }, [cancelTranscription]);\n\n  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {\n    editSegment(segmentId, newText);\n    \n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => \n      meeting.id === meetingId\n        ? {\n            ...meeting,\n            transcript: meeting.transcript?.map(segment => \n              segment.id === segmentId \n                ? {\n                    ...segment,\n                    text: newText,\n                    isEdited: true,\n                    editedAt: new Date(),\n                    editedBy: 'user'\n                  }\n                : segment\n            ),\n          }\n        : meeting\n    ));\n  }, [editSegment]);\n\n  const handleSelectMeeting = useCallback((meeting: Meeting) => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n\n  const handleDeleteMeeting = useCallback((meetingId: string) => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if (currentMeeting?.id === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if (selectedMeetingForTranscript?.id === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n\n  const handleExportMeeting = useCallback((meeting: Meeting) => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus,\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);\n    \n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    \n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n\n\n\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed' as const,\n        progress: 100,\n        completedAt: meeting.date,\n      },\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  // Close mobile menu when clicking outside or on escape\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Element;\n      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    if (isMobileMenuOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when mobile menu is open\n      document.body.classList.add('mobile-menu-open');\n    } else {\n      document.body.classList.remove('mobile-menu-open');\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('keydown', handleEscape);\n      document.body.classList.remove('mobile-menu-open');\n    };\n  }, [isMobileMenuOpen]);\n\n  return (\n    <ErrorBoundary>\n      <div className=\"min-h-screen elegant-background font-inter\">\n        {/* Elegant Grid Pattern */}\n        <div className=\"elegant-grid\"></div>\n\n        {/* Sophisticated Background Elements */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl\"></div>\n          <div className=\"absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl\"></div>\n        </div>\n\n        {/* Content Wrapper */}\n        <div className=\"relative z-10\">\n          {/* Glassmorphic Header */}\n          <header className=\"fixed top-0 left-0 right-0 z-50 glassmorphic-header\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6\">\n              <div className=\"flex items-center justify-between h-16\">\n                {/* Logo - Always visible */}\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\">\n                    <Mic2 className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div>\n                    <h1 className=\"text-white font-semibold text-lg bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\">\n                      MOM App\n                    </h1>\n                    <p className=\"text-white/60 text-xs font-medium hidden sm:block\">Meeting Recording & Transcription</p>\n                  </div>\n                </div>\n\n                {/* Desktop Navigation - Hidden on mobile */}\n                <div className=\"hidden md:flex items-center gap-4\">\n                  <div className=\"bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg\">\n                    <WhisperStatusIndicator />\n                  </div>\n                  <div className=\"flex bg-white/10 backdrop-blur-sm rounded-xl p-1 border border-white/20 shadow-lg\">\n                    <button\n                      onClick={() => setActiveView('recording')}\n                      className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${\n                        activeView === 'recording'\n                          ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                          : 'text-white/70 hover:text-white hover:bg-white/10'\n                      }`}\n                    >\n                      Įrašymas\n                    </button>\n                    <button\n                      onClick={() => setActiveView('transcription')}\n                      className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${\n                        activeView === 'transcription'\n                          ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                          : 'text-white/70 hover:text-white hover:bg-white/10'\n                      }`}\n                    >\n                      Transkribavimas\n                    </button>\n                    <button\n                      onClick={() => setActiveView('transcript')}\n                      className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${\n                        activeView === 'transcript'\n                          ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                          : 'text-white/70 hover:text-white hover:bg-white/10'\n                      }`}\n                    >\n                      Rezultatai\n                    </button>\n                  </div>\n                </div>\n\n                {/* Desktop Action Buttons - Hidden on mobile */}\n                <div className=\"hidden md:flex items-center gap-3\">\n                  {meetings.length === 0 && (\n                    <button\n                      onClick={loadDemoData}\n                      className=\"px-4 py-2 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg\"\n                    >\n                      <TestTube className=\"h-4 w-4 mr-2 inline\" />\n                      Demo\n                    </button>\n                  )}\n                  <button\n                    onClick={() => {\n                      setActiveView('recording');\n                      if (!recordingState.isRecording) {\n                        handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                      }\n                    }}\n                    className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20\"\n                  >\n                    <Plus className=\"h-4 w-4\" />\n                    Naujas pokalbis\n                  </button>\n                </div>\n\n                {/* Mobile menu button - Only visible on mobile */}\n                <div className=\"md:hidden flex items-center\">\n                  <button\n                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                    className=\"p-2 rounded-lg bg-white/10 hover:bg-white/20 border border-white/20 shadow-lg transition-all duration-300\"\n                    aria-expanded={isMobileMenuOpen}\n                  >\n                    <span className=\"sr-only\">Open main menu</span>\n                    {isMobileMenuOpen ? (\n                      <X className=\"h-6 w-6 text-white\" />\n                    ) : (\n                      <Menu className=\"h-6 w-6 text-white\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Mobile menu, show/hide based on menu state */}\n            <div className={`md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`}>\n              <div className=\"px-4 pt-2 pb-4 space-y-3 glassmorphic-card m-2 rounded-xl border border-white/20 shadow-lg mobile-menu-container\">\n                {/* Mobile Navigation Pills */}\n                <div className=\"flex flex-col space-y-2\">\n                  <button\n                    onClick={() => {\n                      setActiveView('recording');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${\n                      activeView === 'recording'\n                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'\n                    }`}\n                  >\n                    <Mic2 className=\"h-4 w-4 mr-2\" />\n                    Įrašymas\n                  </button>\n                  <button\n                    onClick={() => {\n                      setActiveView('transcription');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${\n                      activeView === 'transcription'\n                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'\n                    }`}\n                  >\n                    <Zap className=\"h-4 w-4 mr-2\" />\n                    Transkribavimas\n                  </button>\n                  <button\n                    onClick={() => {\n                      setActiveView('transcript');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${\n                      activeView === 'transcript'\n                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'\n                    }`}\n                  >\n                    <Headphones className=\"h-4 w-4 mr-2\" />\n                    Rezultatai\n                  </button>\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className=\"pt-2 border-t border-white/10\">\n                  <div className=\"flex flex-col space-y-2\">\n                    {meetings.length === 0 && (\n                      <button\n                        onClick={() => {\n                          loadDemoData();\n                          setIsMobileMenuOpen(false);\n                        }}\n                        className=\"px-4 py-3 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg flex items-center\"\n                      >\n                        <TestTube className=\"h-4 w-4 mr-2\" />\n                        Demo\n                      </button>\n                    )}\n                    <button\n                      onClick={() => {\n                        setActiveView('recording');\n                        setIsMobileMenuOpen(false);\n                        if (!recordingState.isRecording) {\n                          handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                        }\n                      }}\n                      className=\"px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20\"\n                    >\n                      <Plus className=\"h-4 w-4\" />\n                      Naujas pokalbis\n                    </button>\n\n                    {/* Whisper Status in Mobile Menu */}\n                    <div className=\"px-4 py-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-white/70 text-sm mr-2\">Whisper Status:</span>\n                        <WhisperStatusIndicator />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n\n\n\n\n        {/* Main Content */}\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 py-8 pt-24 md:pt-24\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-8 min-h-[calc(100vh-140px)]\">\n\n            {/* Main Content Area */}\n            <div className=\"lg:col-span-3\">\n              {/* Recording View */}\n              {activeView === 'recording' && (\n                <div className=\"glassmorphic-card rounded-2xl h-full\">\n                  {/* Header */}\n                  <div className=\"p-5 border-b border-white/15\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20\">\n                        <Mic2 className=\"h-5 w-5 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-base font-semibold text-white\">Pokalbio įrašymas</h2>\n                        <p className=\"text-xs text-white/50 font-medium\">Pradėkite naują pokalbio įrašymą</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-8 flex-1 flex flex-col justify-center items-center min-h-[500px]\">\n                    {!recordingState.isRecording ? (\n                      <div className=\"max-w-lg mx-auto text-center space-y-10 animate-fade-in\">\n                        {/* Hero Icon */}\n                        <div className=\"relative\">\n                          <div className=\"w-32 h-32 bg-gradient-to-br from-blue-500/20 via-indigo-500/15 to-purple-600/20 backdrop-blur-xl rounded-3xl flex items-center justify-center border border-white/10 shadow-2xl mx-auto\">\n                            <div className=\"w-20 h-20 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-2xl flex items-center justify-center\">\n                              <Mic2 className=\"h-10 w-10 text-white\" />\n                            </div>\n                          </div>\n                          <div className=\"absolute -inset-4 bg-gradient-to-r from-blue-500/10 via-transparent to-purple-600/10 rounded-full blur-xl opacity-60\" />\n                        </div>\n\n                        {/* Content */}\n                        <div className=\"space-y-6\">\n                          <div className=\"space-y-4\">\n                            <h2 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight tracking-tight\">\n                              Pradėkite naują pokalbį\n                            </h2>\n                            <p className=\"text-base sm:text-lg text-white/70 leading-relaxed font-medium max-w-2xl mx-auto\">\n                              Profesionalus garso įrašymas su automatine transkribavimo technologija\n                            </p>\n                          </div>\n\n                          {/* Features */}\n                          <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6 mt-8\">\n                            <div className=\"text-center space-y-3 group\">\n                              <div className=\"w-14 h-14 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl flex items-center justify-center mx-auto border border-blue-400/20 group-hover:scale-110 transition-transform duration-300\">\n                                <Zap className=\"h-6 w-6 text-blue-400\" />\n                              </div>\n                              <p className=\"text-sm text-white/70 font-medium leading-tight\">Automatinis<br />transkribavimas</p>\n                            </div>\n                            <div className=\"text-center space-y-3 group\">\n                              <div className=\"w-14 h-14 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-xl flex items-center justify-center mx-auto border border-purple-400/20 group-hover:scale-110 transition-transform duration-300\">\n                                <Headphones className=\"h-6 w-6 text-purple-400\" />\n                              </div>\n                              <p className=\"text-sm text-white/70 font-medium leading-tight\">Aukšta garso<br />kokybė</p>\n                            </div>\n                            <div className=\"text-center space-y-3 group\">\n                              <div className=\"w-14 h-14 bg-gradient-to-br from-indigo-500/20 to-indigo-600/20 rounded-xl flex items-center justify-center mx-auto border border-indigo-400/20 group-hover:scale-110 transition-transform duration-300\">\n                                <Settings className=\"h-6 w-6 text-indigo-400\" />\n                              </div>\n                              <p className=\"text-sm text-white/70 font-medium leading-tight\">Pažangūs<br />nustatymai</p>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* CTA Button */}\n                        <button\n                          onClick={() => handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}\n                          className=\"group relative px-10 py-4 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-500 hover:via-blue-600 hover:to-indigo-600 text-white rounded-2xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-2xl border border-blue-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] mx-auto\"\n                        >\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                          <Plus className=\"h-5 w-5 transition-transform duration-200 group-hover:rotate-90\" />\n                          <span>Pradėti įrašymą</span>\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"max-w-lg mx-auto text-center space-y-10 animate-fade-in\">\n                        {/* Recording Status */}\n                        <div className=\"relative\">\n                          <div className=\"w-32 h-32 bg-gradient-to-br from-red-500/30 via-red-600/20 to-red-700/30 backdrop-blur-xl rounded-3xl flex items-center justify-center border border-red-400/20 shadow-2xl mx-auto animate-pulse\">\n                            <div className=\"w-20 h-20 bg-gradient-to-br from-red-500/40 to-red-600/40 rounded-2xl flex items-center justify-center\">\n                              <Mic2 className=\"h-10 w-10 text-white animate-bounce\" />\n                            </div>\n                          </div>\n                          <div className=\"absolute -inset-4 bg-gradient-to-r from-red-500/20 via-transparent to-red-600/20 rounded-full blur-xl opacity-80 animate-pulse\" />\n\n                          {/* Recording indicator */}\n                          <div className=\"absolute -top-2 -right-2\">\n                            <div className=\"relative\">\n                              <div className=\"w-6 h-6 bg-red-500 rounded-full animate-pulse\" />\n                              <div className=\"absolute inset-0 w-6 h-6 bg-red-500/50 rounded-full animate-ping\" />\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Content */}\n                        <div className=\"space-y-6\">\n                          <div className=\"space-y-4\">\n                            <h2 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight tracking-tight\">\n                              Pokalbis įrašomas\n                            </h2>\n                            <p className=\"text-base sm:text-lg text-white/70 leading-relaxed font-medium max-w-2xl mx-auto\">\n                              Jūsų pokalbis sėkmingai įrašomas ir bus automatiškai transkribuojamas\n                            </p>\n                          </div>\n\n                          {/* Recording Stats */}\n                          <div className=\"bg-white/5 backdrop-blur-md rounded-2xl p-6 border border-white/10\">\n                            <div className=\"grid grid-cols-2 gap-6\">\n                              <div className=\"text-center\">\n                                <div className=\"text-2xl font-bold text-white mb-1\">\n                                  {Math.floor(recordingState.duration / 60)}:{String(recordingState.duration % 60).padStart(2, '0')}\n                                </div>\n                                <div className=\"text-sm text-white/60 font-medium\">Trukmė</div>\n                              </div>\n                              <div className=\"text-center\">\n                                <div className=\"flex items-center justify-center space-x-1 mb-1\">\n                                  <DynamicAudioVisualizer recordingState={recordingState} />\n                                </div>\n                                <div className=\"text-sm text-white/60 font-medium\">Garso lygis</div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Stop Button */}\n                        <button\n                          onClick={handleStopRecording}\n                          className=\"group relative px-10 py-4 bg-gradient-to-r from-red-600 via-red-700 to-red-800 hover:from-red-500 hover:via-red-600 hover:to-red-700 text-white rounded-2xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-2xl border border-red-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] mx-auto\"\n                        >\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-red-400/20 to-red-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                          <Square className=\"h-5 w-5\" />\n                          <span>Sustabdyti įrašymą</span>\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Transcription View */}\n              {activeView === 'transcription' && (\n                <div className=\"glassmorphic-card rounded-2xl h-full\">\n                  {/* Header */}\n                  <div className=\"p-6 border-b border-white/20\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\">\n                        <Zap className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-xl font-semibold text-white\">Transkribavimas</h2>\n                        <p className=\"text-sm text-white/60\">Audio failų konvertavimas į tekstą naudojant AI</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <TranscriptionManager\n                      meetings={meetings}\n                      onStartTranscription={handleStartTranscription}\n                      onCancelTranscription={handleCancelTranscription}\n                      isTranscribing={isTranscribing}\n                      currentTranscriptionId={currentTranscriptionId}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onViewResults={() => setActiveView('transcript')}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Transcript View */}\n              {activeView === 'transcript' && (\n                <div className=\"glassmorphic-card rounded-2xl h-full\">\n                  {/* Header */}\n                  <div className=\"p-6 border-b border-white/20\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\">\n                        <Headphones className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-xl font-semibold text-white\">Rezultatai</h2>\n                        <p className=\"text-sm text-white/60\">Peržiūrėkite ir redaguokite transkribavimo rezultatus</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <ProfessionalTranscriptViewer\n                      meetings={meetings}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onGoToTranscription={() => setActiveView('transcription')}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Sidebar - Meetings List */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"glassmorphic-card rounded-2xl h-full\">\n                {/* Sidebar Header */}\n                <div className=\"p-5 border-b border-white/15\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-br from-indigo-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20\">\n                      <List className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <h2 className=\"text-base font-semibold text-white\">Pokalbiai</h2>\n                      <p className=\"text-xs text-white/50 font-medium\">Visi pokalbiai ({meetings.length})</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Sidebar Content */}\n                <div className=\"p-5\">\n                  {meetings.length === 0 ? (\n                    <div className=\"text-center py-10\">\n                      <div className=\"w-14 h-14 bg-white/8 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-3 border border-white/15 shadow-lg\">\n                        <List className=\"h-5 w-5 text-white/50\" />\n                      </div>\n                      <h3 className=\"text-white font-medium mb-1.5 text-sm\">Nėra pokalbių</h3>\n                      <p className=\"text-white/50 text-xs leading-relaxed\">\n                        Pradėkite naują pokalbį, kad pamatytumėte jį čia\n                      </p>\n                    </div>\n                  ) : (\n                    <MeetingsList\n                      meetings={meetings}\n                      currentMeeting={currentMeeting}\n                      onSelectMeeting={handleSelectMeeting}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onExportMeeting={() => {}}\n                      activeView=\"list\"\n                    />\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n        </div> {/* Close content wrapper */}\n      </div>\n    </ErrorBoundary>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,SACEC,YAAY,EACZC,aAAa,EACbC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,sBAAsB,QACjB,cAAc;AACrB,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,SAAS;AAE5D,SAASC,kBAAkB,QAAQ,kBAAkB;AAErD,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AACrG,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC8B,4BAA4B,EAAEC,+BAA+B,CAAC,GAAG/B,QAAQ,CAAiB,IAAI,CAAC;EACtG,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAA+C,WAAW,CAAC;EACvG,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAM;IAAEoC,cAAc;IAAEC,cAAc;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAgB,CAAC,GAAG/B,gBAAgB,CAAC,CAAC;EAC7G,MAAM;IACJgC,UAAU;IACVC,cAAc;IACdC,uBAAuB;IACvBC,mBAAmB;IACnBC,WAAW;IACXC,eAAe;IACfC,UAAU;IACVC,sBAAsB;IACtBC,QAAQ;IACRC;EACF,CAAC,GAAGxC,gBAAgB,CAAC,CAAC;EAEtB,MAAMyC,oBAAoB,GAAGlD,WAAW,CAAC,MAAOmD,KAAa,IAAK;IAChE,IAAI;MACF,MAAMf,cAAc,CAAC,CAAC;MAEtB,MAAMgB,UAAmB,GAAG;QAC1BC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBL,KAAK,EAAEA,KAAK;QACZM,IAAI,EAAE,IAAIH,IAAI,CAAC,CAAC;QAChBI,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,WAAW;QACnBC,mBAAmB,EAAE;UACnBC,KAAK,EAAE;QACT;MACF,CAAC;MAEDjC,iBAAiB,CAACwB,UAAU,CAAC;MAC7B1B,WAAW,CAACoC,IAAI,IAAI,CAACV,UAAU,EAAE,GAAGU,IAAI,CAAC,CAAC;MAC1C9B,aAAa,CAAC,WAAW,CAAC;MAC1Ba,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAAC3B,cAAc,EAAES,eAAe,CAAC,CAAC;EAErC,MAAMoB,mBAAmB,GAAGjE,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMkE,SAAS,GAAG,MAAM7B,aAAa,CAAC,CAAC;MAEvC,IAAIV,cAAc,IAAIuC,SAAS,EAAE;QAC/B,MAAMC,cAAuB,GAAG;UAC9B,GAAGxC,cAAc;UACjBgC,MAAM,EAAE,WAAW;UACnBD,QAAQ,EAAEU,IAAI,CAACC,KAAK,CAAC,CAACf,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG5B,cAAc,CAAC8B,IAAI,CAACa,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;UACzEJ,SAAS;UACTN,mBAAmB,EAAE;YACnBC,KAAK,EAAE;UACT;QACF,CAAC;QAEDjC,iBAAiB,CAACuC,cAAc,CAAC;QACjCzC,WAAW,CAACoC,IAAI,IACdA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAK1B,cAAc,CAAC0B,EAAE,GAAGc,cAAc,GAAGK,CAAC,CAC/D,CAAC;;QAED;QACAxC,aAAa,CAAC,eAAe,CAAC;MAChC;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDU,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC,EAAE,CAACpC,aAAa,EAAEV,cAAc,CAAC,CAAC;EAEnC,MAAM+C,wBAAwB,GAAG1E,WAAW,CAAC,MAAO2E,SAAiB,IAAK;IACxE,MAAMC,OAAO,GAAGnD,QAAQ,CAACoD,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,CAAC;IACtD,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACV,SAAS,EAAE;;IAEpC;IACA,MAAMC,cAAuB,GAAG;MAC9B,GAAGS,OAAO;MACVhB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,SAAS;QAChBiB,SAAS,EAAE,IAAIxB,IAAI,CAAC;MACtB;IACF,CAAC;IAED5B,WAAW,CAACoC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGR,cAAc,GAAGK,CAAC,CAAC,CAAC;IAC3E1C,+BAA+B,CAACqC,cAAc,CAAC;IAE/C,IAAI;MACF;MACA,MAAMY,MAAM,GAAG,MAAMrC,uBAAuB,CAACkC,OAAO,CAACV,SAAS,EAAES,SAAS,EAAE;QACzEK,UAAU,EAAGhC,QAAQ,IAAK;UACxBtB,WAAW,CAACoC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;YACE,GAAGH,CAAC;YACJZ,mBAAmB,EAAE;cACnB,GAAGY,CAAC,CAACZ,mBAAmB;cACxBZ,QAAQ;cACRa,KAAK,EAAE;YACT;UACF,CAAC,GACDW,CACN,CAAC,CAAC;QACJ,CAAC;QACDS,cAAc,EAAGtB,MAAM,IAAK;UAC1BjC,WAAW,CAACoC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;YACE,GAAGH,CAAC;YACJZ,mBAAmB,EAAED;UACvB,CAAC,GACDa,CACN,CAAC,CAAC;QACJ,CAAC;QACDU,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAMC,gBAAyB,GAAG;QAChC,GAAGhB,cAAc;QACjB3B,UAAU,EAAEuC,MAAM,CAACK,QAAQ;QAC3BC,YAAY,EAAEN,MAAM,CAACO,QAAQ;QAC7BC,QAAQ,EAAER,MAAM,CAACQ,QAAQ;QACzB3B,mBAAmB,EAAE;UACnBC,KAAK,EAAE,WAAW;UAClBb,QAAQ,EAAE,GAAG;UACb8B,SAAS,EAAEX,cAAc,CAACP,mBAAmB,CAACkB,SAAS;UACvDU,WAAW,EAAE,IAAIlC,IAAI,CAAC;QACxB;MACF,CAAC;MAED5B,WAAW,CAACoC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGQ,gBAAgB,GAAGX,CAAC,CAAC,CAAC;MAC7E1C,+BAA+B,CAACqD,gBAAgB,CAAC;;MAEjD;MACAnD,aAAa,CAAC,YAAY,CAAC;MAE3BgC,OAAO,CAACyB,GAAG,CAAC,sCAAsC,EAAE;QAClDL,QAAQ,EAAEL,MAAM,CAACK,QAAQ,CAACM,MAAM;QAChCJ,QAAQ,EAAEP,MAAM,CAACO,QAAQ,CAACI,MAAM;QAChCC,KAAK,EAAEZ,MAAM,CAACQ,QAAQ,CAACK,UAAU;QACjCC,UAAU,EAAEd,MAAM,CAACQ,QAAQ,CAACO;MAC9B,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAEhD,MAAMgC,YAAqB,GAAG;QAC5B,GAAG5B,cAAc;QACjBP,mBAAmB,EAAE;UACnBC,KAAK,EAAE,QAAQ;UACfE,KAAK,EAAEA,KAAK,CAACiC,OAAO;UACpBlB,SAAS,EAAEX,cAAc,CAACP,mBAAmB,CAACkB;QAChD;MACF,CAAC;MAEDpD,WAAW,CAACoC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGoB,YAAY,GAAGvB,CAAC,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAAC/C,QAAQ,EAAEiB,uBAAuB,CAAC,CAAC;EAEvC,MAAMuD,yBAAyB,GAAGjG,WAAW,CAAE2E,SAAiB,IAAK;IACnEhC,mBAAmB,CAAC,CAAC;IAErBjB,WAAW,CAACoC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;MACE,GAAGH,CAAC;MACJZ,mBAAmB,EAAE;QACnB,GAAGY,CAAC,CAACZ,mBAAmB;QACxBC,KAAK,EAAE;MACT;IACF,CAAC,GACDW,CACN,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,mBAAmB,CAAC,CAAC;EAEzB,MAAMuD,iBAAiB,GAAGlG,WAAW,CAAC,CAAC2E,SAAiB,EAAEwB,SAAiB,EAAEC,OAAe,KAAK;IAC/FxD,WAAW,CAACuD,SAAS,EAAEC,OAAO,CAAC;;IAE/B;IACA1E,WAAW,CAACoC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACK,OAAO;MAAA,IAAAyB,mBAAA;MAAA,OAClCzB,OAAO,CAACvB,EAAE,KAAKsB,SAAS,GACpB;QACE,GAAGC,OAAO;QACVpC,UAAU,GAAA6D,mBAAA,GAAEzB,OAAO,CAACpC,UAAU,cAAA6D,mBAAA,uBAAlBA,mBAAA,CAAoB9B,GAAG,CAAC+B,OAAO,IACzCA,OAAO,CAACjD,EAAE,KAAK8C,SAAS,GACpB;UACE,GAAGG,OAAO;UACVC,IAAI,EAAEH,OAAO;UACbI,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,IAAInD,IAAI,CAAC,CAAC;UACpBoD,QAAQ,EAAE;QACZ,CAAC,GACDJ,OACN;MACF,CAAC,GACD1B,OAAO;IAAA,CACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChC,WAAW,CAAC,CAAC;EAEjB,MAAM+D,mBAAmB,GAAG3G,WAAW,CAAE4E,OAAgB,IAAK;IAC5DhD,iBAAiB,CAACgD,OAAO,CAAC;IAC1B,IAAIA,OAAO,CAACpC,UAAU,IAAIoC,OAAO,CAACpC,UAAU,CAACkD,MAAM,GAAG,CAAC,EAAE;MACvD5D,+BAA+B,CAAC8C,OAAO,CAAC;MACxC5C,aAAa,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4E,mBAAmB,GAAG5G,WAAW,CAAE2E,SAAiB,IAAK;IAC7DjD,WAAW,CAACoC,IAAI,IAAIA,IAAI,CAAC+C,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,CAAC,CAAC;IACzD,IAAI,CAAAhD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0B,EAAE,MAAKsB,SAAS,EAAE;MACpC/C,iBAAiB,CAAC,IAAI,CAAC;IACzB;IACA,IAAI,CAAAC,4BAA4B,aAA5BA,4BAA4B,uBAA5BA,4BAA4B,CAAEwB,EAAE,MAAKsB,SAAS,EAAE;MAClD7C,+BAA+B,CAAC,IAAI,CAAC;IACvC;EACF,CAAC,EAAE,CAACH,cAAc,EAAEE,4BAA4B,CAAC,CAAC;EAElD,MAAMiF,mBAAmB,GAAG9G,WAAW,CAAE4E,OAAgB,IAAK;IAC5D,MAAMmC,UAAU,GAAG;MACjB5D,KAAK,EAAEyB,OAAO,CAACzB,KAAK;MACpBM,IAAI,EAAEmB,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC;MAChCtD,QAAQ,EAAEkB,OAAO,CAAClB,QAAQ;MAC1BlB,UAAU,EAAEoC,OAAO,CAACpC,UAAU,IAAIA,UAAU;MAC5C6C,YAAY,EAAET,OAAO,CAACS,YAAY,IAAI,EAAE;MACxCE,QAAQ,EAAEX,OAAO,CAACW,QAAQ,IAAI,CAAC,CAAC;MAChC3B,mBAAmB,EAAEgB,OAAO,CAAChB;IAC/B,CAAC;IAED,MAAMqD,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACJ,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMK,OAAO,GAAG,sCAAsC,GAAEC,kBAAkB,CAACJ,OAAO,CAAC;IAEnF,MAAMK,qBAAqB,GAAG,WAAW1C,OAAO,CAACzB,KAAK,CAACoE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI5C,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAE5I,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC/CF,WAAW,CAACG,YAAY,CAAC,MAAM,EAAET,OAAO,CAAC;IACzCM,WAAW,CAACG,YAAY,CAAC,UAAU,EAAEP,qBAAqB,CAAC;IAC3DI,WAAW,CAACI,KAAK,CAAC,CAAC;EACrB,CAAC,EAAE,CAACtF,UAAU,CAAC,CAAC;EAIhB,MAAMuF,YAAY,GAAG/H,WAAW,CAAC,MAAM;IACrC,MAAMgI,YAAY,GAAGtH,kBAAkB,CAAC,CAAC,CAAC6D,GAAG,CAACK,OAAO,KAAK;MACxD,GAAGA,OAAO;MACVhB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,WAAoB;QAC3Bb,QAAQ,EAAE,GAAG;QACbwC,WAAW,EAAEZ,OAAO,CAACnB;MACvB;IACF,CAAC,CAAC,CAAC;IACH/B,WAAW,CAACsG,YAAY,CAAC;IACzBhG,aAAa,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/B,SAAS,CAAC,MAAM;IACd,MAAMgI,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAiB;MACtC,IAAIlG,gBAAgB,IAAI,CAACkG,MAAM,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;QACjElG,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED,MAAMmG,YAAY,GAAIH,KAAoB,IAAK;MAC7C,IAAIA,KAAK,CAACI,GAAG,KAAK,QAAQ,IAAIrG,gBAAgB,EAAE;QAC9CC,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED,IAAID,gBAAgB,EAAE;MACpB0F,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1DN,QAAQ,CAACY,gBAAgB,CAAC,SAAS,EAAEF,YAAY,CAAC;MAClD;MACAV,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjD,CAAC,MAAM;MACLf,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC;IACpD;IAEA,OAAO,MAAM;MACXhB,QAAQ,CAACiB,mBAAmB,CAAC,WAAW,EAAEX,kBAAkB,CAAC;MAC7DN,QAAQ,CAACiB,mBAAmB,CAAC,SAAS,EAAEP,YAAY,CAAC;MACrDV,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAAC1G,gBAAgB,CAAC,CAAC;EAEtB,oBACEX,OAAA,CAACnB,aAAa;IAAA0I,QAAA,eACZvH,OAAA;MAAKwH,SAAS,EAAC,4CAA4C;MAAAD,QAAA,gBAEzDvH,OAAA;QAAKwH,SAAS,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGpC5H,OAAA;QAAKwH,SAAS,EAAC,kCAAkC;QAAAD,QAAA,gBAC/CvH,OAAA;UAAKwH,SAAS,EAAC;QAAgF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtG5H,OAAA;UAAKwH,SAAS,EAAC;QAAkF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxG5H,OAAA;UAAKwH,SAAS,EAAC;QAA2H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9I,CAAC,eAGN5H,OAAA;QAAKwH,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAE5BvH,OAAA;UAAQwH,SAAS,EAAC,qDAAqD;UAAAD,QAAA,gBACrEvH,OAAA;YAAKwH,SAAS,EAAC,gCAAgC;YAAAD,QAAA,eAC7CvH,OAAA;cAAKwH,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBAErDvH,OAAA;gBAAKwH,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,gBACtCvH,OAAA;kBAAKwH,SAAS,EAAC,6JAA6J;kBAAAD,QAAA,eAC1KvH,OAAA,CAACT,IAAI;oBAACiI,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACN5H,OAAA;kBAAAuH,QAAA,gBACEvH,OAAA;oBAAIwH,SAAS,EAAC,wGAAwG;oBAAAD,QAAA,EAAC;kBAEvH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5H,OAAA;oBAAGwH,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5H,OAAA;gBAAKwH,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,gBAChDvH,OAAA;kBAAKwH,SAAS,EAAC,0EAA0E;kBAAAD,QAAA,eACvFvH,OAAA,CAAClB,sBAAsB;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACN5H,OAAA;kBAAKwH,SAAS,EAAC,mFAAmF;kBAAAD,QAAA,gBAChGvH,OAAA;oBACE6H,OAAO,EAAEA,CAAA,KAAMnH,aAAa,CAAC,WAAW,CAAE;oBAC1C8G,SAAS,EAAE,wEACT/G,UAAU,KAAK,WAAW,GACtB,gGAAgG,GAChG,kDAAkD,EACrD;oBAAA8G,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5H,OAAA;oBACE6H,OAAO,EAAEA,CAAA,KAAMnH,aAAa,CAAC,eAAe,CAAE;oBAC9C8G,SAAS,EAAE,wEACT/G,UAAU,KAAK,eAAe,GAC1B,gGAAgG,GAChG,kDAAkD,EACrD;oBAAA8G,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5H,OAAA;oBACE6H,OAAO,EAAEA,CAAA,KAAMnH,aAAa,CAAC,YAAY,CAAE;oBAC3C8G,SAAS,EAAE,wEACT/G,UAAU,KAAK,YAAY,GACvB,gGAAgG,GAChG,kDAAkD,EACrD;oBAAA8G,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5H,OAAA;gBAAKwH,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,GAC/CpH,QAAQ,CAACiE,MAAM,KAAK,CAAC,iBACpBpE,OAAA;kBACE6H,OAAO,EAAEpB,YAAa;kBACtBe,SAAS,EAAC,qLAAqL;kBAAAD,QAAA,gBAE/LvH,OAAA,CAACR,QAAQ;oBAACgI,SAAS,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,QAE9C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACD5H,OAAA;kBACE6H,OAAO,EAAEA,CAAA,KAAM;oBACbnH,aAAa,CAAC,WAAW,CAAC;oBAC1B,IAAI,CAACG,cAAc,CAACiH,WAAW,EAAE;sBAC/BlG,oBAAoB,CAAC,YAAY,IAAII,IAAI,CAAC,CAAC,CAAC+F,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;oBACxE;kBACF,CAAE;kBACFP,SAAS,EAAC,4PAA4P;kBAAAD,QAAA,gBAEtQvH,OAAA,CAACV,IAAI;oBAACkI,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGN5H,OAAA;gBAAKwH,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eAC1CvH,OAAA;kBACE6H,OAAO,EAAEA,CAAA,KAAMjH,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBACtD6G,SAAS,EAAC,2GAA2G;kBACrH,iBAAe7G,gBAAiB;kBAAA4G,QAAA,gBAEhCvH,OAAA;oBAAMwH,SAAS,EAAC,SAAS;oBAAAD,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC9CjH,gBAAgB,gBACfX,OAAA,CAACF,CAAC;oBAAC0H,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpC5H,OAAA,CAACH,IAAI;oBAAC2H,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5H,OAAA;YAAKwH,SAAS,EAAE,aAAa7G,gBAAgB,GAAG,OAAO,GAAG,QAAQ,EAAG;YAAA4G,QAAA,eACnEvH,OAAA;cAAKwH,SAAS,EAAC,kHAAkH;cAAAD,QAAA,gBAE/HvH,OAAA;gBAAKwH,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,gBACtCvH,OAAA;kBACE6H,OAAO,EAAEA,CAAA,KAAM;oBACbnH,aAAa,CAAC,WAAW,CAAC;oBAC1BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACF4G,SAAS,EAAE,0FACT/G,UAAU,KAAK,WAAW,GACtB,gGAAgG,GAChG,yEAAyE,EAC5E;kBAAA8G,QAAA,gBAEHvH,OAAA,CAACT,IAAI;oBAACiI,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5H,OAAA;kBACE6H,OAAO,EAAEA,CAAA,KAAM;oBACbnH,aAAa,CAAC,eAAe,CAAC;oBAC9BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACF4G,SAAS,EAAE,0FACT/G,UAAU,KAAK,eAAe,GAC1B,gGAAgG,GAChG,yEAAyE,EAC5E;kBAAA8G,QAAA,gBAEHvH,OAAA,CAACP,GAAG;oBAAC+H,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5H,OAAA;kBACE6H,OAAO,EAAEA,CAAA,KAAM;oBACbnH,aAAa,CAAC,YAAY,CAAC;oBAC3BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACF4G,SAAS,EAAE,0FACT/G,UAAU,KAAK,YAAY,GACvB,gGAAgG,GAChG,yEAAyE,EAC5E;kBAAA8G,QAAA,gBAEHvH,OAAA,CAACX,UAAU;oBAACmI,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAEzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGN5H,OAAA;gBAAKwH,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,eAC5CvH,OAAA;kBAAKwH,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,GACrCpH,QAAQ,CAACiE,MAAM,KAAK,CAAC,iBACpBpE,OAAA;oBACE6H,OAAO,EAAEA,CAAA,KAAM;sBACbpB,YAAY,CAAC,CAAC;sBACd7F,mBAAmB,CAAC,KAAK,CAAC;oBAC5B,CAAE;oBACF4G,SAAS,EAAC,uMAAuM;oBAAAD,QAAA,gBAEjNvH,OAAA,CAACR,QAAQ;sBAACgI,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,eACD5H,OAAA;oBACE6H,OAAO,EAAEA,CAAA,KAAM;sBACbnH,aAAa,CAAC,WAAW,CAAC;sBAC1BE,mBAAmB,CAAC,KAAK,CAAC;sBAC1B,IAAI,CAACC,cAAc,CAACiH,WAAW,EAAE;wBAC/BlG,oBAAoB,CAAC,YAAY,IAAII,IAAI,CAAC,CAAC,CAAC+F,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;sBACxE;oBACF,CAAE;oBACFP,SAAS,EAAC,4PAA4P;oBAAAD,QAAA,gBAEtQvH,OAAA,CAACV,IAAI;sBAACkI,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAE9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAGT5H,OAAA;oBAAKwH,SAAS,EAAC,oFAAoF;oBAAAD,QAAA,eACjGvH,OAAA;sBAAKwH,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCvH,OAAA;wBAAMwH,SAAS,EAAC,4BAA4B;wBAAAD,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnE5H,OAAA,CAAClB,sBAAsB;wBAAA2I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAOX5H,OAAA;UAAMwH,SAAS,EAAC,oDAAoD;UAAAD,QAAA,eAClEvH,OAAA;YAAKwH,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBAGvFvH,OAAA;cAAKwH,SAAS,EAAC,eAAe;cAAAD,QAAA,GAE3B9G,UAAU,KAAK,WAAW,iBACzBT,OAAA;gBAAKwH,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnDvH,OAAA;kBAAKwH,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3CvH,OAAA;oBAAKwH,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtCvH,OAAA;sBAAKwH,SAAS,EAAC,6JAA6J;sBAAAD,QAAA,eAC1KvH,OAAA,CAACT,IAAI;wBAACiI,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACN5H,OAAA;sBAAAuH,QAAA,gBACEvH,OAAA;wBAAIwH,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAiB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzE5H,OAAA;wBAAGwH,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,EAAC;sBAAgC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKwH,SAAS,EAAC,oEAAoE;kBAAAD,QAAA,EAChF,CAAC1G,cAAc,CAACiH,WAAW,gBAC1B9H,OAAA;oBAAKwH,SAAS,EAAC,yDAAyD;oBAAAD,QAAA,gBAEtEvH,OAAA;sBAAKwH,SAAS,EAAC,UAAU;sBAAAD,QAAA,gBACvBvH,OAAA;wBAAKwH,SAAS,EAAC,yLAAyL;wBAAAD,QAAA,eACtMvH,OAAA;0BAAKwH,SAAS,EAAC,4GAA4G;0BAAAD,QAAA,eACzHvH,OAAA,CAACT,IAAI;4BAACiI,SAAS,EAAC;0BAAsB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN5H,OAAA;wBAAKwH,SAAS,EAAC;sBAAsH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrI,CAAC,eAGN5H,OAAA;sBAAKwH,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACxBvH,OAAA;wBAAKwH,SAAS,EAAC,WAAW;wBAAAD,QAAA,gBACxBvH,OAAA;0BAAIwH,SAAS,EAAC,oFAAoF;0BAAAD,QAAA,EAAC;wBAEnG;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACL5H,OAAA;0BAAGwH,SAAS,EAAC,kFAAkF;0BAAAD,QAAA,EAAC;wBAEhG;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eAGN5H,OAAA;wBAAKwH,SAAS,EAAC,4CAA4C;wBAAAD,QAAA,gBACzDvH,OAAA;0BAAKwH,SAAS,EAAC,6BAA6B;0BAAAD,QAAA,gBAC1CvH,OAAA;4BAAKwH,SAAS,EAAC,mMAAmM;4BAAAD,QAAA,eAChNvH,OAAA,CAACP,GAAG;8BAAC+H,SAAS,EAAC;4BAAuB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC,eACN5H,OAAA;4BAAGwH,SAAS,EAAC,iDAAiD;4BAAAD,QAAA,GAAC,aAAW,eAAAvH,OAAA;8BAAAyH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,mBAAe;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChG,CAAC,eACN5H,OAAA;0BAAKwH,SAAS,EAAC,6BAA6B;0BAAAD,QAAA,gBAC1CvH,OAAA;4BAAKwH,SAAS,EAAC,yMAAyM;4BAAAD,QAAA,eACtNvH,OAAA,CAACX,UAAU;8BAACmI,SAAS,EAAC;4BAAyB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C,CAAC,eACN5H,OAAA;4BAAGwH,SAAS,EAAC,iDAAiD;4BAAAD,QAAA,GAAC,mBAAY,eAAAvH,OAAA;8BAAAyH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAAM;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxF,CAAC,eACN5H,OAAA;0BAAKwH,SAAS,EAAC,6BAA6B;0BAAAD,QAAA,gBAC1CvH,OAAA;4BAAKwH,SAAS,EAAC,yMAAyM;4BAAAD,QAAA,eACtNvH,OAAA,CAACN,QAAQ;8BAAC8H,SAAS,EAAC;4BAAyB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7C,CAAC,eACN5H,OAAA;4BAAGwH,SAAS,EAAC,iDAAiD;4BAAAD,QAAA,GAAC,oBAAQ,eAAAvH,OAAA;8BAAAyH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,cAAU;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGN5H,OAAA;sBACE6H,OAAO,EAAEA,CAAA,KAAMjG,oBAAoB,CAAC,YAAY,IAAII,IAAI,CAAC,CAAC,CAAC+F,cAAc,CAAC,OAAO,CAAC,EAAE,CAAE;sBACtFP,SAAS,EAAC,wVAAwV;sBAAAD,QAAA,gBAElWvH,OAAA;wBAAKwH,SAAS,EAAC;sBAA2J;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7K5H,OAAA,CAACV,IAAI;wBAACkI,SAAS,EAAC;sBAAiE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpF5H,OAAA;wBAAAuH,QAAA,EAAM;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEN5H,OAAA;oBAAKwH,SAAS,EAAC,yDAAyD;oBAAAD,QAAA,gBAEtEvH,OAAA;sBAAKwH,SAAS,EAAC,UAAU;sBAAAD,QAAA,gBACvBvH,OAAA;wBAAKwH,SAAS,EAAC,kMAAkM;wBAAAD,QAAA,eAC/MvH,OAAA;0BAAKwH,SAAS,EAAC,wGAAwG;0BAAAD,QAAA,eACrHvH,OAAA,CAACT,IAAI;4BAACiI,SAAS,EAAC;0BAAqC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN5H,OAAA;wBAAKwH,SAAS,EAAC;sBAAgI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAGlJ5H,OAAA;wBAAKwH,SAAS,EAAC,0BAA0B;wBAAAD,QAAA,eACvCvH,OAAA;0BAAKwH,SAAS,EAAC,UAAU;0BAAAD,QAAA,gBACvBvH,OAAA;4BAAKwH,SAAS,EAAC;0BAA+C;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACjE5H,OAAA;4BAAKwH,SAAS,EAAC;0BAAkE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGN5H,OAAA;sBAAKwH,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACxBvH,OAAA;wBAAKwH,SAAS,EAAC,WAAW;wBAAAD,QAAA,gBACxBvH,OAAA;0BAAIwH,SAAS,EAAC,oFAAoF;0BAAAD,QAAA,EAAC;wBAEnG;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACL5H,OAAA;0BAAGwH,SAAS,EAAC,kFAAkF;0BAAAD,QAAA,EAAC;wBAEhG;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eAGN5H,OAAA;wBAAKwH,SAAS,EAAC,oEAAoE;wBAAAD,QAAA,eACjFvH,OAAA;0BAAKwH,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,gBACrCvH,OAAA;4BAAKwH,SAAS,EAAC,aAAa;4BAAAD,QAAA,gBAC1BvH,OAAA;8BAAKwH,SAAS,EAAC,oCAAoC;8BAAAD,QAAA,GAChDzE,IAAI,CAACC,KAAK,CAAClC,cAAc,CAACuB,QAAQ,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC4F,MAAM,CAACnH,cAAc,CAACuB,QAAQ,GAAG,EAAE,CAAC,CAAC6F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;4BAAA;8BAAAR,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9F,CAAC,eACN5H,OAAA;8BAAKwH,SAAS,EAAC,mCAAmC;8BAAAD,QAAA,EAAC;4BAAM;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5D,CAAC,eACN5H,OAAA;4BAAKwH,SAAS,EAAC,aAAa;4BAAAD,QAAA,gBAC1BvH,OAAA;8BAAKwH,SAAS,EAAC,iDAAiD;8BAAAD,QAAA,eAC9DvH,OAAA,CAACf,sBAAsB;gCAAC4B,cAAc,EAAEA;8BAAe;gCAAA4G,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvD,CAAC,eACN5H,OAAA;8BAAKwH,SAAS,EAAC,mCAAmC;8BAAAD,QAAA,EAAC;4BAAW;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGN5H,OAAA;sBACE6H,OAAO,EAAElF,mBAAoB;sBAC7B6E,SAAS,EAAC,6UAA6U;sBAAAD,QAAA,gBAEvVvH,OAAA;wBAAKwH,SAAS,EAAC;sBAAuJ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACzK5H,OAAA,CAACJ,MAAM;wBAAC4H,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC9B5H,OAAA;wBAAAuH,QAAA,EAAM;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGAnH,UAAU,KAAK,eAAe,iBAC7BT,OAAA;gBAAKwH,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnDvH,OAAA;kBAAKwH,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3CvH,OAAA;oBAAKwH,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtCvH,OAAA;sBAAKwH,SAAS,EAAC,6JAA6J;sBAAAD,QAAA,eAC1KvH,OAAA,CAACP,GAAG;wBAAC+H,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACN5H,OAAA;sBAAAuH,QAAA,gBACEvH,OAAA;wBAAIwH,SAAS,EAAC,kCAAkC;wBAAAD,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrE5H,OAAA;wBAAGwH,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,EAAC;sBAA+C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKwH,SAAS,EAAC,KAAK;kBAAAD,QAAA,eAClBvH,OAAA,CAACjB,oBAAoB;oBACnBoB,QAAQ,EAAEA,QAAS;oBACnB+H,oBAAoB,EAAE9E,wBAAyB;oBAC/C+E,qBAAqB,EAAExD,yBAA0B;oBACjDxD,cAAc,EAAEA,cAAe;oBAC/BM,sBAAsB,EAAEA,sBAAuB;oBAC/C2G,eAAe,EAAE9C,mBAAoB;oBACrC+C,aAAa,EAAEA,CAAA,KAAM3H,aAAa,CAAC,YAAY;kBAAE;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGAnH,UAAU,KAAK,YAAY,iBAC1BT,OAAA;gBAAKwH,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnDvH,OAAA;kBAAKwH,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3CvH,OAAA;oBAAKwH,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtCvH,OAAA;sBAAKwH,SAAS,EAAC,+JAA+J;sBAAAD,QAAA,eAC5KvH,OAAA,CAACX,UAAU;wBAACmI,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACN5H,OAAA;sBAAAuH,QAAA,gBACEvH,OAAA;wBAAIwH,SAAS,EAAC,kCAAkC;wBAAAD,QAAA,EAAC;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChE5H,OAAA;wBAAGwH,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,EAAC;sBAAqD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKwH,SAAS,EAAC,KAAK;kBAAAD,QAAA,eAClBvH,OAAA,CAAChB,4BAA4B;oBAC3BmB,QAAQ,EAAEA,QAAS;oBACnBiI,eAAe,EAAE9C,mBAAoB;oBACrCgD,mBAAmB,EAAEA,CAAA,KAAM5H,aAAa,CAAC,eAAe;kBAAE;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN5H,OAAA;cAAKwH,SAAS,EAAC,eAAe;cAAAD,QAAA,eAC5BvH,OAAA;gBAAKwH,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnDvH,OAAA;kBAAKwH,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3CvH,OAAA;oBAAKwH,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtCvH,OAAA;sBAAKwH,SAAS,EAAC,+JAA+J;sBAAAD,QAAA,eAC5KvH,OAAA,CAACL,IAAI;wBAAC6H,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACN5H,OAAA;sBAAAuH,QAAA,gBACEvH,OAAA;wBAAIwH,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjE5H,OAAA;wBAAGwH,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,GAAC,kBAAgB,EAACpH,QAAQ,CAACiE,MAAM,EAAC,GAAC;sBAAA;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKwH,SAAS,EAAC,KAAK;kBAAAD,QAAA,EACjBpH,QAAQ,CAACiE,MAAM,KAAK,CAAC,gBACpBpE,OAAA;oBAAKwH,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,gBAChCvH,OAAA;sBAAKwH,SAAS,EAAC,mIAAmI;sBAAAD,QAAA,eAChJvH,OAAA,CAACL,IAAI;wBAAC6H,SAAS,EAAC;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACN5H,OAAA;sBAAIwH,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxE5H,OAAA;sBAAGwH,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,gBAEN5H,OAAA,CAACpB,YAAY;oBACXuB,QAAQ,EAAEA,QAAS;oBACnBE,cAAc,EAAEA,cAAe;oBAC/BkI,eAAe,EAAElD,mBAAoB;oBACrC+C,eAAe,EAAE9C,mBAAoB;oBACrCkD,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;oBAC1B/H,UAAU,EAAC;kBAAM;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC1H,EAAA,CA9uBQD,GAAG;EAAA,QAOiFf,gBAAgB,EAYvGC,gBAAgB;AAAA;AAAAsJ,EAAA,GAnBbxI,GAAG;AAgvBZ,eAAeA,GAAG;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}