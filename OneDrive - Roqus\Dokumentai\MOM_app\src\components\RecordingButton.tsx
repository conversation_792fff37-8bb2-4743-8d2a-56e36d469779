import React from 'react';
import { Mic, Square, Pause, Play } from 'lucide-react';
import { RecordingState } from '../types/meeting';

interface RecordingButtonProps {
  recordingState: RecordingState;
  onStartRecording: () => void;
  onStopRecording: () => void;
  onPauseRecording: () => void;
  onResumeRecording: () => void;
}

export const RecordingButton: React.FC<RecordingButtonProps> = ({
  recordingState,
  onStartRecording,
  onStopRecording,
  onPauseRecording,
  onResumeRecording,
}) => {
  const { isRecording, isPaused } = recordingState;

  if (!isRecording) {
    return (
      <button
        onClick={onStartRecording}
        className="flex items-center justify-center w-14 h-14 bg-gradient-to-br from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-red-200/50 border border-white/30 ring-1 ring-white/15 hover:border-red-300/40 pulse-subtle"
      >
        <Mic className="w-6 h-6" />
      </button>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      {isPaused ? (
        <button
          onClick={onResumeRecording}
          className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-green-500 via-green-600 to-emerald-600 hover:from-green-600 hover:via-green-700 hover:to-emerald-700 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-green-200/50 border border-white/30 ring-1 ring-white/15 hover:border-green-300/40"
        >
          <Play className="w-5 h-5 ml-0.5" />
        </button>
      ) : (
        <button
          onClick={onPauseRecording}
          className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-yellow-500 via-yellow-600 to-orange-600 hover:from-yellow-600 hover:via-yellow-700 hover:to-orange-700 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-yellow-200/50 border border-white/30 ring-1 ring-white/15 hover:border-yellow-300/40"
        >
          <Pause className="w-5 h-5" />
        </button>
      )}
      
      <button
        onClick={onStopRecording}
        className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-gray-600 via-gray-700 to-slate-700 hover:from-gray-700 hover:via-gray-800 hover:to-slate-800 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-gray-300/50 border border-white/30 ring-1 ring-white/15 hover:border-gray-400/40"
      >
        <Square className="w-5 h-5" />
      </button>
    </div>
  );
};