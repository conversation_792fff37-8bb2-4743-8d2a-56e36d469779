{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\ShareContacts.tsx\";\nimport React from 'react';\nimport { Plus, Send } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShareContacts = () => {\n  const contacts = [{\n    id: 1,\n    name: '<PERSON>',\n    avatar: 'A'\n  }, {\n    id: 2,\n    name: '<PERSON>',\n    avatar: '<PERSON>'\n  }, {\n    id: 3,\n    name: '<PERSON>',\n    avatar: 'J'\n  }, {\n    id: 4,\n    name: '<PERSON>',\n    avatar: '<PERSON>'\n  }, {\n    id: 5,\n    name: '<PERSON>',\n    avatar: '<PERSON>'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-white font-semibold text-lg mb-4\",\n      children: \"<PERSON><PERSON><PERSON> pokalbiais\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3 mb-6\",\n      children: [contacts.map(contact => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-sm cursor-pointer hover:scale-110 transition-transform\",\n        title: contact.name,\n        children: contact.avatar\n      }, contact.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white/60 hover:text-white hover:bg-white/30 transition-all\",\n        children: /*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center space-x-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200\",\n        children: [/*#__PURE__*/_jsxDEV(Send, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Si\\u0173sti pokalb\\u012F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full bg-white/10 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center space-x-2 hover:bg-white/20 transition-all duration-200 border border-white/20\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Prid\\u0117ti kontakt\\u0105\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 pt-4 border-t border-white/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white font-semibold text-lg\",\n            children: contacts.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Kontaktai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white font-semibold text-lg\",\n            children: \"12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Bendrinti pokalbiai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = ShareContacts;\nexport default ShareContacts;\nvar _c;\n$RefreshReg$(_c, \"ShareContacts\");", "map": {"version": 3, "names": ["React", "Plus", "Send", "jsxDEV", "_jsxDEV", "ShareContacts", "contacts", "id", "name", "avatar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "contact", "title", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ShareContacts.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Plus, Send } from 'lucide-react';\r\n\r\nconst ShareContacts: React.FC = () => {\r\n  const contacts = [\r\n    { id: 1, name: '<PERSON>', avatar: 'A' },\r\n    { id: 2, name: '<PERSON>', avatar: '<PERSON>' },\r\n    { id: 3, name: '<PERSON>', avatar: '<PERSON>' },\r\n    { id: 4, name: '<PERSON>', avatar: '<PERSON>' },\r\n    { id: 5, name: '<PERSON>', avatar: '<PERSON>' },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n      <h2 className=\"text-white font-semibold text-lg mb-4\"><PERSON><PERSON><PERSON> pokalbiais</h2>\r\n      \r\n      {/* Contacts */}\r\n      <div className=\"flex items-center space-x-3 mb-6\">\r\n        {contacts.map((contact) => (\r\n          <div\r\n            key={contact.id}\r\n            className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-sm cursor-pointer hover:scale-110 transition-transform\"\r\n            title={contact.name}\r\n          >\r\n            {contact.avatar}\r\n          </div>\r\n        ))}\r\n        <button className=\"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white/60 hover:text-white hover:bg-white/30 transition-all\">\r\n          <Plus className=\"h-4 w-4\" />\r\n        </button>\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"space-y-3\">\r\n        <button className=\"w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center space-x-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200\">\r\n          <Send className=\"h-4 w-4\" />\r\n          <span>Siųsti pokalbį</span>\r\n        </button>\r\n        \r\n        <button className=\"w-full bg-white/10 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center space-x-2 hover:bg-white/20 transition-all duration-200 border border-white/20\">\r\n          <span>Pridėti kontaktą</span>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Quick Stats */}\r\n      <div className=\"mt-6 pt-4 border-t border-white/20\">\r\n        <div className=\"grid grid-cols-2 gap-4 text-center\">\r\n          <div>\r\n            <div className=\"text-white font-semibold text-lg\">{contacts.length}</div>\r\n            <div className=\"text-white/60 text-sm\">Kontaktai</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-white font-semibold text-lg\">12</div>\r\n            <div className=\"text-white/60 text-sm\">Bendrinti pokalbiai</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ShareContacts; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EACpC,MAAMC,QAAQ,GAAG,CACf;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAI,CAAC,EACnC;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAI,CAAC,EACtC;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAI,CAAC,EACpC;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAI,CAAC,EACpC;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAI,CAAC,CACpC;EAED,oBACEL,OAAA;IAAKM,SAAS,EAAC,qEAAqE;IAAAC,QAAA,gBAClFP,OAAA;MAAIM,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAG9EX,OAAA;MAAKM,SAAS,EAAC,kCAAkC;MAAAC,QAAA,GAC9CL,QAAQ,CAACU,GAAG,CAAEC,OAAO,iBACpBb,OAAA;QAEEM,SAAS,EAAC,4LAA4L;QACtMQ,KAAK,EAAED,OAAO,CAACT,IAAK;QAAAG,QAAA,EAEnBM,OAAO,CAACR;MAAM,GAJVQ,OAAO,CAACV,EAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKZ,CACN,CAAC,eACFX,OAAA;QAAQM,SAAS,EAAC,qIAAqI;QAAAC,QAAA,eACrJP,OAAA,CAACH,IAAI;UAACS,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNX,OAAA;MAAKM,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBP,OAAA;QAAQM,SAAS,EAAC,gNAAgN;QAAAC,QAAA,gBAChOP,OAAA,CAACF,IAAI;UAACQ,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BX,OAAA;UAAAO,QAAA,EAAM;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAETX,OAAA;QAAQM,SAAS,EAAC,gLAAgL;QAAAC,QAAA,eAChMP,OAAA;UAAAO,QAAA,EAAM;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNX,OAAA;MAAKM,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDP,OAAA;QAAKM,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDP,OAAA;UAAAO,QAAA,gBACEP,OAAA;YAAKM,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEL,QAAQ,CAACa;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEX,OAAA;YAAKM,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNX,OAAA;UAAAO,QAAA,gBACEP,OAAA;YAAKM,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1DX,OAAA;YAAKM,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GAxDIf,aAAuB;AA0D7B,eAAeA,aAAa;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}