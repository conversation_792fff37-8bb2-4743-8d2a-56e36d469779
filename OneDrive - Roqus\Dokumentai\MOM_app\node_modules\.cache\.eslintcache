[{"/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/index.tsx": "1", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx": "2", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/utils/demoData.ts": "3", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Button.tsx": "4", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/index.ts": "5", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/hooks/index.ts": "6", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingButton.tsx": "7", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/MeetingsList.tsx": "8", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/WhisperStatusIndicator.tsx": "9", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/AudioPlayer.tsx": "10", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ErrorBoundary.tsx": "11", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TranscriptionManager.tsx": "12", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TranscriptViewer.tsx": "13", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ProfessionalTranscriptViewer.tsx": "14", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/GridControls.tsx": "15", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/WhisperConfig.tsx": "16", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/CollapsibleTranscriptsList.tsx": "17", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingIndicator.tsx": "18", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/CardsSection.tsx": "19", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Sidebar.tsx": "20", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPanel.tsx": "21", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ShareContacts.tsx": "22", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/DynamicAudioVisualizer.tsx": "23", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TransactionHistory.tsx": "24", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ResultsPage.tsx": "25", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPage.tsx": "26", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TranscriptionPage.tsx": "27", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/AnalyticsSection.tsx": "28", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TodosPage.tsx": "29", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/hooks/useAudioRecorder.ts": "30", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/hooks/useTranscription.ts": "31", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/services/whisperService.ts": "32", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/config/whisper.ts": "33", "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/services/speakerService.ts": "34"}, {"size": 274, "mtime": 1753213560441, "results": "35", "hashOfConfig": "36"}, {"size": 18227, "mtime": 1753265907223, "results": "37", "hashOfConfig": "36"}, {"size": 4195, "mtime": 1753267902859, "results": "38", "hashOfConfig": "36"}, {"size": 2013, "mtime": 1753267915266, "results": "39", "hashOfConfig": "36"}, {"size": 1403, "mtime": 1753265448716, "results": "40", "hashOfConfig": "36"}, {"size": 111, "mtime": 1753213559758, "results": "41", "hashOfConfig": "36"}, {"size": 2634, "mtime": 1753267914695, "results": "42", "hashOfConfig": "36"}, {"size": 7878, "mtime": 1753266380491, "results": "43", "hashOfConfig": "36"}, {"size": 866, "mtime": 1753040855623, "results": "44", "hashOfConfig": "36"}, {"size": 7917, "mtime": 1753267915149, "results": "45", "hashOfConfig": "36"}, {"size": 4087, "mtime": 1753267902789, "results": "46", "hashOfConfig": "36"}, {"size": 10383, "mtime": 1753267914419, "results": "47", "hashOfConfig": "36"}, {"size": 7791, "mtime": 1753267898284, "results": "48", "hashOfConfig": "36"}, {"size": 6770, "mtime": 1753267914599, "results": "49", "hashOfConfig": "36"}, {"size": 6020, "mtime": 1753266453644, "results": "50", "hashOfConfig": "36"}, {"size": 4897, "mtime": 1753266737184, "results": "51", "hashOfConfig": "36"}, {"size": 19278, "mtime": 1753267913512, "results": "52", "hashOfConfig": "36"}, {"size": 3217, "mtime": 1753267915002, "results": "53", "hashOfConfig": "36"}, {"size": 2511, "mtime": 1753261816167, "results": "54", "hashOfConfig": "36"}, {"size": 2832, "mtime": 1753266791808, "results": "55", "hashOfConfig": "36"}, {"size": 8575, "mtime": 1753266724673, "results": "56", "hashOfConfig": "36"}, {"size": 2583, "mtime": 1753261873241, "results": "57", "hashOfConfig": "36"}, {"size": 2251, "mtime": 1753215383400, "results": "58", "hashOfConfig": "36"}, {"size": 5481, "mtime": 1753266751474, "results": "59", "hashOfConfig": "36"}, {"size": 7818, "mtime": 1753266192427, "results": "60", "hashOfConfig": "36"}, {"size": 15541, "mtime": 1753265827857, "results": "61", "hashOfConfig": "36"}, {"size": 5834, "mtime": 1753266241692, "results": "62", "hashOfConfig": "36"}, {"size": 3748, "mtime": 1753261837541, "results": "63", "hashOfConfig": "36"}, {"size": 27811, "mtime": 1753268532047, "results": "64", "hashOfConfig": "36"}, {"size": 5762, "mtime": 1753214163617, "results": "65", "hashOfConfig": "36"}, {"size": 10587, "mtime": 1753267902121, "results": "66", "hashOfConfig": "36"}, {"size": 10086, "mtime": 1753267900745, "results": "67", "hashOfConfig": "36"}, {"size": 1243, "mtime": 1753267899234, "results": "68", "hashOfConfig": "36"}, {"size": 9878, "mtime": 1753267902597, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1go3nke", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/index.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Do<PERSON>mentai/MOM_app/src/App.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/utils/demoData.ts", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Button.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/index.ts", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Do<PERSON>mentai/MOM_app/src/hooks/index.ts", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingButton.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/MeetingsList.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/WhisperStatusIndicator.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/AudioPlayer.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ErrorBoundary.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TranscriptionManager.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TranscriptViewer.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ProfessionalTranscriptViewer.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/GridControls.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/WhisperConfig.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/CollapsibleTranscriptsList.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingIndicator.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/CardsSection.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Sidebar.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPanel.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ShareContacts.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/DynamicAudioVisualizer.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TransactionHistory.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ResultsPage.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPage.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TranscriptionPage.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Do<PERSON>mentai/MOM_app/src/components/AnalyticsSection.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TodosPage.tsx", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Do<PERSON>mentai/MOM_app/src/hooks/useAudioRecorder.ts", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Do<PERSON>mentai/MOM_app/src/hooks/useTranscription.ts", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/services/whisperService.ts", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/<PERSON><PERSON>/MOM_app/src/config/whisper.ts", [], [], "/mnt/c/Users/<USER>/OneDrive - Roqus/Do<PERSON>mentai/MOM_app/src/services/speakerService.ts", [], []]