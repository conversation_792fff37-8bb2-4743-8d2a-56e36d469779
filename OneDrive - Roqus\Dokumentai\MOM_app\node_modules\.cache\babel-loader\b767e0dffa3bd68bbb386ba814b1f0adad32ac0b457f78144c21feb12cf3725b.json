{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\TodosPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Plus, Edit, Trash2, X, Check } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TodosPage = () => {\n  _s();\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: 'Pasir<PERSON><PERSON><PERSON> prezentacijai',\n    completed: false,\n    date: '2024-01-20',\n    priority: 'high',\n    project: 'Aexn CRM analitika',\n    owner: '<PERSON>'\n  }, {\n    id: 2,\n    text: 'Susitikimas su klientu',\n    completed: true,\n    date: '2024-01-18',\n    priority: 'medium',\n    project: 'Aexn | Analitika',\n    owner: '<PERSON><PERSON>'\n  }, {\n    id: 3,\n    text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokument<PERSON>',\n    completed: false,\n    date: '2024-01-22',\n    priority: 'low',\n    project: 'Aexn_analitika',\n    owner: '<PERSON><PERSON>'\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false,\n    priority: 'medium',\n    project: 'Aexn_analitika',\n    owner: 'Jonas Jonaitis'\n  }]);\n  const [editingTodo, setEditingTodo] = useState(null);\n  const [editingText, setEditingText] = useState('');\n  const [editingProject, setEditingProject] = useState('');\n  const [editingOwner, setEditingOwner] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState('medium');\n  const [newTodoProject, setNewTodoProject] = useState('');\n  const [newTodoOwner, setNewTodoOwner] = useState('');\n  const [sortType, setSortType] = useState('date_desc');\n  const toggleTodo = id => {\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const deleteTodo = id => {\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n  const startEditingTodo = todo => {\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n    setEditingProject(todo.project || '');\n    setEditingOwner(todo.owner || '');\n  };\n  const saveEditedTodo = () => {\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => todo.id === editingTodo ? {\n        ...todo,\n        text: editingText.trim(),\n        project: editingProject,\n        owner: editingOwner\n      } : todo));\n      setEditingTodo(null);\n      setEditingText('');\n      setEditingProject('');\n      setEditingOwner('');\n    }\n  };\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n    setEditingProject('');\n    setEditingOwner('');\n  };\n  const addNewTodo = () => {\n    if (newTodoText.trim()) {\n      const newTodo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority,\n        project: newTodoProject,\n        owner: newTodoOwner\n      };\n      setTodos(prev => [...prev, newTodo]);\n      setNewTodoText('');\n      setNewTodoDate('');\n      setNewTodoPriority('medium');\n      setNewTodoProject('');\n      setNewTodoOwner('');\n      setShowNewTodoForm(false);\n    }\n  };\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n    setNewTodoProject('');\n    setNewTodoOwner('');\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-400';\n      case 'medium':\n        return 'text-yellow-400';\n      case 'low':\n        return 'text-green-400';\n      default:\n        return 'text-white/60';\n    }\n  };\n  const sortedTodos = [...todos].sort((a, b) => {\n    if (sortType === 'date_desc') {\n      if (!a.date) return 1;\n      if (!b.date) return -1;\n      return b.date.localeCompare(a.date);\n    }\n    if (sortType === 'date_asc') {\n      if (!a.date) return 1;\n      if (!b.date) return -1;\n      return a.date.localeCompare(b.date);\n    }\n    if (sortType === 'alpha_asc') {\n      return a.text.localeCompare(b.text, 'lt');\n    }\n    if (sortType === 'alpha_desc') {\n      return b.text.localeCompare(a.text, 'lt');\n    }\n    return 0;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",\n          children: /*#__PURE__*/_jsxDEV(Check, {\n            className: \"h-6 w-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-white\",\n            children: \"Susitikimo u\\u017Eduotys\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-white/60\",\n            children: \"Tvarkykite, planuokite ir sekite susitikimo u\\u017Eduotis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"U\\u017Eduo\\u010Di\\u0173 s\\u0105ra\\u0161as\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowNewTodoForm(true),\n          className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\",\n          children: /*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-4 w-4 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 flex gap-2 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"sort-todos\",\n          className: \"text-white/60 text-sm\",\n          children: \"R\\u016B\\u0161iuoti:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          id: \"sort-todos\",\n          value: sortType,\n          onChange: e => setSortType(e.target.value),\n          className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white focus:outline-none focus:border-blue-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"date_desc\",\n            children: \"Pagal dat\\u0105 (naujausios vir\\u0161uje)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"date_asc\",\n            children: \"Pagal dat\\u0105 (seniausios vir\\u0161uje)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"alpha_asc\",\n            children: \"Pagal ab\\u0117c\\u0117l\\u0119 (A\\u2013Z)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"alpha_desc\",\n            children: \"Pagal ab\\u0117c\\u0117l\\u0119 (Z\\u2013A)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), showNewTodoForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-4 bg-white/5 rounded-xl border border-white/10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newTodoText,\n            onChange: e => setNewTodoText(e.target.value),\n            placeholder: \"\\u012Eveskite u\\u017Eduot\\u012F...\",\n            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\",\n            onKeyPress: e => e.key === 'Enter' && addNewTodo()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: newTodoDate,\n              onChange: e => setNewTodoDate(e.target.value),\n              className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: newTodoPriority,\n              onChange: e => setNewTodoPriority(e.target.value),\n              className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"low\",\n                children: \"\\u017Demas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"medium\",\n                children: \"Vidutinis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"high\",\n                children: \"Auk\\u0161tas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoProject,\n              onChange: e => setNewTodoProject(e.target.value),\n              placeholder: \"Projekto pavadinimas\",\n              className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoOwner,\n              onChange: e => setNewTodoOwner(e.target.value),\n              placeholder: \"Atsakingas asmuo\",\n              className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: addNewTodo,\n              className: \"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Check, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), \"Prid\\u0117ti\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: cancelNewTodo,\n              className: \"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(X, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), \"At\\u0161aukti\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full text-sm text-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"bg-white/10\",\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-3 py-2 text-left\",\n                children: \"Pavadinimas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-3 py-2 text-left\",\n                children: \"Projekto pavadinimas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-3 py-2 text-left\",\n                children: \"Atsakingas asmuo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-3 py-2 text-left\",\n                children: \"Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-3 py-2 text-left\",\n                children: \"Prioritetas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-3 py-2 text-left\",\n                children: \"B\\u016Bsena\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-3 py-2 text-left\",\n                children: \"Veiksmai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: sortedTodos.map(todo => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"border-b border-white/10 hover:bg-white/5\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-3 py-2\",\n                children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: editingText,\n                  onChange: e => setEditingText(e.target.value),\n                  className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 23\n                }, this) : todo.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-3 py-2\",\n                children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: editingProject,\n                  onChange: e => setEditingProject(e.target.value),\n                  className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 23\n                }, this) : todo.project\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-3 py-2\",\n                children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: editingOwner,\n                  onChange: e => setEditingOwner(e.target.value),\n                  className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this) : todo.owner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-3 py-2\",\n                children: todo.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-3 py-2\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`,\n                  children: todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-3 py-2\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: todo.completed,\n                  onChange: () => toggleTodo(todo.id),\n                  className: \"accent-green-500\",\n                  disabled: editingTodo === todo.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-3 py-2 flex gap-1\",\n                children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: saveEditedTodo,\n                    className: \"p-1 text-green-400 hover:text-green-600\",\n                    children: /*#__PURE__*/_jsxDEV(Check, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 110\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: cancelEditing,\n                    className: \"p-1 text-red-400 hover:text-red-600\",\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 105\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startEditingTodo(todo),\n                    className: \"p-1 text-white/60 hover:text-white\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 119\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteTodo(todo.id),\n                    className: \"p-1 text-white/60 hover:text-red-400\",\n                    children: /*#__PURE__*/_jsxDEV(Trash2, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 118\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, todo.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 pt-4 border-t border-white/10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/60 text-sm\",\n          children: [\"U\\u017Ebaigta: \", todos.filter(t => t.completed).length, \" i\\u0161 \", todos.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(TodosPage, \"T3EXOLO7hTfMgQJRr2OlNJLcEB4=\");\n_c = TodosPage;\nexport default TodosPage;\nvar _c;\n$RefreshReg$(_c, \"TodosPage\");", "map": {"version": 3, "names": ["React", "useState", "Plus", "Edit", "Trash2", "X", "Check", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TodosPage", "_s", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "date", "priority", "project", "owner", "editingTodo", "setEditingTodo", "editingText", "setEditingText", "editingProject", "setEditingProject", "<PERSON><PERSON><PERSON><PERSON>", "setEditingOwner", "newTodoText", "setNewTodoText", "showNewTodoForm", "setShowNewTodoForm", "newTodoDate", "setNewTodoDate", "newTodoPriority", "setNewTodoPriority", "newTodoProject", "setNewTodoProject", "newTodoOwner", "setNewTodoOwner", "sortType", "setSortType", "toggleTodo", "prev", "map", "todo", "deleteTodo", "filter", "startEditingTodo", "saveEditedTodo", "trim", "cancelEditing", "addNewTodo", "newTodo", "Date", "now", "undefined", "cancelNewTodo", "getPriorityColor", "sortedTodos", "sort", "a", "b", "localeCompare", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "htmlFor", "value", "onChange", "e", "target", "type", "placeholder", "onKeyPress", "key", "checked", "disabled", "t", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TodosPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Plus, Edit, Trash2, Calendar, X, Check } from 'lucide-react';\r\n\r\ninterface Todo {\r\n  id: number;\r\n  text: string;\r\n  completed: boolean;\r\n  date?: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n  project?: string;\r\n  owner?: string;\r\n}\r\n\r\nconst TodosPage: React.FC = () => {\r\n  const [todos, setTodos] = useState<Todo[]>([\r\n    { id: 1, text: '<PERSON><PERSON><PERSON><PERSON><PERSON> prezenta<PERSON>', completed: false, date: '2024-01-20', priority: 'high', project: 'Aexn CRM analitika', owner: '<PERSON>' },\r\n    { id: 2, text: 'Susi<PERSON><PERSON><PERSON> su klientu', completed: true, date: '2024-01-18', priority: 'medium', project: 'Aexn | Ana<PERSON><PERSON>', owner: '<PERSON><PERSON>' },\r\n    { id: 3, text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokumentus', completed: false, date: '2024-01-22', priority: 'low', project: 'Aexn_analitika', owner: '<PERSON><PERSON>' },\r\n    { id: 4, text: '<PERSON><PERSON>ti kitą savait<PERSON>', completed: false, priority: 'medium', project: 'Aexn_analitika', owner: '<PERSON>' }\r\n  ]);\r\n  const [editingTodo, setEditingTodo] = useState<number | null>(null);\r\n  const [editingText, setEditingText] = useState('');\r\n  const [editingProject, setEditingProject] = useState('');\r\n  const [editingOwner, setEditingOwner] = useState('');\r\n  const [newTodoText, setNewTodoText] = useState('');\r\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\r\n  const [newTodoDate, setNewTodoDate] = useState('');\r\n  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');\r\n  const [newTodoProject, setNewTodoProject] = useState('');\r\n  const [newTodoOwner, setNewTodoOwner] = useState('');\r\n  const [sortType, setSortType] = useState<'date_desc' | 'date_asc' | 'alpha_asc' | 'alpha_desc'>('date_desc');\r\n\r\n  const toggleTodo = (id: number) => {\r\n    setTodos(prev => prev.map(todo => \r\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\r\n    ));\r\n  };\r\n  const deleteTodo = (id: number) => {\r\n    setTodos(prev => prev.filter(todo => todo.id !== id));\r\n  };\r\n  const startEditingTodo = (todo: Todo) => {\r\n    setEditingTodo(todo.id);\r\n    setEditingText(todo.text);\r\n    setEditingProject(todo.project || '');\r\n    setEditingOwner(todo.owner || '');\r\n  };\r\n  const saveEditedTodo = () => {\r\n    if (editingTodo && editingText.trim()) {\r\n      setTodos(prev => prev.map(todo => \r\n        todo.id === editingTodo ? { ...todo, text: editingText.trim(), project: editingProject, owner: editingOwner } : todo\r\n      ));\r\n      setEditingTodo(null);\r\n      setEditingText('');\r\n      setEditingProject('');\r\n      setEditingOwner('');\r\n    }\r\n  };\r\n  const cancelEditing = () => {\r\n    setEditingTodo(null);\r\n    setEditingText('');\r\n    setEditingProject('');\r\n    setEditingOwner('');\r\n  };\r\n  const addNewTodo = () => {\r\n    if (newTodoText.trim()) {\r\n      const newTodo: Todo = {\r\n        id: Date.now(),\r\n        text: newTodoText.trim(),\r\n        completed: false,\r\n        date: newTodoDate || undefined,\r\n        priority: newTodoPriority,\r\n        project: newTodoProject,\r\n        owner: newTodoOwner\r\n      };\r\n      setTodos(prev => [...prev, newTodo]);\r\n      setNewTodoText('');\r\n      setNewTodoDate('');\r\n      setNewTodoPriority('medium');\r\n      setNewTodoProject('');\r\n      setNewTodoOwner('');\r\n      setShowNewTodoForm(false);\r\n    }\r\n  };\r\n  const cancelNewTodo = () => {\r\n    setShowNewTodoForm(false);\r\n    setNewTodoText('');\r\n    setNewTodoDate('');\r\n    setNewTodoPriority('medium');\r\n    setNewTodoProject('');\r\n    setNewTodoOwner('');\r\n  };\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'text-red-400';\r\n      case 'medium': return 'text-yellow-400';\r\n      case 'low': return 'text-green-400';\r\n      default: return 'text-white/60';\r\n    }\r\n  };\r\n  const sortedTodos = [...todos].sort((a, b) => {\r\n    if (sortType === 'date_desc') {\r\n      if (!a.date) return 1;\r\n      if (!b.date) return -1;\r\n      return b.date.localeCompare(a.date);\r\n    }\r\n    if (sortType === 'date_asc') {\r\n      if (!a.date) return 1;\r\n      if (!b.date) return -1;\r\n      return a.date.localeCompare(b.date);\r\n    }\r\n    if (sortType === 'alpha_asc') {\r\n      return a.text.localeCompare(b.text, 'lt');\r\n    }\r\n    if (sortType === 'alpha_desc') {\r\n      return b.text.localeCompare(a.text, 'lt');\r\n    }\r\n    return 0;\r\n  });\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\r\n            <Check className=\"h-6 w-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-white\">Susitikimo užduotys</h2>\r\n            <p className=\"text-sm text-white/60\">Tvarkykite, planuokite ir sekite susitikimo užduotis</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <h3 className=\"text-lg font-semibold text-white\">Užduočių sąrašas</h3>\r\n          </div>\r\n          <button\r\n            onClick={() => setShowNewTodoForm(true)}\r\n            className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\"\r\n          >\r\n            <Plus className=\"h-4 w-4 text-white\" />\r\n          </button>\r\n        </div>\r\n        <div className=\"mb-4 flex gap-2 items-center\">\r\n          <label htmlFor=\"sort-todos\" className=\"text-white/60 text-sm\">Rūšiuoti:</label>\r\n          <select\r\n            id=\"sort-todos\"\r\n            value={sortType}\r\n            onChange={e => setSortType(e.target.value as any)}\r\n            className=\"bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n          >\r\n            <option value=\"date_desc\">Pagal datą (naujausios viršuje)</option>\r\n            <option value=\"date_asc\">Pagal datą (seniausios viršuje)</option>\r\n            <option value=\"alpha_asc\">Pagal abėcėlę (A–Z)</option>\r\n            <option value=\"alpha_desc\">Pagal abėcėlę (Z–A)</option>\r\n          </select>\r\n        </div>\r\n        {showNewTodoForm && (\r\n          <div className=\"mb-4 p-4 bg-white/5 rounded-xl border border-white/10\">\r\n            <div className=\"space-y-3\">\r\n              <input\r\n                type=\"text\"\r\n                value={newTodoText}\r\n                onChange={(e) => setNewTodoText(e.target.value)}\r\n                placeholder=\"Įveskite užduotį...\"\r\n                className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                onKeyPress={(e) => e.key === 'Enter' && addNewTodo()}\r\n              />\r\n              <div className=\"flex gap-2\">\r\n                <input\r\n                  type=\"date\"\r\n                  value={newTodoDate}\r\n                  onChange={(e) => setNewTodoDate(e.target.value)}\r\n                  className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                />\r\n                <select\r\n                  value={newTodoPriority}\r\n                  onChange={(e) => setNewTodoPriority(e.target.value as 'low' | 'medium' | 'high')}\r\n                  className=\"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                >\r\n                  <option value=\"low\">Žemas</option>\r\n                  <option value=\"medium\">Vidutinis</option>\r\n                  <option value=\"high\">Aukštas</option>\r\n                </select>\r\n              </div>\r\n              <div className=\"flex gap-2\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoProject}\r\n                  onChange={e => setNewTodoProject(e.target.value)}\r\n                  placeholder=\"Projekto pavadinimas\"\r\n                  className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                />\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoOwner}\r\n                  onChange={e => setNewTodoOwner(e.target.value)}\r\n                  placeholder=\"Atsakingas asmuo\"\r\n                  className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                />\r\n              </div>\r\n              <div className=\"flex gap-2\">\r\n                <button\r\n                  onClick={addNewTodo}\r\n                  className=\"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                >\r\n                  <Check className=\"h-4 w-4\" />\r\n                  Pridėti\r\n                </button>\r\n                <button\r\n                  onClick={cancelNewTodo}\r\n                  className=\"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                >\r\n                  <X className=\"h-4 w-4\" />\r\n                  Atšaukti\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full text-sm text-white\">\r\n            <thead>\r\n              <tr className=\"bg-white/10\">\r\n                <th className=\"px-3 py-2 text-left\">Pavadinimas</th>\r\n                <th className=\"px-3 py-2 text-left\">Projekto pavadinimas</th>\r\n                <th className=\"px-3 py-2 text-left\">Atsakingas asmuo</th>\r\n                <th className=\"px-3 py-2 text-left\">Data</th>\r\n                <th className=\"px-3 py-2 text-left\">Prioritetas</th>\r\n                <th className=\"px-3 py-2 text-left\">Būsena</th>\r\n                <th className=\"px-3 py-2 text-left\">Veiksmai</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {sortedTodos.map((todo) => (\r\n                <tr key={todo.id} className=\"border-b border-white/10 hover:bg-white/5\">\r\n                  <td className=\"px-3 py-2\">\r\n                    {editingTodo === todo.id ? (\r\n                      <input\r\n                        type=\"text\"\r\n                        value={editingText}\r\n                        onChange={e => setEditingText(e.target.value)}\r\n                        className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                      />\r\n                    ) : (\r\n                      todo.text\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-3 py-2\">\r\n                    {editingTodo === todo.id ? (\r\n                      <input\r\n                        type=\"text\"\r\n                        value={editingProject}\r\n                        onChange={e => setEditingProject(e.target.value)}\r\n                        className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                      />\r\n                    ) : (\r\n                      todo.project\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-3 py-2\">\r\n                    {editingTodo === todo.id ? (\r\n                      <input\r\n                        type=\"text\"\r\n                        value={editingOwner}\r\n                        onChange={e => setEditingOwner(e.target.value)}\r\n                        className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                      />\r\n                    ) : (\r\n                      todo.owner\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-3 py-2\">{todo.date}</td>\r\n                  <td className=\"px-3 py-2\">\r\n                    <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`}>\r\n                      {todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"px-3 py-2\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={todo.completed}\r\n                      onChange={() => toggleTodo(todo.id)}\r\n                      className=\"accent-green-500\"\r\n                      disabled={editingTodo === todo.id}\r\n                    />\r\n                  </td>\r\n                  <td className=\"px-3 py-2 flex gap-1\">\r\n                    {editingTodo === todo.id ? (\r\n                      <>\r\n                        <button onClick={saveEditedTodo} className=\"p-1 text-green-400 hover:text-green-600\"><Check className=\"h-4 w-4\" /></button>\r\n                        <button onClick={cancelEditing} className=\"p-1 text-red-400 hover:text-red-600\"><X className=\"h-4 w-4\" /></button>\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <button onClick={() => startEditingTodo(todo)} className=\"p-1 text-white/60 hover:text-white\"><Edit className=\"h-4 w-4\" /></button>\r\n                        <button onClick={() => deleteTodo(todo.id)} className=\"p-1 text-white/60 hover:text-red-400\"><Trash2 className=\"h-4 w-4\" /></button>\r\n                      </>\r\n                    )}\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n          <div className=\"text-white/60 text-sm\">\r\n            Užbaigta: {todos.filter(t => t.completed).length} iš {todos.length}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TodosPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAYC,CAAC,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAYtE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAS,CACzC;IAAEc,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,MAAM;IAAEC,OAAO,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC3J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC1J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,KAAK;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAe,CAAC,EACjJ;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEE,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACnI,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAA4B,QAAQ,CAAC;EAC3F,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAwD,WAAW,CAAC;EAE5G,MAAM2C,UAAU,GAAI7B,EAAU,IAAK;IACjCD,QAAQ,CAAC+B,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAAChC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGgC,IAAI;MAAE9B,SAAS,EAAE,CAAC8B,IAAI,CAAC9B;IAAU,CAAC,GAAG8B,IAC7D,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,UAAU,GAAIjC,EAAU,IAAK;IACjCD,QAAQ,CAAC+B,IAAI,IAAIA,IAAI,CAACI,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAChC,EAAE,KAAKA,EAAE,CAAC,CAAC;EACvD,CAAC;EACD,MAAMmC,gBAAgB,GAAIH,IAAU,IAAK;IACvCxB,cAAc,CAACwB,IAAI,CAAChC,EAAE,CAAC;IACvBU,cAAc,CAACsB,IAAI,CAAC/B,IAAI,CAAC;IACzBW,iBAAiB,CAACoB,IAAI,CAAC3B,OAAO,IAAI,EAAE,CAAC;IACrCS,eAAe,CAACkB,IAAI,CAAC1B,KAAK,IAAI,EAAE,CAAC;EACnC,CAAC;EACD,MAAM8B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI7B,WAAW,IAAIE,WAAW,CAAC4B,IAAI,CAAC,CAAC,EAAE;MACrCtC,QAAQ,CAAC+B,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAAChC,EAAE,KAAKO,WAAW,GAAG;QAAE,GAAGyB,IAAI;QAAE/B,IAAI,EAAEQ,WAAW,CAAC4B,IAAI,CAAC,CAAC;QAAEhC,OAAO,EAAEM,cAAc;QAAEL,KAAK,EAAEO;MAAa,CAAC,GAAGmB,IAClH,CAAC,CAAC;MACFxB,cAAc,CAAC,IAAI,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC;EACD,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1B9B,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;IAClBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAMyB,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIxB,WAAW,CAACsB,IAAI,CAAC,CAAC,EAAE;MACtB,MAAMG,OAAa,GAAG;QACpBxC,EAAE,EAAEyC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdzC,IAAI,EAAEc,WAAW,CAACsB,IAAI,CAAC,CAAC;QACxBnC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAEgB,WAAW,IAAIwB,SAAS;QAC9BvC,QAAQ,EAAEiB,eAAe;QACzBhB,OAAO,EAAEkB,cAAc;QACvBjB,KAAK,EAAEmB;MACT,CAAC;MACD1B,QAAQ,CAAC+B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEU,OAAO,CAAC,CAAC;MACpCxB,cAAc,CAAC,EAAE,CAAC;MAClBI,cAAc,CAAC,EAAE,CAAC;MAClBE,kBAAkB,CAAC,QAAQ,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;MACnBR,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EACD,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B1B,kBAAkB,CAAC,KAAK,CAAC;IACzBF,cAAc,CAAC,EAAE,CAAC;IAClBI,cAAc,CAAC,EAAE,CAAC;IAClBE,kBAAkB,CAAC,QAAQ,CAAC;IAC5BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAMmB,gBAAgB,GAAIzC,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EACD,MAAM0C,WAAW,GAAG,CAAC,GAAGhD,KAAK,CAAC,CAACiD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC5C,IAAItB,QAAQ,KAAK,WAAW,EAAE;MAC5B,IAAI,CAACqB,CAAC,CAAC7C,IAAI,EAAE,OAAO,CAAC;MACrB,IAAI,CAAC8C,CAAC,CAAC9C,IAAI,EAAE,OAAO,CAAC,CAAC;MACtB,OAAO8C,CAAC,CAAC9C,IAAI,CAAC+C,aAAa,CAACF,CAAC,CAAC7C,IAAI,CAAC;IACrC;IACA,IAAIwB,QAAQ,KAAK,UAAU,EAAE;MAC3B,IAAI,CAACqB,CAAC,CAAC7C,IAAI,EAAE,OAAO,CAAC;MACrB,IAAI,CAAC8C,CAAC,CAAC9C,IAAI,EAAE,OAAO,CAAC,CAAC;MACtB,OAAO6C,CAAC,CAAC7C,IAAI,CAAC+C,aAAa,CAACD,CAAC,CAAC9C,IAAI,CAAC;IACrC;IACA,IAAIwB,QAAQ,KAAK,WAAW,EAAE;MAC5B,OAAOqB,CAAC,CAAC/C,IAAI,CAACiD,aAAa,CAACD,CAAC,CAAChD,IAAI,EAAE,IAAI,CAAC;IAC3C;IACA,IAAI0B,QAAQ,KAAK,YAAY,EAAE;MAC7B,OAAOsB,CAAC,CAAChD,IAAI,CAACiD,aAAa,CAACF,CAAC,CAAC/C,IAAI,EAAE,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC;EACV,CAAC,CAAC;EAEF,oBACER,OAAA;IAAK0D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3D,OAAA;MAAK0D,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClF3D,OAAA;QAAK0D,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC3D,OAAA;UAAK0D,SAAS,EAAC,+JAA+J;UAAAC,QAAA,eAC5K3D,OAAA,CAACF,KAAK;YAAC4D,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACN/D,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAI0D,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzE/D,OAAA;YAAG0D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN/D,OAAA;MAAK0D,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAClF3D,OAAA;QAAK0D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD3D,OAAA;UAAK0D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtC3D,OAAA;YAAI0D,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN/D,OAAA;UACEgE,OAAO,EAAEA,CAAA,KAAMvC,kBAAkB,CAAC,IAAI,CAAE;UACxCiC,SAAS,EAAC,0EAA0E;UAAAC,QAAA,eAEpF3D,OAAA,CAACN,IAAI;YAACgE,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/D,OAAA;QAAK0D,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3C3D,OAAA;UAAOiE,OAAO,EAAC,YAAY;UAACP,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/E/D,OAAA;UACEO,EAAE,EAAC,YAAY;UACf2D,KAAK,EAAEhC,QAAS;UAChBiC,QAAQ,EAAEC,CAAC,IAAIjC,WAAW,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAY,CAAE;UAClDR,SAAS,EAAC,6GAA6G;UAAAC,QAAA,gBAEvH3D,OAAA;YAAQkE,KAAK,EAAC,WAAW;YAAAP,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClE/D,OAAA;YAAQkE,KAAK,EAAC,UAAU;YAAAP,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjE/D,OAAA;YAAQkE,KAAK,EAAC,WAAW;YAAAP,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtD/D,OAAA;YAAQkE,KAAK,EAAC,YAAY;YAAAP,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EACLvC,eAAe,iBACdxB,OAAA;QAAK0D,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpE3D,OAAA;UAAK0D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3D,OAAA;YACEsE,IAAI,EAAC,MAAM;YACXJ,KAAK,EAAE5C,WAAY;YACnB6C,QAAQ,EAAGC,CAAC,IAAK7C,cAAc,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDK,WAAW,EAAC,oCAAqB;YACjCb,SAAS,EAAC,yIAAyI;YACnJc,UAAU,EAAGJ,CAAC,IAAKA,CAAC,CAACK,GAAG,KAAK,OAAO,IAAI3B,UAAU,CAAC;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACF/D,OAAA;YAAK0D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3D,OAAA;cACEsE,IAAI,EAAC,MAAM;cACXJ,KAAK,EAAExC,WAAY;cACnByC,QAAQ,EAAGC,CAAC,IAAKzC,cAAc,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDR,SAAS,EAAC;YAAoH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/H,CAAC,eACF/D,OAAA;cACEkE,KAAK,EAAEtC,eAAgB;cACvBuC,QAAQ,EAAGC,CAAC,IAAKvC,kBAAkB,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAkC,CAAE;cACjFR,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvH3D,OAAA;gBAAQkE,KAAK,EAAC,KAAK;gBAAAP,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC/D,OAAA;gBAAQkE,KAAK,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzC/D,OAAA;gBAAQkE,KAAK,EAAC,MAAM;gBAAAP,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN/D,OAAA;YAAK0D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3D,OAAA;cACEsE,IAAI,EAAC,MAAM;cACXJ,KAAK,EAAEpC,cAAe;cACtBqC,QAAQ,EAAEC,CAAC,IAAIrC,iBAAiB,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACjDK,WAAW,EAAC,sBAAsB;cAClCb,SAAS,EAAC;YAAyI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpJ,CAAC,eACF/D,OAAA;cACEsE,IAAI,EAAC,MAAM;cACXJ,KAAK,EAAElC,YAAa;cACpBmC,QAAQ,EAAEC,CAAC,IAAInC,eAAe,CAACmC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CK,WAAW,EAAC,kBAAkB;cAC9Bb,SAAS,EAAC;YAAyI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/D,OAAA;YAAK0D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3D,OAAA;cACEgE,OAAO,EAAElB,UAAW;cACpBY,SAAS,EAAC,iIAAiI;cAAAC,QAAA,gBAE3I3D,OAAA,CAACF,KAAK;gBAAC4D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/D,OAAA;cACEgE,OAAO,EAAEb,aAAc;cACvBO,SAAS,EAAC,6HAA6H;cAAAC,QAAA,gBAEvI3D,OAAA,CAACH,CAAC;gBAAC6D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAE3B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACD/D,OAAA;QAAK0D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B3D,OAAA;UAAO0D,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC9C3D,OAAA;YAAA2D,QAAA,eACE3D,OAAA;cAAI0D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACzB3D,OAAA;gBAAI0D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpD/D,OAAA;gBAAI0D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7D/D,OAAA;gBAAI0D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzD/D,OAAA;gBAAI0D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7C/D,OAAA;gBAAI0D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpD/D,OAAA;gBAAI0D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/C/D,OAAA;gBAAI0D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR/D,OAAA;YAAA2D,QAAA,EACGN,WAAW,CAACf,GAAG,CAAEC,IAAI,iBACpBvC,OAAA;cAAkB0D,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACrE3D,OAAA;gBAAI0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB7C,WAAW,KAAKyB,IAAI,CAAChC,EAAE,gBACtBP,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBACXJ,KAAK,EAAElD,WAAY;kBACnBmD,QAAQ,EAAEC,CAAC,IAAInD,cAAc,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC9CR,SAAS,EAAC;gBAAoH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/H,CAAC,GAEFxB,IAAI,CAAC/B;cACN;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACL/D,OAAA;gBAAI0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB7C,WAAW,KAAKyB,IAAI,CAAChC,EAAE,gBACtBP,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBACXJ,KAAK,EAAEhD,cAAe;kBACtBiD,QAAQ,EAAEC,CAAC,IAAIjD,iBAAiB,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDR,SAAS,EAAC;gBAAoH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/H,CAAC,GAEFxB,IAAI,CAAC3B;cACN;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACL/D,OAAA;gBAAI0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB7C,WAAW,KAAKyB,IAAI,CAAChC,EAAE,gBACtBP,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBACXJ,KAAK,EAAE9C,YAAa;kBACpB+C,QAAQ,EAAEC,CAAC,IAAI/C,eAAe,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAAoH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/H,CAAC,GAEFxB,IAAI,CAAC1B;cACN;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACL/D,OAAA;gBAAI0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEpB,IAAI,CAAC7B;cAAI;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1C/D,OAAA;gBAAI0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB3D,OAAA;kBAAM0D,SAAS,EAAE,kCAAkCN,gBAAgB,CAACb,IAAI,CAAC5B,QAAQ,CAAC,cAAe;kBAAAgD,QAAA,EAC9FpB,IAAI,CAAC5B,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG4B,IAAI,CAAC5B,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG;gBAAO;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL/D,OAAA;gBAAI0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB3D,OAAA;kBACEsE,IAAI,EAAC,UAAU;kBACfI,OAAO,EAAEnC,IAAI,CAAC9B,SAAU;kBACxB0D,QAAQ,EAAEA,CAAA,KAAM/B,UAAU,CAACG,IAAI,CAAChC,EAAE,CAAE;kBACpCmD,SAAS,EAAC,kBAAkB;kBAC5BiB,QAAQ,EAAE7D,WAAW,KAAKyB,IAAI,CAAChC;gBAAG;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACL/D,OAAA;gBAAI0D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EACjC7C,WAAW,KAAKyB,IAAI,CAAChC,EAAE,gBACtBP,OAAA,CAAAE,SAAA;kBAAAyD,QAAA,gBACE3D,OAAA;oBAAQgE,OAAO,EAAErB,cAAe;oBAACe,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,eAAC3D,OAAA,CAACF,KAAK;sBAAC4D,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3H/D,OAAA;oBAAQgE,OAAO,EAAEnB,aAAc;oBAACa,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eAAC3D,OAAA,CAACH,CAAC;sBAAC6D,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eAClH,CAAC,gBAEH/D,OAAA,CAAAE,SAAA;kBAAAyD,QAAA,gBACE3D,OAAA;oBAAQgE,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAACH,IAAI,CAAE;oBAACmB,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,eAAC3D,OAAA,CAACL,IAAI;sBAAC+D,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnI/D,OAAA;oBAAQgE,OAAO,EAAEA,CAAA,KAAMxB,UAAU,CAACD,IAAI,CAAChC,EAAE,CAAE;oBAACmD,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,eAAC3D,OAAA,CAACJ,MAAM;sBAAC8D,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACpI;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GAhEExB,IAAI,CAAChC,EAAE;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiEZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/D,OAAA;QAAK0D,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjD3D,OAAA;UAAK0D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,iBAC3B,EAACtD,KAAK,CAACoC,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAACnE,SAAS,CAAC,CAACoE,MAAM,EAAC,WAAI,EAACxE,KAAK,CAACwE,MAAM;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA7SID,SAAmB;AAAA2E,EAAA,GAAnB3E,SAAmB;AA+SzB,eAAeA,SAAS;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}