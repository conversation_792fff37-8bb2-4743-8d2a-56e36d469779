{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\AudioPlayer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Play, Pause, Volume2, VolumeX, SkipBack, SkipForward } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AudioPlayer = ({\n  audioBlob,\n  audioUrl,\n  title = 'Audio įrašas',\n  className = ''\n}) => {\n  _s();\n  const audioRef = useRef(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [volume, setVolume] = useState(1);\n  const [isMuted, setIsMuted] = useState(false);\n  const [audioSrc, setAudioSrc] = useState('');\n\n  // Initialize audio source\n  useEffect(() => {\n    if (audioBlob) {\n      const url = URL.createObjectURL(audioBlob);\n      setAudioSrc(url);\n      return () => URL.revokeObjectURL(url);\n    } else if (audioUrl) {\n      setAudioSrc(audioUrl);\n    }\n  }, [audioBlob, audioUrl]);\n\n  // Audio event listeners\n  useEffect(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    const handleLoadedMetadata = () => {\n      setDuration(audio.duration);\n    };\n    const handleTimeUpdate = () => {\n      setCurrentTime(audio.currentTime);\n    };\n    const handleEnded = () => {\n      setIsPlaying(false);\n      setCurrentTime(0);\n    };\n    audio.addEventListener('loadedmetadata', handleLoadedMetadata);\n    audio.addEventListener('timeupdate', handleTimeUpdate);\n    audio.addEventListener('ended', handleEnded);\n    return () => {\n      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);\n      audio.removeEventListener('timeupdate', handleTimeUpdate);\n      audio.removeEventListener('ended', handleEnded);\n    };\n  }, [audioSrc]);\n  const togglePlay = () => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    if (isPlaying) {\n      audio.pause();\n    } else {\n      audio.play();\n    }\n    setIsPlaying(!isPlaying);\n  };\n  const handleSeek = e => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    const seekTime = parseFloat(e.target.value) / 100 * duration;\n    audio.currentTime = seekTime;\n    setCurrentTime(seekTime);\n  };\n  const handleVolumeChange = e => {\n    const audio = audioRef.current;\n    const newVolume = parseFloat(e.target.value) / 100;\n    setVolume(newVolume);\n    setIsMuted(newVolume === 0);\n    if (audio) {\n      audio.volume = newVolume;\n    }\n  };\n  const toggleMute = () => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    if (isMuted) {\n      audio.volume = volume;\n      setIsMuted(false);\n    } else {\n      audio.volume = 0;\n      setIsMuted(true);\n    }\n  };\n  const skip = seconds => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    const newTime = Math.max(0, Math.min(duration, currentTime + seconds));\n    audio.currentTime = newTime;\n    setCurrentTime(newTime);\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = Math.floor(seconds % 60);\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  const progress = duration > 0 ? currentTime / duration * 100 : 0;\n  if (!audioSrc) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `gradient-border-fade rounded-3xl shadow-soft bg-unique-gradient-1 backdrop-blur-2xl p-4 transition-smooth hover:shadow-elegant border border-white/30 ring-1 ring-white/15 ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center text-gray-500\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm\",\n          children: \"N\\u0117ra audio \\u012Fra\\u0161o\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-2 backdrop-blur-2xl p-4 transition-smooth hover:shadow-primary hover-gradient-shift border border-white/30 ring-1 ring-white/15 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"audio\", {\n      ref: audioRef,\n      src: audioSrc,\n      preload: \"metadata\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), title && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-sm font-medium text-gray-900 truncate\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"0\",\n          max: \"100\",\n          value: progress,\n          onChange: handleSeek,\n          className: \"w-full h-2 bg-gray-200 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500/20\",\n          style: {\n            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${progress}%, #e5e7eb ${progress}%, #e5e7eb 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between mt-1 text-xs text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatTime(currentTime)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatTime(duration)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => skip(-10),\n          className: \"p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/30 shadow-soft hover:shadow-elegant\",\n          title: \"At\\u0161okti 10s\",\n          children: /*#__PURE__*/_jsxDEV(SkipBack, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: togglePlay,\n          className: \"flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 text-white rounded-full shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-2 focus:ring-blue-500/20 border border-blue-400/30 hover:border-blue-300/40\",\n          children: isPlaying ? /*#__PURE__*/_jsxDEV(Pause, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Play, {\n            className: \"w-4 h-4 ml-0.5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => skip(10),\n          className: \"p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/30 shadow-soft hover:shadow-elegant\",\n          title: \"Pra\\u0161okti 10s\",\n          children: /*#__PURE__*/_jsxDEV(SkipForward, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleMute,\n          className: \"p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/30 shadow-soft hover:shadow-elegant\",\n          children: isMuted || volume === 0 ? /*#__PURE__*/_jsxDEV(VolumeX, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Volume2, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"0\",\n          max: \"100\",\n          value: isMuted ? 0 : volume * 100,\n          onChange: handleVolumeChange,\n          className: \"w-20 h-2 bg-gray-200 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500/20\",\n          style: {\n            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${isMuted ? 0 : volume * 100}%, #e5e7eb ${isMuted ? 0 : volume * 100}%, #e5e7eb 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n_s(AudioPlayer, \"G3Zt3cwiv1Ad7VJQ3W+TJdfvQno=\");\n_c = AudioPlayer;\nvar _c;\n$RefreshReg$(_c, \"AudioPlayer\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Play", "Pause", "Volume2", "VolumeX", "SkipBack", "SkipForward", "jsxDEV", "_jsxDEV", "AudioPlayer", "audioBlob", "audioUrl", "title", "className", "_s", "audioRef", "isPlaying", "setIsPlaying", "currentTime", "setCurrentTime", "duration", "setDuration", "volume", "setVolume", "isMuted", "setIsMuted", "audioSrc", "setAudioSrc", "url", "URL", "createObjectURL", "revokeObjectURL", "audio", "current", "handleLoadedMetadata", "handleTimeUpdate", "handleEnded", "addEventListener", "removeEventListener", "togglePlay", "pause", "play", "handleSeek", "e", "seekTime", "parseFloat", "target", "value", "handleVolumeChange", "newVolume", "toggleMute", "skip", "seconds", "newTime", "Math", "max", "min", "formatTime", "mins", "floor", "secs", "toString", "padStart", "progress", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "src", "preload", "type", "onChange", "style", "background", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/AudioPlayer.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Play, Pause, Volume2, VolumeX, SkipBack, SkipForward } from 'lucide-react';\r\n\r\ninterface AudioPlayerProps {\r\n  audioBlob?: Blob;\r\n  audioUrl?: string;\r\n  title?: string;\r\n  className?: string;\r\n}\r\n\r\nexport const AudioPlayer: React.FC<AudioPlayerProps> = ({\r\n  audioBlob,\r\n  audioUrl,\r\n  title = 'Audio įrašas',\r\n  className = '',\r\n}) => {\r\n  const audioRef = useRef<HTMLAudioElement>(null);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [currentTime, setCurrentTime] = useState(0);\r\n  const [duration, setDuration] = useState(0);\r\n  const [volume, setVolume] = useState(1);\r\n  const [isMuted, setIsMuted] = useState(false);\r\n  const [audioSrc, setAudioSrc] = useState<string>('');\r\n\r\n  // Initialize audio source\r\n  useEffect(() => {\r\n    if (audioBlob) {\r\n      const url = URL.createObjectURL(audioBlob);\r\n      setAudioSrc(url);\r\n      return () => URL.revokeObjectURL(url);\r\n    } else if (audioUrl) {\r\n      setAudioSrc(audioUrl);\r\n    }\r\n  }, [audioBlob, audioUrl]);\r\n\r\n  // Audio event listeners\r\n  useEffect(() => {\r\n    const audio = audioRef.current;\r\n    if (!audio) return;\r\n\r\n    const handleLoadedMetadata = () => {\r\n      setDuration(audio.duration);\r\n    };\r\n\r\n    const handleTimeUpdate = () => {\r\n      setCurrentTime(audio.currentTime);\r\n    };\r\n\r\n    const handleEnded = () => {\r\n      setIsPlaying(false);\r\n      setCurrentTime(0);\r\n    };\r\n\r\n    audio.addEventListener('loadedmetadata', handleLoadedMetadata);\r\n    audio.addEventListener('timeupdate', handleTimeUpdate);\r\n    audio.addEventListener('ended', handleEnded);\r\n\r\n    return () => {\r\n      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);\r\n      audio.removeEventListener('timeupdate', handleTimeUpdate);\r\n      audio.removeEventListener('ended', handleEnded);\r\n    };\r\n  }, [audioSrc]);\r\n\r\n  const togglePlay = () => {\r\n    const audio = audioRef.current;\r\n    if (!audio) return;\r\n\r\n    if (isPlaying) {\r\n      audio.pause();\r\n    } else {\r\n      audio.play();\r\n    }\r\n    setIsPlaying(!isPlaying);\r\n  };\r\n\r\n  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const audio = audioRef.current;\r\n    if (!audio) return;\r\n\r\n    const seekTime = (parseFloat(e.target.value) / 100) * duration;\r\n    audio.currentTime = seekTime;\r\n    setCurrentTime(seekTime);\r\n  };\r\n\r\n  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const audio = audioRef.current;\r\n    const newVolume = parseFloat(e.target.value) / 100;\r\n    \r\n    setVolume(newVolume);\r\n    setIsMuted(newVolume === 0);\r\n    \r\n    if (audio) {\r\n      audio.volume = newVolume;\r\n    }\r\n  };\r\n\r\n  const toggleMute = () => {\r\n    const audio = audioRef.current;\r\n    if (!audio) return;\r\n\r\n    if (isMuted) {\r\n      audio.volume = volume;\r\n      setIsMuted(false);\r\n    } else {\r\n      audio.volume = 0;\r\n      setIsMuted(true);\r\n    }\r\n  };\r\n\r\n  const skip = (seconds: number) => {\r\n    const audio = audioRef.current;\r\n    if (!audio) return;\r\n\r\n    const newTime = Math.max(0, Math.min(duration, currentTime + seconds));\r\n    audio.currentTime = newTime;\r\n    setCurrentTime(newTime);\r\n  };\r\n\r\n  const formatTime = (seconds: number): string => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = Math.floor(seconds % 60);\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;\r\n\r\n  if (!audioSrc) {\r\n    return (\r\n      <div className={`gradient-border-fade rounded-3xl shadow-soft bg-unique-gradient-1 backdrop-blur-2xl p-4 transition-smooth hover:shadow-elegant border border-white/30 ring-1 ring-white/15 ${className}`}>\r\n        <div className=\"flex items-center justify-center text-gray-500\">\r\n          <span className=\"text-sm\">Nėra audio įrašo</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-2 backdrop-blur-2xl p-4 transition-smooth hover:shadow-primary hover-gradient-shift border border-white/30 ring-1 ring-white/15 ${className}`}>\r\n      <audio ref={audioRef} src={audioSrc} preload=\"metadata\" />\r\n      \r\n      {/* Title */}\r\n      {title && (\r\n        <div className=\"mb-3\">\r\n          <h4 className=\"text-sm font-medium text-gray-900 truncate\">{title}</h4>\r\n        </div>\r\n      )}\r\n\r\n      {/* Progress Bar */}\r\n      <div className=\"mb-4\">\r\n        <div className=\"relative\">\r\n          <input\r\n            type=\"range\"\r\n            min=\"0\"\r\n            max=\"100\"\r\n            value={progress}\r\n            onChange={handleSeek}\r\n            className=\"w-full h-2 bg-gray-200 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500/20\"\r\n            style={{\r\n              background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${progress}%, #e5e7eb ${progress}%, #e5e7eb 100%)`,\r\n            }}\r\n          />\r\n        </div>\r\n        <div className=\"flex justify-between mt-1 text-xs text-gray-500\">\r\n          <span>{formatTime(currentTime)}</span>\r\n          <span>{formatTime(duration)}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Controls */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          {/* Skip backward */}\r\n          <button\r\n            onClick={() => skip(-10)}\r\n            className=\"p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/30 shadow-soft hover:shadow-elegant\"\r\n            title=\"Atšokti 10s\"\r\n          >\r\n            <SkipBack className=\"h-4 w-4\" />\r\n          </button>\r\n\r\n          {/* Play/Pause */}\r\n          <button\r\n            onClick={togglePlay}\r\n            className=\"flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 text-white rounded-full shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-2 focus:ring-blue-500/20 border border-blue-400/30 hover:border-blue-300/40\"\r\n          >\r\n            {isPlaying ? (\r\n              <Pause className=\"w-4 h-4\" />\r\n            ) : (\r\n              <Play className=\"w-4 h-4 ml-0.5\" />\r\n            )}\r\n          </button>\r\n\r\n          {/* Skip forward */}\r\n          <button\r\n            onClick={() => skip(10)}\r\n            className=\"p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/30 shadow-soft hover:shadow-elegant\"\r\n            title=\"Prašokti 10s\"\r\n          >\r\n            <SkipForward className=\"h-4 w-4\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Volume Control */}\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={toggleMute}\r\n            className=\"p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/30 shadow-soft hover:shadow-elegant\"\r\n          >\r\n            {isMuted || volume === 0 ? (\r\n              <VolumeX className=\"h-4 w-4\" />\r\n            ) : (\r\n              <Volume2 className=\"h-4 w-4\" />\r\n            )}\r\n          </button>\r\n          <input\r\n            type=\"range\"\r\n            min=\"0\"\r\n            max=\"100\"\r\n            value={isMuted ? 0 : volume * 100}\r\n            onChange={handleVolumeChange}\r\n            className=\"w-20 h-2 bg-gray-200 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500/20\"\r\n            style={{\r\n              background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${isMuted ? 0 : volume * 100}%, #e5e7eb ${isMuted ? 0 : volume * 100}%, #e5e7eb 100%)`,\r\n            }}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASpF,OAAO,MAAMC,WAAuC,GAAGA,CAAC;EACtDC,SAAS;EACTC,QAAQ;EACRC,KAAK,GAAG,cAAc;EACtBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGhB,MAAM,CAAmB,IAAI,CAAC;EAC/C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;;EAEpD;EACAE,SAAS,CAAC,MAAM;IACd,IAAIU,SAAS,EAAE;MACb,MAAMkB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACpB,SAAS,CAAC;MAC1CiB,WAAW,CAACC,GAAG,CAAC;MAChB,OAAO,MAAMC,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;IACvC,CAAC,MAAM,IAAIjB,QAAQ,EAAE;MACnBgB,WAAW,CAAChB,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAACD,SAAS,EAAEC,QAAQ,CAAC,CAAC;;EAEzB;EACAX,SAAS,CAAC,MAAM;IACd,MAAMgC,KAAK,GAAGjB,QAAQ,CAACkB,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,MAAME,oBAAoB,GAAGA,CAAA,KAAM;MACjCb,WAAW,CAACW,KAAK,CAACZ,QAAQ,CAAC;IAC7B,CAAC;IAED,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;MAC7BhB,cAAc,CAACa,KAAK,CAACd,WAAW,CAAC;IACnC,CAAC;IAED,MAAMkB,WAAW,GAAGA,CAAA,KAAM;MACxBnB,YAAY,CAAC,KAAK,CAAC;MACnBE,cAAc,CAAC,CAAC,CAAC;IACnB,CAAC;IAEDa,KAAK,CAACK,gBAAgB,CAAC,gBAAgB,EAAEH,oBAAoB,CAAC;IAC9DF,KAAK,CAACK,gBAAgB,CAAC,YAAY,EAAEF,gBAAgB,CAAC;IACtDH,KAAK,CAACK,gBAAgB,CAAC,OAAO,EAAED,WAAW,CAAC;IAE5C,OAAO,MAAM;MACXJ,KAAK,CAACM,mBAAmB,CAAC,gBAAgB,EAAEJ,oBAAoB,CAAC;MACjEF,KAAK,CAACM,mBAAmB,CAAC,YAAY,EAAEH,gBAAgB,CAAC;MACzDH,KAAK,CAACM,mBAAmB,CAAC,OAAO,EAAEF,WAAW,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,CAACV,QAAQ,CAAC,CAAC;EAEd,MAAMa,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMP,KAAK,GAAGjB,QAAQ,CAACkB,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,IAAIhB,SAAS,EAAE;MACbgB,KAAK,CAACQ,KAAK,CAAC,CAAC;IACf,CAAC,MAAM;MACLR,KAAK,CAACS,IAAI,CAAC,CAAC;IACd;IACAxB,YAAY,CAAC,CAACD,SAAS,CAAC;EAC1B,CAAC;EAED,MAAM0B,UAAU,GAAIC,CAAsC,IAAK;IAC7D,MAAMX,KAAK,GAAGjB,QAAQ,CAACkB,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,MAAMY,QAAQ,GAAIC,UAAU,CAACF,CAAC,CAACG,MAAM,CAACC,KAAK,CAAC,GAAG,GAAG,GAAI3B,QAAQ;IAC9DY,KAAK,CAACd,WAAW,GAAG0B,QAAQ;IAC5BzB,cAAc,CAACyB,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMI,kBAAkB,GAAIL,CAAsC,IAAK;IACrE,MAAMX,KAAK,GAAGjB,QAAQ,CAACkB,OAAO;IAC9B,MAAMgB,SAAS,GAAGJ,UAAU,CAACF,CAAC,CAACG,MAAM,CAACC,KAAK,CAAC,GAAG,GAAG;IAElDxB,SAAS,CAAC0B,SAAS,CAAC;IACpBxB,UAAU,CAACwB,SAAS,KAAK,CAAC,CAAC;IAE3B,IAAIjB,KAAK,EAAE;MACTA,KAAK,CAACV,MAAM,GAAG2B,SAAS;IAC1B;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMlB,KAAK,GAAGjB,QAAQ,CAACkB,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,IAAIR,OAAO,EAAE;MACXQ,KAAK,CAACV,MAAM,GAAGA,MAAM;MACrBG,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,MAAM;MACLO,KAAK,CAACV,MAAM,GAAG,CAAC;MAChBG,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC;EAED,MAAM0B,IAAI,GAAIC,OAAe,IAAK;IAChC,MAAMpB,KAAK,GAAGjB,QAAQ,CAACkB,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,MAAMqB,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACpC,QAAQ,EAAEF,WAAW,GAAGkC,OAAO,CAAC,CAAC;IACtEpB,KAAK,CAACd,WAAW,GAAGmC,OAAO;IAC3BlC,cAAc,CAACkC,OAAO,CAAC;EACzB,CAAC;EAED,MAAMI,UAAU,GAAIL,OAAe,IAAa;IAC9C,MAAMM,IAAI,GAAGJ,IAAI,CAACK,KAAK,CAACP,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMQ,IAAI,GAAGN,IAAI,CAACK,KAAK,CAACP,OAAO,GAAG,EAAE,CAAC;IACrC,OAAO,GAAGM,IAAI,IAAIE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,MAAMC,QAAQ,GAAG3C,QAAQ,GAAG,CAAC,GAAIF,WAAW,GAAGE,QAAQ,GAAI,GAAG,GAAG,CAAC;EAElE,IAAI,CAACM,QAAQ,EAAE;IACb,oBACElB,OAAA;MAAKK,SAAS,EAAE,8KAA8KA,SAAS,EAAG;MAAAmD,QAAA,eACxMxD,OAAA;QAAKK,SAAS,EAAC,gDAAgD;QAAAmD,QAAA,eAC7DxD,OAAA;UAAMK,SAAS,EAAC,SAAS;UAAAmD,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAKK,SAAS,EAAE,sMAAsMA,SAAS,EAAG;IAAAmD,QAAA,gBAChOxD,OAAA;MAAO6D,GAAG,EAAEtD,QAAS;MAACuD,GAAG,EAAE5C,QAAS;MAAC6C,OAAO,EAAC;IAAU;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGzDxD,KAAK,iBACJJ,OAAA;MAAKK,SAAS,EAAC,MAAM;MAAAmD,QAAA,eACnBxD,OAAA;QAAIK,SAAS,EAAC,4CAA4C;QAAAmD,QAAA,EAAEpD;MAAK;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CACN,eAGD5D,OAAA;MAAKK,SAAS,EAAC,MAAM;MAAAmD,QAAA,gBACnBxD,OAAA;QAAKK,SAAS,EAAC,UAAU;QAAAmD,QAAA,eACvBxD,OAAA;UACEgE,IAAI,EAAC,OAAO;UACZhB,GAAG,EAAC,GAAG;UACPD,GAAG,EAAC,KAAK;UACTR,KAAK,EAAEgB,QAAS;UAChBU,QAAQ,EAAE/B,UAAW;UACrB7B,SAAS,EAAC,2HAA2H;UACrI6D,KAAK,EAAE;YACLC,UAAU,EAAE,iDAAiDZ,QAAQ,cAAcA,QAAQ;UAC7F;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN5D,OAAA;QAAKK,SAAS,EAAC,iDAAiD;QAAAmD,QAAA,gBAC9DxD,OAAA;UAAAwD,QAAA,EAAOP,UAAU,CAACvC,WAAW;QAAC;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtC5D,OAAA;UAAAwD,QAAA,EAAOP,UAAU,CAACrC,QAAQ;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,mCAAmC;MAAAmD,QAAA,gBAChDxD,OAAA;QAAKK,SAAS,EAAC,6BAA6B;QAAAmD,QAAA,gBAE1CxD,OAAA;UACEoE,OAAO,EAAEA,CAAA,KAAMzB,IAAI,CAAC,CAAC,EAAE,CAAE;UACzBtC,SAAS,EAAC,wPAAwP;UAClQD,KAAK,EAAC,kBAAa;UAAAoD,QAAA,eAEnBxD,OAAA,CAACH,QAAQ;YAACQ,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGT5D,OAAA;UACEoE,OAAO,EAAErC,UAAW;UACpB1B,SAAS,EAAC,0VAA0V;UAAAmD,QAAA,EAEnWhD,SAAS,gBACRR,OAAA,CAACN,KAAK;YAACW,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE7B5D,OAAA,CAACP,IAAI;YAACY,SAAS,EAAC;UAAgB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACnC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT5D,OAAA;UACEoE,OAAO,EAAEA,CAAA,KAAMzB,IAAI,CAAC,EAAE,CAAE;UACxBtC,SAAS,EAAC,wPAAwP;UAClQD,KAAK,EAAC,mBAAc;UAAAoD,QAAA,eAEpBxD,OAAA,CAACF,WAAW;YAACO,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN5D,OAAA;QAAKK,SAAS,EAAC,6BAA6B;QAAAmD,QAAA,gBAC1CxD,OAAA;UACEoE,OAAO,EAAE1B,UAAW;UACpBrC,SAAS,EAAC,wPAAwP;UAAAmD,QAAA,EAEjQxC,OAAO,IAAIF,MAAM,KAAK,CAAC,gBACtBd,OAAA,CAACJ,OAAO;YAACS,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE/B5D,OAAA,CAACL,OAAO;YAACU,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAC/B;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACT5D,OAAA;UACEgE,IAAI,EAAC,OAAO;UACZhB,GAAG,EAAC,GAAG;UACPD,GAAG,EAAC,KAAK;UACTR,KAAK,EAAEvB,OAAO,GAAG,CAAC,GAAGF,MAAM,GAAG,GAAI;UAClCmD,QAAQ,EAAEzB,kBAAmB;UAC7BnC,SAAS,EAAC,yHAAyH;UACnI6D,KAAK,EAAE;YACLC,UAAU,EAAE,iDAAiDnD,OAAO,GAAG,CAAC,GAAGF,MAAM,GAAG,GAAG,cAAcE,OAAO,GAAG,CAAC,GAAGF,MAAM,GAAG,GAAG;UACjI;QAAE;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA5NWL,WAAuC;AAAAoE,EAAA,GAAvCpE,WAAuC;AAAA,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}