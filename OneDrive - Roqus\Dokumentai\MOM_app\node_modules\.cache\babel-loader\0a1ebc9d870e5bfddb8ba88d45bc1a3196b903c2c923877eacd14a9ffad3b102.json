{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from 'react';\nimport { MeetingsList, ErrorBoundary, WhisperStatusIndicator, TranscriptionManager, ProfessionalTranscriptViewer, RecordingPanel } from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { createDemoMeetings } from './utils/demoData';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List, Menu, X } from 'lucide-react';\nimport './styles/background.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [meetings, setMeetings] = useState([]);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState(null);\n  const [activeView, setActiveView] = useState('recording');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showTitleModal, setShowTitleModal] = useState(false);\n  const [newMeetingTitle, setNewMeetingTitle] = useState('');\n  const {\n    recordingState,\n    startRecording,\n    stopRecording,\n    pauseRecording,\n    resumeRecording\n  } = useAudioRecorder();\n  const {\n    transcript,\n    isTranscribing,\n    transcribeAudioEnhanced,\n    cancelTranscription,\n    editSegment,\n    clearTranscript,\n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n  const handleStartRecording = useCallback(async title => {\n    try {\n      await startRecording();\n      const newMeeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started'\n        }\n      };\n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n  const handleStartRecordingWithTitle = useCallback(() => {\n    setNewMeetingTitle(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n    setShowTitleModal(true);\n  }, []);\n  const confirmStartRecording = useCallback(() => {\n    if (newMeetingTitle.trim()) {\n      handleStartRecording(newMeetingTitle.trim());\n      setShowTitleModal(false);\n      setNewMeetingTitle('');\n    }\n  }, [newMeetingTitle, handleStartRecording]);\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      if (currentMeeting && audioBlob) {\n        const updatedMeeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started'\n          }\n        };\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m));\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n  const handleStartTranscription = useCallback(async meetingId => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date()\n      }\n    };\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: progress => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: {\n              ...m.transcriptionStatus,\n              progress,\n              state: 'processing'\n            }\n          } : m));\n        },\n        onStatusUpdate: status => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: status\n          } : m));\n        },\n        enhanceSpeakers: true\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date()\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n\n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence\n      });\n    } catch (error) {\n      console.error('❌ Transkribavimo klaida:', error);\n      const errorMeeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n  const handleCancelTranscription = useCallback(meetingId => {\n    cancelTranscription();\n    setMeetings(prev => prev.map(m => m.id === meetingId ? {\n      ...m,\n      transcriptionStatus: {\n        ...m.transcriptionStatus,\n        state: 'cancelled'\n      }\n    } : m));\n  }, [cancelTranscription]);\n  const handleEditSegment = useCallback((meetingId, segmentId, newText) => {\n    editSegment(segmentId, newText);\n\n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => {\n      var _meeting$transcript;\n      return meeting.id === meetingId ? {\n        ...meeting,\n        transcript: (_meeting$transcript = meeting.transcript) === null || _meeting$transcript === void 0 ? void 0 : _meeting$transcript.map(segment => segment.id === segmentId ? {\n          ...segment,\n          text: newText,\n          isEdited: true,\n          editedAt: new Date(),\n          editedBy: 'user'\n        } : segment)\n      } : meeting;\n    }));\n  }, [editSegment]);\n  const handleSelectMeeting = useCallback(meeting => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n  const handleDeleteMeeting = useCallback(meetingId => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if ((currentMeeting === null || currentMeeting === void 0 ? void 0 : currentMeeting.id) === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if ((selectedMeetingForTranscript === null || selectedMeetingForTranscript === void 0 ? void 0 : selectedMeetingForTranscript.id) === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n  const handleExportMeeting = useCallback(meeting => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus\n    };\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);\n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed',\n        progress: 100,\n        completedAt: meeting.date\n      }\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  // Close mobile menu when clicking outside or on escape\n  useEffect(() => {\n    const handleClickOutside = event => {\n      const target = event.target;\n      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n    const handleEscape = event => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n    if (isMobileMenuOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when mobile menu is open\n      document.body.classList.add('mobile-menu-open');\n    } else {\n      document.body.classList.remove('mobile-menu-open');\n    }\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('keydown', handleEscape);\n      document.body.classList.remove('mobile-menu-open');\n    };\n  }, [isMobileMenuOpen]);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: [showTitleModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl max-w-md w-full p-6 animate-scale-in\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-xl border border-blue-400/20\",\n              children: /*#__PURE__*/_jsxDEV(Mic2, {\n                className: \"h-6 w-6 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-white\",\n              children: \"Naujas pokalbis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-white/60\",\n              children: \"\\u012Eveskite pokalbio pavadinim\\u0105\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newMeetingTitle,\n              onChange: e => setNewMeetingTitle(e.target.value),\n              onKeyDown: e => e.key === 'Enter' && confirmStartRecording(),\n              className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200\",\n              placeholder: \"Pokalbio pavadinimas...\",\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setShowTitleModal(false);\n                  setNewMeetingTitle('');\n                },\n                className: \"flex-1 px-4 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white/70 hover:text-white rounded-xl transition-all duration-200 text-sm font-medium\",\n                children: \"At\\u0161aukti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: confirmStartRecording,\n                disabled: !newMeetingTitle.trim(),\n                className: \"flex-1 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 disabled:from-blue-600/50 disabled:to-indigo-600/50 text-white rounded-xl transition-all duration-200 text-sm font-medium disabled:cursor-not-allowed\",\n                children: \"Prad\\u0117ti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen elegant-background font-inter\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"elegant-grid\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          className: \"fixed top-0 left-0 right-0 z-50 glassmorphic-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between h-16\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\",\n                  children: /*#__PURE__*/_jsxDEV(Mic2, {\n                    className: \"h-5 w-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-white font-semibold text-lg bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                    children: \"MOM App\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/60 text-xs font-medium hidden sm:block\",\n                    children: \"Meeting Recording & Transcription\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white/8 backdrop-blur-sm rounded-lg border border-white/15 shadow-md\",\n                  children: /*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex bg-white/8 backdrop-blur-sm rounded-lg p-0.5 border border-white/15 shadow-md\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('recording'),\n                    className: `px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-300 ${activeView === 'recording' ? 'bg-gradient-to-r from-blue-500/70 to-purple-600/70 text-white shadow-md border border-white/15' : 'text-white/60 hover:text-white hover:bg-white/8'}`,\n                    children: \"\\u012Era\\u0161ymas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('transcription'),\n                    className: `px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-300 ${activeView === 'transcription' ? 'bg-gradient-to-r from-blue-500/70 to-purple-600/70 text-white shadow-md border border-white/15' : 'text-white/60 hover:text-white hover:bg-white/8'}`,\n                    children: \"Transkribavimas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('transcript'),\n                    className: `px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-300 ${activeView === 'transcript' ? 'bg-gradient-to-r from-blue-500/70 to-purple-600/70 text-white shadow-md border border-white/15' : 'text-white/60 hover:text-white hover:bg-white/8'}`,\n                    children: \"Rezultatai\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:flex items-center gap-2\",\n                children: [meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: loadDemoData,\n                  className: \"px-3 py-1.5 text-xs font-medium text-white/70 hover:text-white bg-white/8 hover:bg-white/15 backdrop-blur-sm rounded-md transition-all duration-300 border border-white/15 shadow-md\",\n                  children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                    className: \"h-3 w-3 mr-1.5 inline\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 23\n                  }, this), \"Demo\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!recordingState.isRecording) {\n                      handleStartRecordingWithTitle();\n                    }\n                  },\n                  disabled: recordingState.isRecording,\n                  className: \"px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500/70 to-purple-600/70 hover:from-blue-600/80 hover:to-purple-700/80 disabled:from-gray-500/40 disabled:to-gray-600/40 backdrop-blur-sm rounded-md transition-all duration-300 flex items-center gap-1.5 shadow-md border border-white/15 disabled:cursor-not-allowed\",\n                  children: [/*#__PURE__*/_jsxDEV(Plus, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 21\n                  }, this), \"Naujas pokalbis\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:hidden flex items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n                  className: \"p-1.5 rounded-md bg-white/8 hover:bg-white/15 border border-white/15 shadow-md transition-all duration-300\",\n                  \"aria-expanded\": isMobileMenuOpen,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Open main menu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 21\n                  }, this), isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n                    className: \"h-4 w-4 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                    className: \"h-4 w-4 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 pt-2 pb-4 space-y-3 glassmorphic-card m-2 rounded-xl border border-white/20 shadow-lg mobile-menu-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('recording');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${activeView === 'recording' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Mic2, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 21\n                  }, this), \"\\u012Era\\u0161ymas\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('transcription');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${activeView === 'transcription' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Zap, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 21\n                  }, this), \"Transkribavimas\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('transcript');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${activeView === 'transcript' ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20' : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Headphones, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 21\n                  }, this), \"Rezultatai\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pt-2 border-t border-white/10\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col space-y-2\",\n                  children: [meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      loadDemoData();\n                      setIsMobileMenuOpen(false);\n                    },\n                    className: \"px-4 py-3 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                      className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 25\n                    }, this), \"Demo\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setIsMobileMenuOpen(false);\n                      if (!recordingState.isRecording) {\n                        handleStartRecordingWithTitle();\n                      }\n                    },\n                    disabled: recordingState.isRecording,\n                    className: \"px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 disabled:from-gray-500/50 disabled:to-gray-600/50 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20 disabled:cursor-not-allowed\",\n                    children: [/*#__PURE__*/_jsxDEV(Plus, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 23\n                    }, this), \"Naujas pokalbis\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"px-4 py-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white/70 text-sm mr-2\",\n                        children: \"Whisper Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 566,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 567,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 py-8 pt-24 md:pt-24\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-8 min-h-[calc(100vh-140px)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-3\",\n              children: [activeView === 'recording' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative overflow-hidden rounded-3xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-700/20 backdrop-blur-2xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-[1px] bg-gradient-to-br from-white/8 via-white/4 to-transparent rounded-3xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-3xl border-2 border-slate-400/20 shadow-2xl shadow-black/40\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-4 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative z-10 h-full flex flex-col\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 border-b border-white/8\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"relative\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-gradient-to-br from-slate-700/70 via-slate-600/60 to-slate-500/50 backdrop-blur-lg rounded-lg flex items-center justify-center shadow-lg border border-slate-400/25\",\n                          children: /*#__PURE__*/_jsxDEV(Mic2, {\n                            className: \"h-4 w-4 text-white drop-shadow-sm\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 606,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 605,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute top-0.5 left-0.5 w-1.5 h-1.5 bg-white/15 rounded-full blur-xs\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 608,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 604,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-0.5\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                          className: \"text-sm font-semibold bg-gradient-to-r from-white via-slate-100 to-slate-200 bg-clip-text text-transparent tracking-tight\",\n                          children: \"Pokalbio \\u012Fra\\u0161ymas\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 611,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-slate-300/60 font-medium\",\n                          children: \"Prad\\u0117kite nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 614,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 603,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 flex flex-col min-h-[500px]\",\n                    children: [/*#__PURE__*/_jsxDEV(RecordingPanel, {\n                      recordingState: recordingState,\n                      currentMeeting: currentMeeting,\n                      onStartRecording: handleStartRecording,\n                      onStopRecording: handleStopRecording,\n                      onPauseRecording: pauseRecording,\n                      onResumeRecording: resumeRecording\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 23\n                    }, this), !recordingState.isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"px-6 pb-6\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-3 gap-3 max-w-md mx-auto\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center space-y-2 group cursor-default\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-slate-600/15 via-slate-500/12 to-slate-400/8 rounded-lg flex items-center justify-center mx-auto border border-slate-400/15 group-hover:scale-105 group-hover:border-blue-400/25 group-hover:bg-blue-500/8 transition-all duration-300 shadow-md\",\n                            children: /*#__PURE__*/_jsxDEV(Zap, {\n                              className: \"h-3.5 w-3.5 text-slate-300 group-hover:text-blue-300 transition-colors duration-300\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 639,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 638,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-slate-300/70 font-medium leading-tight\",\n                            children: [\"Automatinis\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 642,\n                              columnNumber: 44\n                            }, this), \"transkribavimas\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 641,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 637,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center space-y-2 group cursor-default\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-slate-600/15 via-slate-500/12 to-slate-400/8 rounded-lg flex items-center justify-center mx-auto border border-slate-400/15 group-hover:scale-105 group-hover:border-purple-400/25 group-hover:bg-purple-500/8 transition-all duration-300 shadow-md\",\n                            children: /*#__PURE__*/_jsxDEV(Headphones, {\n                              className: \"h-3.5 w-3.5 text-slate-300 group-hover:text-purple-300 transition-colors duration-300\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 647,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 646,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-slate-300/70 font-medium leading-tight\",\n                            children: [\"Auk\\u0161ta garso\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 650,\n                              columnNumber: 45\n                            }, this), \"kokyb\\u0117\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 649,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 645,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center space-y-2 group cursor-default\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-slate-600/15 via-slate-500/12 to-slate-400/8 rounded-lg flex items-center justify-center mx-auto border border-slate-400/15 group-hover:scale-105 group-hover:border-indigo-400/25 group-hover:bg-indigo-500/8 transition-all duration-300 shadow-md\",\n                            children: /*#__PURE__*/_jsxDEV(Settings, {\n                              className: \"h-3.5 w-3.5 text-slate-300 group-hover:text-indigo-300 transition-colors duration-300\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 655,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 654,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-slate-300/70 font-medium leading-tight\",\n                            children: [\"Pa\\u017Eang\\u016Bs\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 658,\n                              columnNumber: 41\n                            }, this), \"nustatymai\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 657,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 653,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 635,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this), activeView === 'recording' && recordingState.isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative overflow-hidden rounded-3xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-700/20 backdrop-blur-2xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-[1px] bg-gradient-to-br from-white/8 via-white/4 to-transparent rounded-3xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-3xl border-2 border-slate-400/20 shadow-2xl shadow-black/40\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative z-10 h-full flex flex-col\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 flex flex-col min-h-[500px]\",\n                    children: /*#__PURE__*/_jsxDEV(RecordingPanel, {\n                      recordingState: recordingState,\n                      currentMeeting: currentMeeting,\n                      onStartRecording: handleStartRecording,\n                      onStopRecording: handleStopRecording,\n                      onPauseRecording: pauseRecording,\n                      onResumeRecording: resumeRecording\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this), activeView === 'transcription' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6 border-b border-white/20\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(Zap, {\n                        className: \"h-6 w-6 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 706,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-semibold text-white\",\n                        children: \"Transkribavimas\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-white/60\",\n                        children: \"Audio fail\\u0173 konvertavimas \\u012F tekst\\u0105 naudojant AI\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: /*#__PURE__*/_jsxDEV(TranscriptionManager, {\n                    meetings: meetings,\n                    onStartTranscription: handleStartTranscription,\n                    onCancelTranscription: handleCancelTranscription,\n                    isTranscribing: isTranscribing,\n                    currentTranscriptionId: currentTranscriptionId,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onViewResults: () => setActiveView('transcript')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), activeView === 'transcript' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6 border-b border-white/20\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(Headphones, {\n                        className: \"h-6 w-6 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 737,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 736,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-semibold text-white\",\n                        children: \"Rezultatai\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 740,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-white/60\",\n                        children: \"Per\\u017Ei\\u016Br\\u0117kite ir redaguokite transkribavimo rezultatus\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 741,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: /*#__PURE__*/_jsxDEV(ProfessionalTranscriptViewer, {\n                    meetings: meetings,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onGoToTranscription: () => setActiveView('transcription')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative overflow-hidden rounded-3xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-700/20 backdrop-blur-2xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-[1px] bg-gradient-to-br from-white/8 via-white/4 to-transparent rounded-3xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-3xl border-2 border-slate-400/20 shadow-2xl shadow-black/40\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-4 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative z-10 h-full flex flex-col\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 border-b border-white/8\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"relative\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 bg-gradient-to-br from-indigo-700/70 via-indigo-600/60 to-purple-600/50 backdrop-blur-lg rounded-lg flex items-center justify-center shadow-lg border border-indigo-400/25\",\n                          children: /*#__PURE__*/_jsxDEV(List, {\n                            className: \"h-4 w-4 text-white drop-shadow-sm\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 778,\n                            columnNumber: 27\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 777,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute top-0.5 left-0.5 w-1.5 h-1.5 bg-white/15 rounded-full blur-xs\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 780,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 776,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-0.5\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                          className: \"text-sm font-semibold bg-gradient-to-r from-white via-slate-100 to-slate-200 bg-clip-text text-transparent tracking-tight\",\n                          children: \"Pokalbiai\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 783,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-slate-300/60 font-medium\",\n                          children: [\"Visi pokalbiai (\", meetings.length, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 786,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 782,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 775,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 774,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 p-4\",\n                    children: meetings.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center py-8 space-y-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"relative mx-auto w-12 h-12\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute inset-0 bg-gradient-to-br from-slate-600/15 via-slate-500/12 to-slate-400/8 rounded-xl\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 798,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute inset-[1px] bg-gradient-to-br from-white/6 to-transparent rounded-xl\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 799,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"relative w-full h-full flex items-center justify-center border border-slate-400/15 rounded-xl shadow-lg\",\n                          children: /*#__PURE__*/_jsxDEV(List, {\n                            className: \"h-5 w-5 text-slate-300/50\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 801,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 800,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 797,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-white font-medium text-sm tracking-tight\",\n                          children: \"N\\u0117ra pokalbi\\u0173\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 805,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-slate-300/60 text-xs leading-relaxed max-w-xs mx-auto\",\n                          children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 806,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 804,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 796,\n                      columnNumber: 23\n                    }, this) : /*#__PURE__*/_jsxDEV(MeetingsList, {\n                      meetings: meetings,\n                      currentMeeting: currentMeeting,\n                      onSelectMeeting: handleSelectMeeting,\n                      onDeleteMeeting: handleDeleteMeeting,\n                      onExportMeeting: () => {},\n                      activeView: \"list\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 812,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 324,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"q4lqd/4P/vbhdnJrcW+YtP8rp3g=\", false, function () {\n  return [useAudioRecorder, useTranscription];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "MeetingsList", "Error<PERSON>ou<PERSON><PERSON>", "WhisperStatusIndicator", "TranscriptionManager", "ProfessionalTranscriptViewer", "RecordingPanel", "useAudioRecorder", "useTranscription", "createDemoMeetings", "Headphones", "Plus", "Mic2", "TestTube", "Zap", "Settings", "List", "<PERSON><PERSON>", "X", "jsxDEV", "_jsxDEV", "App", "_s", "meetings", "setMeetings", "currentMeeting", "setCurrentMeeting", "selectedMeetingForTranscript", "setSelectedMeetingForTranscript", "activeView", "setActiveView", "isMobileMenuOpen", "setIsMobileMenuOpen", "showTitleModal", "setShowTitleModal", "newMeetingTitle", "setNewMeetingTitle", "recordingState", "startRecording", "stopRecording", "pauseRecording", "resumeRecording", "transcript", "isTranscribing", "transcribeAudioEnhanced", "cancelTranscription", "editSegment", "clearTranscript", "clearError", "currentTranscriptionId", "progress", "isWhisperConfigured", "handleStartRecording", "title", "newMeeting", "id", "Date", "now", "toString", "date", "duration", "status", "transcriptionStatus", "state", "prev", "error", "console", "handleStartRecordingWithTitle", "toLocaleString", "confirmStartRecording", "trim", "handleStopRecording", "audioBlob", "updatedMeeting", "Math", "floor", "getTime", "map", "m", "alert", "handleStartTranscription", "meetingId", "meeting", "find", "startedAt", "result", "onProgress", "onStatusUpdate", "enhanceSpeakers", "completedMeeting", "segments", "participants", "speakers", "metadata", "completedAt", "log", "length", "words", "totalWords", "confidence", "averageConfidence", "errorMeeting", "message", "handleCancelTranscription", "handleEditSegment", "segmentId", "newText", "_meeting$transcript", "segment", "text", "isEdited", "editedAt", "editedBy", "handleSelectMeeting", "handleDeleteMeeting", "filter", "handleExportMeeting", "exportData", "toISOString", "dataStr", "JSON", "stringify", "dataUri", "encodeURIComponent", "exportFileDefaultName", "replace", "toLowerCase", "split", "linkElement", "document", "createElement", "setAttribute", "click", "loadDemoData", "demoMeetings", "handleClickOutside", "event", "target", "closest", "handleEscape", "key", "addEventListener", "body", "classList", "add", "remove", "removeEventListener", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "onKeyDown", "placeholder", "autoFocus", "onClick", "disabled", "isRecording", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "onStartTranscription", "onCancelTranscription", "onDeleteMeeting", "onViewResults", "onGoToTranscription", "onSelectMeeting", "onExportMeeting", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from 'react';\nimport {\n  <PERSON>s<PERSON>ist,\n  <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>,\n  WhisperStatusIndicator,\n  TranscriptionManager,\n  ProfessionalTranscriptViewer,\n  DynamicAudioVisualizer,\n  RecordingPanel\n} from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { Meeting, TranscriptionStatus, Speaker } from './types/meeting';\nimport { createDemoMeetings } from './utils/demoData';\nimport { identifySpeakers } from './services/speakerService';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List, Square, Menu, X } from 'lucide-react';\nimport './styles/background.css';\n\nfunction App() {\n  const [meetings, setMeetings] = useState<Meeting[]>([]);\n  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);\n  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showTitleModal, setShowTitleModal] = useState(false);\n  const [newMeetingTitle, setNewMeetingTitle] = useState('');\n\n  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();\n  const { \n    transcript, \n    isTranscribing, \n    transcribeAudioEnhanced, \n    cancelTranscription,\n    editSegment,\n    clearTranscript, \n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n\n  const handleStartRecording = useCallback(async (title: string) => {\n    try {\n      await startRecording();\n      \n      const newMeeting: Meeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started',\n        },\n      };\n      \n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n\n  const handleStartRecordingWithTitle = useCallback(() => {\n    setNewMeetingTitle(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n    setShowTitleModal(true);\n  }, []);\n\n  const confirmStartRecording = useCallback(() => {\n    if (newMeetingTitle.trim()) {\n      handleStartRecording(newMeetingTitle.trim());\n      setShowTitleModal(false);\n      setNewMeetingTitle('');\n    }\n  }, [newMeetingTitle, handleStartRecording]);\n\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      \n      if (currentMeeting && audioBlob) {\n        const updatedMeeting: Meeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started',\n          },\n        };\n\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => \n          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)\n        );\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n\n  const handleStartTranscription = useCallback(async (meetingId: string) => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting: Meeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date(),\n      },\n    };\n\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: (progress) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: { \n                    ...m.transcriptionStatus, \n                    progress,\n                    state: 'processing' \n                  } \n                }\n              : m\n          ));\n        },\n        onStatusUpdate: (status) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: status \n                }\n              : m\n          ));\n        },\n        enhanceSpeakers: true,\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting: Meeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date(),\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n      \n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence,\n      });\n\n    } catch (error: any) {\n      console.error('❌ Transkribavimo klaida:', error);\n      \n      const errorMeeting: Meeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n\n  const handleCancelTranscription = useCallback((meetingId: string) => {\n    cancelTranscription();\n    \n    setMeetings(prev => prev.map(m => \n      m.id === meetingId \n        ? { \n            ...m, \n            transcriptionStatus: { \n              ...m.transcriptionStatus, \n              state: 'cancelled' as const \n            } \n          }\n        : m\n    ));\n  }, [cancelTranscription]);\n\n  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {\n    editSegment(segmentId, newText);\n    \n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => \n      meeting.id === meetingId\n        ? {\n            ...meeting,\n            transcript: meeting.transcript?.map(segment => \n              segment.id === segmentId \n                ? {\n                    ...segment,\n                    text: newText,\n                    isEdited: true,\n                    editedAt: new Date(),\n                    editedBy: 'user'\n                  }\n                : segment\n            ),\n          }\n        : meeting\n    ));\n  }, [editSegment]);\n\n  const handleSelectMeeting = useCallback((meeting: Meeting) => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n\n  const handleDeleteMeeting = useCallback((meetingId: string) => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if (currentMeeting?.id === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if (selectedMeetingForTranscript?.id === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n\n  const handleExportMeeting = useCallback((meeting: Meeting) => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus,\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);\n    \n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    \n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n\n\n\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed' as const,\n        progress: 100,\n        completedAt: meeting.date,\n      },\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  // Close mobile menu when clicking outside or on escape\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Element;\n      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    if (isMobileMenuOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when mobile menu is open\n      document.body.classList.add('mobile-menu-open');\n    } else {\n      document.body.classList.remove('mobile-menu-open');\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('keydown', handleEscape);\n      document.body.classList.remove('mobile-menu-open');\n    };\n  }, [isMobileMenuOpen]);\n\n  return (\n    <ErrorBoundary>\n      {/* Title Input Modal */}\n      {showTitleModal && (\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl max-w-md w-full p-6 animate-scale-in\">\n            <div className=\"space-y-4\">\n              <div className=\"text-center space-y-2\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-xl border border-blue-400/20\">\n                  <Mic2 className=\"h-6 w-6 text-blue-400\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-white\">Naujas pokalbis</h3>\n                <p className=\"text-sm text-white/60\">Įveskite pokalbio pavadinimą</p>\n              </div>\n\n              <div className=\"space-y-3\">\n                <input\n                  type=\"text\"\n                  value={newMeetingTitle}\n                  onChange={(e) => setNewMeetingTitle(e.target.value)}\n                  onKeyDown={(e) => e.key === 'Enter' && confirmStartRecording()}\n                  className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200\"\n                  placeholder=\"Pokalbio pavadinimas...\"\n                  autoFocus\n                />\n\n                <div className=\"flex gap-3\">\n                  <button\n                    onClick={() => {\n                      setShowTitleModal(false);\n                      setNewMeetingTitle('');\n                    }}\n                    className=\"flex-1 px-4 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white/70 hover:text-white rounded-xl transition-all duration-200 text-sm font-medium\"\n                  >\n                    Atšaukti\n                  </button>\n                  <button\n                    onClick={confirmStartRecording}\n                    disabled={!newMeetingTitle.trim()}\n                    className=\"flex-1 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 disabled:from-blue-600/50 disabled:to-indigo-600/50 text-white rounded-xl transition-all duration-200 text-sm font-medium disabled:cursor-not-allowed\"\n                  >\n                    Pradėti\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"min-h-screen elegant-background font-inter\">\n        {/* Elegant Grid Pattern */}\n        <div className=\"elegant-grid\"></div>\n\n        {/* Sophisticated Background Elements */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl\"></div>\n          <div className=\"absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl\"></div>\n        </div>\n\n        {/* Content Wrapper */}\n        <div className=\"relative z-10\">\n          {/* Glassmorphic Header */}\n          <header className=\"fixed top-0 left-0 right-0 z-50 glassmorphic-header\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6\">\n              <div className=\"flex items-center justify-between h-16\">\n                {/* Logo - Always visible */}\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\">\n                    <Mic2 className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div>\n                    <h1 className=\"text-white font-semibold text-lg bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\">\n                      MOM App\n                    </h1>\n                    <p className=\"text-white/60 text-xs font-medium hidden sm:block\">Meeting Recording & Transcription</p>\n                  </div>\n                </div>\n\n                {/* Compact Desktop Navigation */}\n                <div className=\"hidden md:flex items-center gap-3\">\n                  <div className=\"bg-white/8 backdrop-blur-sm rounded-lg border border-white/15 shadow-md\">\n                    <WhisperStatusIndicator />\n                  </div>\n                  <div className=\"flex bg-white/8 backdrop-blur-sm rounded-lg p-0.5 border border-white/15 shadow-md\">\n                    <button\n                      onClick={() => setActiveView('recording')}\n                      className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-300 ${\n                        activeView === 'recording'\n                          ? 'bg-gradient-to-r from-blue-500/70 to-purple-600/70 text-white shadow-md border border-white/15'\n                          : 'text-white/60 hover:text-white hover:bg-white/8'\n                      }`}\n                    >\n                      Įrašymas\n                    </button>\n                    <button\n                      onClick={() => setActiveView('transcription')}\n                      className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-300 ${\n                        activeView === 'transcription'\n                          ? 'bg-gradient-to-r from-blue-500/70 to-purple-600/70 text-white shadow-md border border-white/15'\n                          : 'text-white/60 hover:text-white hover:bg-white/8'\n                      }`}\n                    >\n                      Transkribavimas\n                    </button>\n                    <button\n                      onClick={() => setActiveView('transcript')}\n                      className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-300 ${\n                        activeView === 'transcript'\n                          ? 'bg-gradient-to-r from-blue-500/70 to-purple-600/70 text-white shadow-md border border-white/15'\n                          : 'text-white/60 hover:text-white hover:bg-white/8'\n                      }`}\n                    >\n                      Rezultatai\n                    </button>\n                  </div>\n                </div>\n\n                {/* Compact Desktop Action Buttons */}\n                <div className=\"hidden md:flex items-center gap-2\">\n                  {meetings.length === 0 && (\n                    <button\n                      onClick={loadDemoData}\n                      className=\"px-3 py-1.5 text-xs font-medium text-white/70 hover:text-white bg-white/8 hover:bg-white/15 backdrop-blur-sm rounded-md transition-all duration-300 border border-white/15 shadow-md\"\n                    >\n                      <TestTube className=\"h-3 w-3 mr-1.5 inline\" />\n                      Demo\n                    </button>\n                  )}\n                  <button\n                    onClick={() => {\n                      if (!recordingState.isRecording) {\n                        handleStartRecordingWithTitle();\n                      }\n                    }}\n                    disabled={recordingState.isRecording}\n                    className=\"px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500/70 to-purple-600/70 hover:from-blue-600/80 hover:to-purple-700/80 disabled:from-gray-500/40 disabled:to-gray-600/40 backdrop-blur-sm rounded-md transition-all duration-300 flex items-center gap-1.5 shadow-md border border-white/15 disabled:cursor-not-allowed\"\n                  >\n                    <Plus className=\"h-3 w-3\" />\n                    Naujas pokalbis\n                  </button>\n                </div>\n\n                {/* Compact Mobile menu button */}\n                <div className=\"md:hidden flex items-center\">\n                  <button\n                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                    className=\"p-1.5 rounded-md bg-white/8 hover:bg-white/15 border border-white/15 shadow-md transition-all duration-300\"\n                    aria-expanded={isMobileMenuOpen}\n                  >\n                    <span className=\"sr-only\">Open main menu</span>\n                    {isMobileMenuOpen ? (\n                      <X className=\"h-4 w-4 text-white\" />\n                    ) : (\n                      <Menu className=\"h-4 w-4 text-white\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Mobile menu, show/hide based on menu state */}\n            <div className={`md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`}>\n              <div className=\"px-4 pt-2 pb-4 space-y-3 glassmorphic-card m-2 rounded-xl border border-white/20 shadow-lg mobile-menu-container\">\n                {/* Mobile Navigation Pills */}\n                <div className=\"flex flex-col space-y-2\">\n                  <button\n                    onClick={() => {\n                      setActiveView('recording');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${\n                      activeView === 'recording'\n                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'\n                    }`}\n                  >\n                    <Mic2 className=\"h-4 w-4 mr-2\" />\n                    Įrašymas\n                  </button>\n                  <button\n                    onClick={() => {\n                      setActiveView('transcription');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${\n                      activeView === 'transcription'\n                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'\n                    }`}\n                  >\n                    <Zap className=\"h-4 w-4 mr-2\" />\n                    Transkribavimas\n                  </button>\n                  <button\n                    onClick={() => {\n                      setActiveView('transcript');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${\n                      activeView === 'transcript'\n                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'\n                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'\n                    }`}\n                  >\n                    <Headphones className=\"h-4 w-4 mr-2\" />\n                    Rezultatai\n                  </button>\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className=\"pt-2 border-t border-white/10\">\n                  <div className=\"flex flex-col space-y-2\">\n                    {meetings.length === 0 && (\n                      <button\n                        onClick={() => {\n                          loadDemoData();\n                          setIsMobileMenuOpen(false);\n                        }}\n                        className=\"px-4 py-3 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg flex items-center\"\n                      >\n                        <TestTube className=\"h-4 w-4 mr-2\" />\n                        Demo\n                      </button>\n                    )}\n                    <button\n                      onClick={() => {\n                        setIsMobileMenuOpen(false);\n                        if (!recordingState.isRecording) {\n                          handleStartRecordingWithTitle();\n                        }\n                      }}\n                      disabled={recordingState.isRecording}\n                      className=\"px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 disabled:from-gray-500/50 disabled:to-gray-600/50 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20 disabled:cursor-not-allowed\"\n                    >\n                      <Plus className=\"h-4 w-4\" />\n                      Naujas pokalbis\n                    </button>\n\n                    {/* Whisper Status in Mobile Menu */}\n                    <div className=\"px-4 py-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-white/70 text-sm mr-2\">Whisper Status:</span>\n                        <WhisperStatusIndicator />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n\n\n\n\n        {/* Main Content */}\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 py-8 pt-24 md:pt-24\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-8 min-h-[calc(100vh-140px)]\">\n\n            {/* Main Content Area */}\n            <div className=\"lg:col-span-3\">\n              {/* Recording View */}\n              {activeView === 'recording' && (\n                <div className=\"relative overflow-hidden rounded-3xl h-full\">\n                  {/* Enhanced Glassmorphic Background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-700/20 backdrop-blur-2xl\"></div>\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/5\"></div>\n                  <div className=\"absolute inset-[1px] bg-gradient-to-br from-white/8 via-white/4 to-transparent rounded-3xl\"></div>\n\n                  {/* Sophisticated Border */}\n                  <div className=\"absolute inset-0 rounded-3xl border-2 border-slate-400/20 shadow-2xl shadow-black/40\"></div>\n\n                  {/* Subtle Inner Glow */}\n                  <div className=\"absolute inset-4 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-50\"></div>\n\n                  <div className=\"relative z-10 h-full flex flex-col\">\n                    {/* Compact Header */}\n                    <div className=\"p-4 border-b border-white/8\">\n                      <div className=\"flex items-center gap-3\">\n                        <div className=\"relative\">\n                          <div className=\"w-8 h-8 bg-gradient-to-br from-slate-700/70 via-slate-600/60 to-slate-500/50 backdrop-blur-lg rounded-lg flex items-center justify-center shadow-lg border border-slate-400/25\">\n                            <Mic2 className=\"h-4 w-4 text-white drop-shadow-sm\" />\n                          </div>\n                          <div className=\"absolute top-0.5 left-0.5 w-1.5 h-1.5 bg-white/15 rounded-full blur-xs\"></div>\n                        </div>\n                        <div className=\"space-y-0.5\">\n                          <h2 className=\"text-sm font-semibold bg-gradient-to-r from-white via-slate-100 to-slate-200 bg-clip-text text-transparent tracking-tight\">\n                            Pokalbio įrašymas\n                          </h2>\n                          <p className=\"text-xs text-slate-300/60 font-medium\">\n                            Pradėkite naują pokalbio įrašymą\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Enhanced Content Area */}\n                    <div className=\"flex-1 flex flex-col min-h-[500px]\">\n                      {/* Use our enhanced RecordingPanel */}\n                      <RecordingPanel\n                        recordingState={recordingState}\n                        currentMeeting={currentMeeting}\n                        onStartRecording={handleStartRecording}\n                        onStopRecording={handleStopRecording}\n                        onPauseRecording={pauseRecording}\n                        onResumeRecording={resumeRecording}\n                      />\n\n                      {/* Compact Features Section */}\n                      {!recordingState.isRecording && (\n                        <div className=\"px-6 pb-6\">\n                          <div className=\"grid grid-cols-3 gap-3 max-w-md mx-auto\">\n                            <div className=\"text-center space-y-2 group cursor-default\">\n                              <div className=\"w-8 h-8 bg-gradient-to-br from-slate-600/15 via-slate-500/12 to-slate-400/8 rounded-lg flex items-center justify-center mx-auto border border-slate-400/15 group-hover:scale-105 group-hover:border-blue-400/25 group-hover:bg-blue-500/8 transition-all duration-300 shadow-md\">\n                                <Zap className=\"h-3.5 w-3.5 text-slate-300 group-hover:text-blue-300 transition-colors duration-300\" />\n                              </div>\n                              <p className=\"text-xs text-slate-300/70 font-medium leading-tight\">\n                                Automatinis<br />transkribavimas\n                              </p>\n                            </div>\n                            <div className=\"text-center space-y-2 group cursor-default\">\n                              <div className=\"w-8 h-8 bg-gradient-to-br from-slate-600/15 via-slate-500/12 to-slate-400/8 rounded-lg flex items-center justify-center mx-auto border border-slate-400/15 group-hover:scale-105 group-hover:border-purple-400/25 group-hover:bg-purple-500/8 transition-all duration-300 shadow-md\">\n                                <Headphones className=\"h-3.5 w-3.5 text-slate-300 group-hover:text-purple-300 transition-colors duration-300\" />\n                              </div>\n                              <p className=\"text-xs text-slate-300/70 font-medium leading-tight\">\n                                Aukšta garso<br />kokybė\n                              </p>\n                            </div>\n                            <div className=\"text-center space-y-2 group cursor-default\">\n                              <div className=\"w-8 h-8 bg-gradient-to-br from-slate-600/15 via-slate-500/12 to-slate-400/8 rounded-lg flex items-center justify-center mx-auto border border-slate-400/15 group-hover:scale-105 group-hover:border-indigo-400/25 group-hover:bg-indigo-500/8 transition-all duration-300 shadow-md\">\n                                <Settings className=\"h-3.5 w-3.5 text-slate-300 group-hover:text-indigo-300 transition-colors duration-300\" />\n                              </div>\n                              <p className=\"text-xs text-slate-300/70 font-medium leading-tight\">\n                                Pažangūs<br />nustatymai\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Recording Active State */}\n              {activeView === 'recording' && recordingState.isRecording && (\n                <div className=\"relative overflow-hidden rounded-3xl h-full\">\n                  {/* Enhanced Glassmorphic Background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-700/20 backdrop-blur-2xl\"></div>\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/5\"></div>\n                  <div className=\"absolute inset-[1px] bg-gradient-to-br from-white/8 via-white/4 to-transparent rounded-3xl\"></div>\n\n                  {/* Sophisticated Border */}\n                  <div className=\"absolute inset-0 rounded-3xl border-2 border-slate-400/20 shadow-2xl shadow-black/40\"></div>\n\n                  <div className=\"relative z-10 h-full flex flex-col\">\n                    {/* Enhanced Content Area */}\n                    <div className=\"flex-1 flex flex-col min-h-[500px]\">\n                      {/* Use our enhanced RecordingPanel */}\n                      <RecordingPanel\n                        recordingState={recordingState}\n                        currentMeeting={currentMeeting}\n                        onStartRecording={handleStartRecording}\n                        onStopRecording={handleStopRecording}\n                        onPauseRecording={pauseRecording}\n                        onResumeRecording={resumeRecording}\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n\n\n\n              {/* Transcription View */}\n              {activeView === 'transcription' && (\n                <div className=\"glassmorphic-card rounded-2xl h-full\">\n                  {/* Header */}\n                  <div className=\"p-6 border-b border-white/20\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\">\n                        <Zap className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-xl font-semibold text-white\">Transkribavimas</h2>\n                        <p className=\"text-sm text-white/60\">Audio failų konvertavimas į tekstą naudojant AI</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <TranscriptionManager\n                      meetings={meetings}\n                      onStartTranscription={handleStartTranscription}\n                      onCancelTranscription={handleCancelTranscription}\n                      isTranscribing={isTranscribing}\n                      currentTranscriptionId={currentTranscriptionId}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onViewResults={() => setActiveView('transcript')}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Transcript View */}\n              {activeView === 'transcript' && (\n                <div className=\"glassmorphic-card rounded-2xl h-full\">\n                  {/* Header */}\n                  <div className=\"p-6 border-b border-white/20\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\">\n                        <Headphones className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-xl font-semibold text-white\">Rezultatai</h2>\n                        <p className=\"text-sm text-white/60\">Peržiūrėkite ir redaguokite transkribavimo rezultatus</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <ProfessionalTranscriptViewer\n                      meetings={meetings}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onGoToTranscription={() => setActiveView('transcription')}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Enhanced Sidebar - Meetings List */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"relative overflow-hidden rounded-3xl h-full\">\n                {/* Sophisticated Glassmorphic Background */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-700/20 backdrop-blur-2xl\"></div>\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/5\"></div>\n                <div className=\"absolute inset-[1px] bg-gradient-to-br from-white/8 via-white/4 to-transparent rounded-3xl\"></div>\n\n                {/* Elegant Border */}\n                <div className=\"absolute inset-0 rounded-3xl border-2 border-slate-400/20 shadow-2xl shadow-black/40\"></div>\n\n                {/* Subtle Inner Glow */}\n                <div className=\"absolute inset-4 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-50\"></div>\n\n                <div className=\"relative z-10 h-full flex flex-col\">\n                  {/* Compact Sidebar Header */}\n                  <div className=\"p-4 border-b border-white/8\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"relative\">\n                        <div className=\"w-8 h-8 bg-gradient-to-br from-indigo-700/70 via-indigo-600/60 to-purple-600/50 backdrop-blur-lg rounded-lg flex items-center justify-center shadow-lg border border-indigo-400/25\">\n                          <List className=\"h-4 w-4 text-white drop-shadow-sm\" />\n                        </div>\n                        <div className=\"absolute top-0.5 left-0.5 w-1.5 h-1.5 bg-white/15 rounded-full blur-xs\"></div>\n                      </div>\n                      <div className=\"space-y-0.5\">\n                        <h2 className=\"text-sm font-semibold bg-gradient-to-r from-white via-slate-100 to-slate-200 bg-clip-text text-transparent tracking-tight\">\n                          Pokalbiai\n                        </h2>\n                        <p className=\"text-xs text-slate-300/60 font-medium\">\n                          Visi pokalbiai ({meetings.length})\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Compact Sidebar Content */}\n                  <div className=\"flex-1 p-4\">\n                    {meetings.length === 0 ? (\n                      <div className=\"text-center py-8 space-y-4\">\n                        <div className=\"relative mx-auto w-12 h-12\">\n                          <div className=\"absolute inset-0 bg-gradient-to-br from-slate-600/15 via-slate-500/12 to-slate-400/8 rounded-xl\"></div>\n                          <div className=\"absolute inset-[1px] bg-gradient-to-br from-white/6 to-transparent rounded-xl\"></div>\n                          <div className=\"relative w-full h-full flex items-center justify-center border border-slate-400/15 rounded-xl shadow-lg\">\n                            <List className=\"h-5 w-5 text-slate-300/50\" />\n                          </div>\n                        </div>\n                        <div className=\"space-y-2\">\n                          <h3 className=\"text-white font-medium text-sm tracking-tight\">Nėra pokalbių</h3>\n                          <p className=\"text-slate-300/60 text-xs leading-relaxed max-w-xs mx-auto\">\n                            Pradėkite naują pokalbį, kad pamatytumėte jį čia\n                          </p>\n                        </div>\n                      </div>\n                    ) : (\n                      <MeetingsList\n                        meetings={meetings}\n                        currentMeeting={currentMeeting}\n                        onSelectMeeting={handleSelectMeeting}\n                        onDeleteMeeting={handleDeleteMeeting}\n                        onExportMeeting={() => {}}\n                        activeView=\"list\"\n                      />\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n        </div> {/* Close content wrapper */}\n      </div>\n    </ErrorBoundary>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,SACEC,YAAY,EACZC,aAAa,EACbC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,EAE5BC,cAAc,QACT,cAAc;AACrB,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,SAAS;AAE5D,SAASC,kBAAkB,QAAQ,kBAAkB;AAErD,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAUC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AACrG,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC6B,4BAA4B,EAAEC,+BAA+B,CAAC,GAAG9B,QAAQ,CAAiB,IAAI,CAAC;EACtG,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAA+C,WAAW,CAAC;EACvG,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM;IAAEuC,cAAc;IAAEC,cAAc;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAgB,CAAC,GAAGlC,gBAAgB,CAAC,CAAC;EAC7G,MAAM;IACJmC,UAAU;IACVC,cAAc;IACdC,uBAAuB;IACvBC,mBAAmB;IACnBC,WAAW;IACXC,eAAe;IACfC,UAAU;IACVC,sBAAsB;IACtBC,QAAQ;IACRC;EACF,CAAC,GAAG3C,gBAAgB,CAAC,CAAC;EAEtB,MAAM4C,oBAAoB,GAAGrD,WAAW,CAAC,MAAOsD,KAAa,IAAK;IAChE,IAAI;MACF,MAAMf,cAAc,CAAC,CAAC;MAEtB,MAAMgB,UAAmB,GAAG;QAC1BC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBL,KAAK,EAAEA,KAAK;QACZM,IAAI,EAAE,IAAIH,IAAI,CAAC,CAAC;QAChBI,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,WAAW;QACnBC,mBAAmB,EAAE;UACnBC,KAAK,EAAE;QACT;MACF,CAAC;MAEDrC,iBAAiB,CAAC4B,UAAU,CAAC;MAC7B9B,WAAW,CAACwC,IAAI,IAAI,CAACV,UAAU,EAAE,GAAGU,IAAI,CAAC,CAAC;MAC1ClC,aAAa,CAAC,WAAW,CAAC;MAC1BiB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAAC3B,cAAc,EAAES,eAAe,CAAC,CAAC;EAErC,MAAMoB,6BAA6B,GAAGpE,WAAW,CAAC,MAAM;IACtDqC,kBAAkB,CAAC,YAAY,IAAIoB,IAAI,CAAC,CAAC,CAACY,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;IACpElC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmC,qBAAqB,GAAGtE,WAAW,CAAC,MAAM;IAC9C,IAAIoC,eAAe,CAACmC,IAAI,CAAC,CAAC,EAAE;MAC1BlB,oBAAoB,CAACjB,eAAe,CAACmC,IAAI,CAAC,CAAC,CAAC;MAC5CpC,iBAAiB,CAAC,KAAK,CAAC;MACxBE,kBAAkB,CAAC,EAAE,CAAC;IACxB;EACF,CAAC,EAAE,CAACD,eAAe,EAAEiB,oBAAoB,CAAC,CAAC;EAE3C,MAAMmB,mBAAmB,GAAGxE,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMyE,SAAS,GAAG,MAAMjC,aAAa,CAAC,CAAC;MAEvC,IAAId,cAAc,IAAI+C,SAAS,EAAE;QAC/B,MAAMC,cAAuB,GAAG;UAC9B,GAAGhD,cAAc;UACjBoC,MAAM,EAAE,WAAW;UACnBD,QAAQ,EAAEc,IAAI,CAACC,KAAK,CAAC,CAACnB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGhC,cAAc,CAACkC,IAAI,CAACiB,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;UACzEJ,SAAS;UACTV,mBAAmB,EAAE;YACnBC,KAAK,EAAE;UACT;QACF,CAAC;QAEDrC,iBAAiB,CAAC+C,cAAc,CAAC;QACjCjD,WAAW,CAACwC,IAAI,IACdA,IAAI,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK9B,cAAc,CAAC8B,EAAE,GAAGkB,cAAc,GAAGK,CAAC,CAC/D,CAAC;;QAED;QACAhD,aAAa,CAAC,eAAe,CAAC;MAChC;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDc,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC,EAAE,CAACxC,aAAa,EAAEd,cAAc,CAAC,CAAC;EAEnC,MAAMuD,wBAAwB,GAAGjF,WAAW,CAAC,MAAOkF,SAAiB,IAAK;IACxE,MAAMC,OAAO,GAAG3D,QAAQ,CAAC4D,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,CAAC;IACtD,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACV,SAAS,EAAE;;IAEpC;IACA,MAAMC,cAAuB,GAAG;MAC9B,GAAGS,OAAO;MACVpB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,SAAS;QAChBqB,SAAS,EAAE,IAAI5B,IAAI,CAAC;MACtB;IACF,CAAC;IAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GAAGR,cAAc,GAAGK,CAAC,CAAC,CAAC;IAC3ElD,+BAA+B,CAAC6C,cAAc,CAAC;IAE/C,IAAI;MACF;MACA,MAAMY,MAAM,GAAG,MAAMzC,uBAAuB,CAACsC,OAAO,CAACV,SAAS,EAAES,SAAS,EAAE;QACzEK,UAAU,EAAGpC,QAAQ,IAAK;UACxB1B,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GACd;YACE,GAAGH,CAAC;YACJhB,mBAAmB,EAAE;cACnB,GAAGgB,CAAC,CAAChB,mBAAmB;cACxBZ,QAAQ;cACRa,KAAK,EAAE;YACT;UACF,CAAC,GACDe,CACN,CAAC,CAAC;QACJ,CAAC;QACDS,cAAc,EAAG1B,MAAM,IAAK;UAC1BrC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GACd;YACE,GAAGH,CAAC;YACJhB,mBAAmB,EAAED;UACvB,CAAC,GACDiB,CACN,CAAC,CAAC;QACJ,CAAC;QACDU,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAMC,gBAAyB,GAAG;QAChC,GAAGhB,cAAc;QACjB/B,UAAU,EAAE2C,MAAM,CAACK,QAAQ;QAC3BC,YAAY,EAAEN,MAAM,CAACO,QAAQ;QAC7BC,QAAQ,EAAER,MAAM,CAACQ,QAAQ;QACzB/B,mBAAmB,EAAE;UACnBC,KAAK,EAAE,WAAW;UAClBb,QAAQ,EAAE,GAAG;UACbkC,SAAS,EAAEX,cAAc,CAACX,mBAAmB,CAACsB,SAAS;UACvDU,WAAW,EAAE,IAAItC,IAAI,CAAC;QACxB;MACF,CAAC;MAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GAAGQ,gBAAgB,GAAGX,CAAC,CAAC,CAAC;MAC7ElD,+BAA+B,CAAC6D,gBAAgB,CAAC;;MAEjD;MACA3D,aAAa,CAAC,YAAY,CAAC;MAE3BoC,OAAO,CAAC6B,GAAG,CAAC,sCAAsC,EAAE;QAClDL,QAAQ,EAAEL,MAAM,CAACK,QAAQ,CAACM,MAAM;QAChCJ,QAAQ,EAAEP,MAAM,CAACO,QAAQ,CAACI,MAAM;QAChCC,KAAK,EAAEZ,MAAM,CAACQ,QAAQ,CAACK,UAAU;QACjCC,UAAU,EAAEd,MAAM,CAACQ,QAAQ,CAACO;MAC9B,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOnC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAEhD,MAAMoC,YAAqB,GAAG;QAC5B,GAAG5B,cAAc;QACjBX,mBAAmB,EAAE;UACnBC,KAAK,EAAE,QAAQ;UACfE,KAAK,EAAEA,KAAK,CAACqC,OAAO;UACpBlB,SAAS,EAAEX,cAAc,CAACX,mBAAmB,CAACsB;QAChD;MACF,CAAC;MAED5D,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GAAGoB,YAAY,GAAGvB,CAAC,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAACvD,QAAQ,EAAEqB,uBAAuB,CAAC,CAAC;EAEvC,MAAM2D,yBAAyB,GAAGxG,WAAW,CAAEkF,SAAiB,IAAK;IACnEpC,mBAAmB,CAAC,CAAC;IAErBrB,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GACd;MACE,GAAGH,CAAC;MACJhB,mBAAmB,EAAE;QACnB,GAAGgB,CAAC,CAAChB,mBAAmB;QACxBC,KAAK,EAAE;MACT;IACF,CAAC,GACDe,CACN,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjC,mBAAmB,CAAC,CAAC;EAEzB,MAAM2D,iBAAiB,GAAGzG,WAAW,CAAC,CAACkF,SAAiB,EAAEwB,SAAiB,EAAEC,OAAe,KAAK;IAC/F5D,WAAW,CAAC2D,SAAS,EAAEC,OAAO,CAAC;;IAE/B;IACAlF,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACK,OAAO;MAAA,IAAAyB,mBAAA;MAAA,OAClCzB,OAAO,CAAC3B,EAAE,KAAK0B,SAAS,GACpB;QACE,GAAGC,OAAO;QACVxC,UAAU,GAAAiE,mBAAA,GAAEzB,OAAO,CAACxC,UAAU,cAAAiE,mBAAA,uBAAlBA,mBAAA,CAAoB9B,GAAG,CAAC+B,OAAO,IACzCA,OAAO,CAACrD,EAAE,KAAKkD,SAAS,GACpB;UACE,GAAGG,OAAO;UACVC,IAAI,EAAEH,OAAO;UACbI,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,IAAIvD,IAAI,CAAC,CAAC;UACpBwD,QAAQ,EAAE;QACZ,CAAC,GACDJ,OACN;MACF,CAAC,GACD1B,OAAO;IAAA,CACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpC,WAAW,CAAC,CAAC;EAEjB,MAAMmE,mBAAmB,GAAGlH,WAAW,CAAEmF,OAAgB,IAAK;IAC5DxD,iBAAiB,CAACwD,OAAO,CAAC;IAC1B,IAAIA,OAAO,CAACxC,UAAU,IAAIwC,OAAO,CAACxC,UAAU,CAACsD,MAAM,GAAG,CAAC,EAAE;MACvDpE,+BAA+B,CAACsD,OAAO,CAAC;MACxCpD,aAAa,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoF,mBAAmB,GAAGnH,WAAW,CAAEkF,SAAiB,IAAK;IAC7DzD,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACmD,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,CAAC,CAAC;IACzD,IAAI,CAAAxD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B,EAAE,MAAK0B,SAAS,EAAE;MACpCvD,iBAAiB,CAAC,IAAI,CAAC;IACzB;IACA,IAAI,CAAAC,4BAA4B,aAA5BA,4BAA4B,uBAA5BA,4BAA4B,CAAE4B,EAAE,MAAK0B,SAAS,EAAE;MAClDrD,+BAA+B,CAAC,IAAI,CAAC;IACvC;EACF,CAAC,EAAE,CAACH,cAAc,EAAEE,4BAA4B,CAAC,CAAC;EAElD,MAAMyF,mBAAmB,GAAGrH,WAAW,CAAEmF,OAAgB,IAAK;IAC5D,MAAMmC,UAAU,GAAG;MACjBhE,KAAK,EAAE6B,OAAO,CAAC7B,KAAK;MACpBM,IAAI,EAAEuB,OAAO,CAACvB,IAAI,CAAC2D,WAAW,CAAC,CAAC;MAChC1D,QAAQ,EAAEsB,OAAO,CAACtB,QAAQ;MAC1BlB,UAAU,EAAEwC,OAAO,CAACxC,UAAU,IAAIA,UAAU;MAC5CiD,YAAY,EAAET,OAAO,CAACS,YAAY,IAAI,EAAE;MACxCE,QAAQ,EAAEX,OAAO,CAACW,QAAQ,IAAI,CAAC,CAAC;MAChC/B,mBAAmB,EAAEoB,OAAO,CAACpB;IAC/B,CAAC;IAED,MAAMyD,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACJ,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMK,OAAO,GAAG,sCAAsC,GAAEC,kBAAkB,CAACJ,OAAO,CAAC;IAEnF,MAAMK,qBAAqB,GAAG,WAAW1C,OAAO,CAAC7B,KAAK,CAACwE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI5C,OAAO,CAACvB,IAAI,CAAC2D,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAE5I,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC/CF,WAAW,CAACG,YAAY,CAAC,MAAM,EAAET,OAAO,CAAC;IACzCM,WAAW,CAACG,YAAY,CAAC,UAAU,EAAEP,qBAAqB,CAAC;IAC3DI,WAAW,CAACI,KAAK,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC1F,UAAU,CAAC,CAAC;EAIhB,MAAM2F,YAAY,GAAGtI,WAAW,CAAC,MAAM;IACrC,MAAMuI,YAAY,GAAG7H,kBAAkB,CAAC,CAAC,CAACoE,GAAG,CAACK,OAAO,KAAK;MACxD,GAAGA,OAAO;MACVpB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,WAAoB;QAC3Bb,QAAQ,EAAE,GAAG;QACb4C,WAAW,EAAEZ,OAAO,CAACvB;MACvB;IACF,CAAC,CAAC,CAAC;IACHnC,WAAW,CAAC8G,YAAY,CAAC;IACzBxG,aAAa,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9B,SAAS,CAAC,MAAM;IACd,MAAMuI,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAiB;MACtC,IAAI1G,gBAAgB,IAAI,CAAC0G,MAAM,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;QACjE1G,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED,MAAM2G,YAAY,GAAIH,KAAoB,IAAK;MAC7C,IAAIA,KAAK,CAACI,GAAG,KAAK,QAAQ,IAAI7G,gBAAgB,EAAE;QAC9CC,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED,IAAID,gBAAgB,EAAE;MACpBkG,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1DN,QAAQ,CAACY,gBAAgB,CAAC,SAAS,EAAEF,YAAY,CAAC;MAClD;MACAV,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjD,CAAC,MAAM;MACLf,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC;IACpD;IAEA,OAAO,MAAM;MACXhB,QAAQ,CAACiB,mBAAmB,CAAC,WAAW,EAAEX,kBAAkB,CAAC;MAC7DN,QAAQ,CAACiB,mBAAmB,CAAC,SAAS,EAAEP,YAAY,CAAC;MACrDV,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAAClH,gBAAgB,CAAC,CAAC;EAEtB,oBACEX,OAAA,CAAClB,aAAa;IAAAiJ,QAAA,GAEXlH,cAAc,iBACbb,OAAA;MAAKgI,SAAS,EAAC,sFAAsF;MAAAD,QAAA,eACnG/H,OAAA;QAAKgI,SAAS,EAAC,qHAAqH;QAAAD,QAAA,eAClI/H,OAAA;UAAKgI,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB/H,OAAA;YAAKgI,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBACpC/H,OAAA;cAAKgI,SAAS,EAAC,4IAA4I;cAAAD,QAAA,eACzJ/H,OAAA,CAACR,IAAI;gBAACwI,SAAS,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNpI,OAAA;cAAIgI,SAAS,EAAC,kCAAkC;cAAAD,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEpI,OAAA;cAAGgI,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAENpI,OAAA;YAAKgI,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB/H,OAAA;cACEqI,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEvH,eAAgB;cACvBwH,QAAQ,EAAGC,CAAC,IAAKxH,kBAAkB,CAACwH,CAAC,CAACnB,MAAM,CAACiB,KAAK,CAAE;cACpDG,SAAS,EAAGD,CAAC,IAAKA,CAAC,CAAChB,GAAG,KAAK,OAAO,IAAIvE,qBAAqB,CAAC,CAAE;cAC/D+E,SAAS,EAAC,2MAA2M;cACrNU,WAAW,EAAC,yBAAyB;cACrCC,SAAS;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEFpI,OAAA;cAAKgI,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB/H,OAAA;gBACE4I,OAAO,EAAEA,CAAA,KAAM;kBACb9H,iBAAiB,CAAC,KAAK,CAAC;kBACxBE,kBAAkB,CAAC,EAAE,CAAC;gBACxB,CAAE;gBACFgH,SAAS,EAAC,kKAAkK;gBAAAD,QAAA,EAC7K;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpI,OAAA;gBACE4I,OAAO,EAAE3F,qBAAsB;gBAC/B4F,QAAQ,EAAE,CAAC9H,eAAe,CAACmC,IAAI,CAAC,CAAE;gBAClC8E,SAAS,EAAC,+PAA+P;gBAAAD,QAAA,EAC1Q;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDpI,OAAA;MAAKgI,SAAS,EAAC,4CAA4C;MAAAD,QAAA,gBAEzD/H,OAAA;QAAKgI,SAAS,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGpCpI,OAAA;QAAKgI,SAAS,EAAC,kCAAkC;QAAAD,QAAA,gBAC/C/H,OAAA;UAAKgI,SAAS,EAAC;QAAgF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGpI,OAAA;UAAKgI,SAAS,EAAC;QAAkF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxGpI,OAAA;UAAKgI,SAAS,EAAC;QAA2H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9I,CAAC,eAGNpI,OAAA;QAAKgI,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAE5B/H,OAAA;UAAQgI,SAAS,EAAC,qDAAqD;UAAAD,QAAA,gBACrE/H,OAAA;YAAKgI,SAAS,EAAC,gCAAgC;YAAAD,QAAA,eAC7C/H,OAAA;cAAKgI,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBAErD/H,OAAA;gBAAKgI,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,gBACtC/H,OAAA;kBAAKgI,SAAS,EAAC,6JAA6J;kBAAAD,QAAA,eAC1K/H,OAAA,CAACR,IAAI;oBAACwI,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNpI,OAAA;kBAAA+H,QAAA,gBACE/H,OAAA;oBAAIgI,SAAS,EAAC,wGAAwG;oBAAAD,QAAA,EAAC;kBAEvH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLpI,OAAA;oBAAGgI,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpI,OAAA;gBAAKgI,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,gBAChD/H,OAAA;kBAAKgI,SAAS,EAAC,yEAAyE;kBAAAD,QAAA,eACtF/H,OAAA,CAACjB,sBAAsB;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNpI,OAAA;kBAAKgI,SAAS,EAAC,oFAAoF;kBAAAD,QAAA,gBACjG/H,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,WAAW,CAAE;oBAC1CsH,SAAS,EAAE,0EACTvH,UAAU,KAAK,WAAW,GACtB,gGAAgG,GAChG,iDAAiD,EACpD;oBAAAsH,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpI,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,eAAe,CAAE;oBAC9CsH,SAAS,EAAE,0EACTvH,UAAU,KAAK,eAAe,GAC1B,gGAAgG,GAChG,iDAAiD,EACpD;oBAAAsH,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpI,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,YAAY,CAAE;oBAC3CsH,SAAS,EAAE,0EACTvH,UAAU,KAAK,YAAY,GACvB,gGAAgG,GAChG,iDAAiD,EACpD;oBAAAsH,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpI,OAAA;gBAAKgI,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,GAC/C5H,QAAQ,CAACyE,MAAM,KAAK,CAAC,iBACpB5E,OAAA;kBACE4I,OAAO,EAAE3B,YAAa;kBACtBe,SAAS,EAAC,sLAAsL;kBAAAD,QAAA,gBAEhM/H,OAAA,CAACP,QAAQ;oBAACuI,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,QAEhD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACDpI,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAAC3H,cAAc,CAAC6H,WAAW,EAAE;sBAC/B/F,6BAA6B,CAAC,CAAC;oBACjC;kBACF,CAAE;kBACF8F,QAAQ,EAAE5H,cAAc,CAAC6H,WAAY;kBACrCd,SAAS,EAAC,8UAA8U;kBAAAD,QAAA,gBAExV/H,OAAA,CAACT,IAAI;oBAACyI,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNpI,OAAA;gBAAKgI,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eAC1C/H,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAMhI,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBACtDqH,SAAS,EAAC,4GAA4G;kBACtH,iBAAerH,gBAAiB;kBAAAoH,QAAA,gBAEhC/H,OAAA;oBAAMgI,SAAS,EAAC,SAAS;oBAAAD,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC9CzH,gBAAgB,gBACfX,OAAA,CAACF,CAAC;oBAACkI,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpCpI,OAAA,CAACH,IAAI;oBAACmI,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpI,OAAA;YAAKgI,SAAS,EAAE,aAAarH,gBAAgB,GAAG,OAAO,GAAG,QAAQ,EAAG;YAAAoH,QAAA,eACnE/H,OAAA;cAAKgI,SAAS,EAAC,kHAAkH;cAAAD,QAAA,gBAE/H/H,OAAA;gBAAKgI,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,gBACtC/H,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACblI,aAAa,CAAC,WAAW,CAAC;oBAC1BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACFoH,SAAS,EAAE,0FACTvH,UAAU,KAAK,WAAW,GACtB,gGAAgG,GAChG,yEAAyE,EAC5E;kBAAAsH,QAAA,gBAEH/H,OAAA,CAACR,IAAI;oBAACwI,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACblI,aAAa,CAAC,eAAe,CAAC;oBAC9BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACFoH,SAAS,EAAE,0FACTvH,UAAU,KAAK,eAAe,GAC1B,gGAAgG,GAChG,yEAAyE,EAC5E;kBAAAsH,QAAA,gBAEH/H,OAAA,CAACN,GAAG;oBAACsI,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACblI,aAAa,CAAC,YAAY,CAAC;oBAC3BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACFoH,SAAS,EAAE,0FACTvH,UAAU,KAAK,YAAY,GACvB,gGAAgG,GAChG,yEAAyE,EAC5E;kBAAAsH,QAAA,gBAEH/H,OAAA,CAACV,UAAU;oBAAC0I,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAEzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNpI,OAAA;gBAAKgI,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,eAC5C/H,OAAA;kBAAKgI,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,GACrC5H,QAAQ,CAACyE,MAAM,KAAK,CAAC,iBACpB5E,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAM;sBACb3B,YAAY,CAAC,CAAC;sBACdrG,mBAAmB,CAAC,KAAK,CAAC;oBAC5B,CAAE;oBACFoH,SAAS,EAAC,uMAAuM;oBAAAD,QAAA,gBAEjN/H,OAAA,CAACP,QAAQ;sBAACuI,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,eACDpI,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAM;sBACbhI,mBAAmB,CAAC,KAAK,CAAC;sBAC1B,IAAI,CAACK,cAAc,CAAC6H,WAAW,EAAE;wBAC/B/F,6BAA6B,CAAC,CAAC;sBACjC;oBACF,CAAE;oBACF8F,QAAQ,EAAE5H,cAAc,CAAC6H,WAAY;oBACrCd,SAAS,EAAC,0UAA0U;oBAAAD,QAAA,gBAEpV/H,OAAA,CAACT,IAAI;sBAACyI,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAE9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAGTpI,OAAA;oBAAKgI,SAAS,EAAC,oFAAoF;oBAAAD,QAAA,eACjG/H,OAAA;sBAAKgI,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChC/H,OAAA;wBAAMgI,SAAS,EAAC,4BAA4B;wBAAAD,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnEpI,OAAA,CAACjB,sBAAsB;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAOXpI,OAAA;UAAMgI,SAAS,EAAC,oDAAoD;UAAAD,QAAA,eAClE/H,OAAA;YAAKgI,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBAGvF/H,OAAA;cAAKgI,SAAS,EAAC,eAAe;cAAAD,QAAA,GAE3BtH,UAAU,KAAK,WAAW,iBACzBT,OAAA;gBAAKgI,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAE1D/H,OAAA;kBAAKgI,SAAS,EAAC;gBAAyG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/HpI,OAAA;kBAAKgI,SAAS,EAAC;gBAA4E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClGpI,OAAA;kBAAKgI,SAAS,EAAC;gBAA4F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAGlHpI,OAAA;kBAAKgI,SAAS,EAAC;gBAAsF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAG5GpI,OAAA;kBAAKgI,SAAS,EAAC;gBAAuF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE7GpI,OAAA;kBAAKgI,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBAEjD/H,OAAA;oBAAKgI,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,eAC1C/H,OAAA;sBAAKgI,SAAS,EAAC,yBAAyB;sBAAAD,QAAA,gBACtC/H,OAAA;wBAAKgI,SAAS,EAAC,UAAU;wBAAAD,QAAA,gBACvB/H,OAAA;0BAAKgI,SAAS,EAAC,gLAAgL;0BAAAD,QAAA,eAC7L/H,OAAA,CAACR,IAAI;4BAACwI,SAAS,EAAC;0BAAmC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC,eACNpI,OAAA;0BAAKgI,SAAS,EAAC;wBAAwE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3F,CAAC,eACNpI,OAAA;wBAAKgI,SAAS,EAAC,aAAa;wBAAAD,QAAA,gBAC1B/H,OAAA;0BAAIgI,SAAS,EAAC,2HAA2H;0BAAAD,QAAA,EAAC;wBAE1I;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACLpI,OAAA;0BAAGgI,SAAS,EAAC,uCAAuC;0BAAAD,QAAA,EAAC;wBAErD;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNpI,OAAA;oBAAKgI,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,gBAEjD/H,OAAA,CAACd,cAAc;sBACb+B,cAAc,EAAEA,cAAe;sBAC/BZ,cAAc,EAAEA,cAAe;sBAC/B0I,gBAAgB,EAAE/G,oBAAqB;sBACvCgH,eAAe,EAAE7F,mBAAoB;sBACrC8F,gBAAgB,EAAE7H,cAAe;sBACjC8H,iBAAiB,EAAE7H;oBAAgB;sBAAA4G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,EAGD,CAACnH,cAAc,CAAC6H,WAAW,iBAC1B9I,OAAA;sBAAKgI,SAAS,EAAC,WAAW;sBAAAD,QAAA,eACxB/H,OAAA;wBAAKgI,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD/H,OAAA;0BAAKgI,SAAS,EAAC,4CAA4C;0BAAAD,QAAA,gBACzD/H,OAAA;4BAAKgI,SAAS,EAAC,iRAAiR;4BAAAD,QAAA,eAC9R/H,OAAA,CAACN,GAAG;8BAACsI,SAAS,EAAC;4BAAqF;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpG,CAAC,eACNpI,OAAA;4BAAGgI,SAAS,EAAC,qDAAqD;4BAAAD,QAAA,GAAC,aACtD,eAAA/H,OAAA;8BAAAiI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,mBACnB;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eACNpI,OAAA;0BAAKgI,SAAS,EAAC,4CAA4C;0BAAAD,QAAA,gBACzD/H,OAAA;4BAAKgI,SAAS,EAAC,qRAAqR;4BAAAD,QAAA,eAClS/H,OAAA,CAACV,UAAU;8BAAC0I,SAAS,EAAC;4BAAuF;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7G,CAAC,eACNpI,OAAA;4BAAGgI,SAAS,EAAC,qDAAqD;4BAAAD,QAAA,GAAC,mBACrD,eAAA/H,OAAA;8BAAAiI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACpB;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eACNpI,OAAA;0BAAKgI,SAAS,EAAC,4CAA4C;0BAAAD,QAAA,gBACzD/H,OAAA;4BAAKgI,SAAS,EAAC,qRAAqR;4BAAAD,QAAA,eAClS/H,OAAA,CAACL,QAAQ;8BAACqI,SAAS,EAAC;4BAAuF;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3G,CAAC,eACNpI,OAAA;4BAAGgI,SAAS,EAAC,qDAAqD;4BAAAD,QAAA,GAAC,oBACzD,eAAA/H,OAAA;8BAAAiI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,cAChB;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA3H,UAAU,KAAK,WAAW,IAAIQ,cAAc,CAAC6H,WAAW,iBACvD9I,OAAA;gBAAKgI,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAE1D/H,OAAA;kBAAKgI,SAAS,EAAC;gBAAyG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/HpI,OAAA;kBAAKgI,SAAS,EAAC;gBAA4E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClGpI,OAAA;kBAAKgI,SAAS,EAAC;gBAA4F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAGlHpI,OAAA;kBAAKgI,SAAS,EAAC;gBAAsF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE5GpI,OAAA;kBAAKgI,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eAEjD/H,OAAA;oBAAKgI,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,eAEjD/H,OAAA,CAACd,cAAc;sBACb+B,cAAc,EAAEA,cAAe;sBAC/BZ,cAAc,EAAEA,cAAe;sBAC/B0I,gBAAgB,EAAE/G,oBAAqB;sBACvCgH,eAAe,EAAE7F,mBAAoB;sBACrC8F,gBAAgB,EAAE7H,cAAe;sBACjC8H,iBAAiB,EAAE7H;oBAAgB;sBAAA4G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAKA3H,UAAU,KAAK,eAAe,iBAC7BT,OAAA;gBAAKgI,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnD/H,OAAA;kBAAKgI,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3C/H,OAAA;oBAAKgI,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtC/H,OAAA;sBAAKgI,SAAS,EAAC,6JAA6J;sBAAAD,QAAA,eAC1K/H,OAAA,CAACN,GAAG;wBAACsI,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACNpI,OAAA;sBAAA+H,QAAA,gBACE/H,OAAA;wBAAIgI,SAAS,EAAC,kCAAkC;wBAAAD,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrEpI,OAAA;wBAAGgI,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,EAAC;sBAA+C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNpI,OAAA;kBAAKgI,SAAS,EAAC,KAAK;kBAAAD,QAAA,eAClB/H,OAAA,CAAChB,oBAAoB;oBACnBmB,QAAQ,EAAEA,QAAS;oBACnBgJ,oBAAoB,EAAEvF,wBAAyB;oBAC/CwF,qBAAqB,EAAEjE,yBAA0B;oBACjD5D,cAAc,EAAEA,cAAe;oBAC/BM,sBAAsB,EAAEA,sBAAuB;oBAC/CwH,eAAe,EAAEvD,mBAAoB;oBACrCwD,aAAa,EAAEA,CAAA,KAAM5I,aAAa,CAAC,YAAY;kBAAE;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA3H,UAAU,KAAK,YAAY,iBAC1BT,OAAA;gBAAKgI,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnD/H,OAAA;kBAAKgI,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3C/H,OAAA;oBAAKgI,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtC/H,OAAA;sBAAKgI,SAAS,EAAC,+JAA+J;sBAAAD,QAAA,eAC5K/H,OAAA,CAACV,UAAU;wBAAC0I,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACNpI,OAAA;sBAAA+H,QAAA,gBACE/H,OAAA;wBAAIgI,SAAS,EAAC,kCAAkC;wBAAAD,QAAA,EAAC;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChEpI,OAAA;wBAAGgI,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,EAAC;sBAAqD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNpI,OAAA;kBAAKgI,SAAS,EAAC,KAAK;kBAAAD,QAAA,eAClB/H,OAAA,CAACf,4BAA4B;oBAC3BkB,QAAQ,EAAEA,QAAS;oBACnBkJ,eAAe,EAAEvD,mBAAoB;oBACrCyD,mBAAmB,EAAEA,CAAA,KAAM7I,aAAa,CAAC,eAAe;kBAAE;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNpI,OAAA;cAAKgI,SAAS,EAAC,eAAe;cAAAD,QAAA,eAC5B/H,OAAA;gBAAKgI,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAE1D/H,OAAA;kBAAKgI,SAAS,EAAC;gBAAyG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/HpI,OAAA;kBAAKgI,SAAS,EAAC;gBAA4E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClGpI,OAAA;kBAAKgI,SAAS,EAAC;gBAA4F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAGlHpI,OAAA;kBAAKgI,SAAS,EAAC;gBAAsF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAG5GpI,OAAA;kBAAKgI,SAAS,EAAC;gBAAuF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE7GpI,OAAA;kBAAKgI,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBAEjD/H,OAAA;oBAAKgI,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,eAC1C/H,OAAA;sBAAKgI,SAAS,EAAC,yBAAyB;sBAAAD,QAAA,gBACtC/H,OAAA;wBAAKgI,SAAS,EAAC,UAAU;wBAAAD,QAAA,gBACvB/H,OAAA;0BAAKgI,SAAS,EAAC,oLAAoL;0BAAAD,QAAA,eACjM/H,OAAA,CAACJ,IAAI;4BAACoI,SAAS,EAAC;0BAAmC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC,eACNpI,OAAA;0BAAKgI,SAAS,EAAC;wBAAwE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3F,CAAC,eACNpI,OAAA;wBAAKgI,SAAS,EAAC,aAAa;wBAAAD,QAAA,gBAC1B/H,OAAA;0BAAIgI,SAAS,EAAC,2HAA2H;0BAAAD,QAAA,EAAC;wBAE1I;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACLpI,OAAA;0BAAGgI,SAAS,EAAC,uCAAuC;0BAAAD,QAAA,GAAC,kBACnC,EAAC5H,QAAQ,CAACyE,MAAM,EAAC,GACnC;wBAAA;0BAAAqD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNpI,OAAA;oBAAKgI,SAAS,EAAC,YAAY;oBAAAD,QAAA,EACxB5H,QAAQ,CAACyE,MAAM,KAAK,CAAC,gBACpB5E,OAAA;sBAAKgI,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,gBACzC/H,OAAA;wBAAKgI,SAAS,EAAC,4BAA4B;wBAAAD,QAAA,gBACzC/H,OAAA;0BAAKgI,SAAS,EAAC;wBAAiG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACvHpI,OAAA;0BAAKgI,SAAS,EAAC;wBAA+E;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACrGpI,OAAA;0BAAKgI,SAAS,EAAC,yGAAyG;0BAAAD,QAAA,eACtH/H,OAAA,CAACJ,IAAI;4BAACoI,SAAS,EAAC;0BAA2B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpI,OAAA;wBAAKgI,SAAS,EAAC,WAAW;wBAAAD,QAAA,gBACxB/H,OAAA;0BAAIgI,SAAS,EAAC,+CAA+C;0BAAAD,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAChFpI,OAAA;0BAAGgI,SAAS,EAAC,4DAA4D;0BAAAD,QAAA,EAAC;wBAE1E;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,gBAENpI,OAAA,CAACnB,YAAY;sBACXsB,QAAQ,EAAEA,QAAS;sBACnBE,cAAc,EAAEA,cAAe;sBAC/BmJ,eAAe,EAAE3D,mBAAoB;sBACrCwD,eAAe,EAAEvD,mBAAoB;sBACrC2D,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;sBAC1BhJ,UAAU,EAAC;oBAAM;sBAAAwH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAClI,EAAA,CA7yBQD,GAAG;EAAA,QASiFd,gBAAgB,EAYvGC,gBAAgB;AAAA;AAAAsK,EAAA,GArBbzJ,GAAG;AA+yBZ,eAAeA,GAAG;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}