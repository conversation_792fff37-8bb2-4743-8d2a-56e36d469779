{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\CardsSection.tsx\";\nimport React from 'react';\nimport { CreditCard, ToggleLeft } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CardsSection = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-white font-semibold text-lg mb-4\",\n      children: \"Kortel\\u0117s\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-6 mb-6 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white/80 text-sm\",\n            children: \"cloudcash\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CreditCard, {\n            className: \"h-6 w-6 text-white/80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white text-xl font-semibold mb-2\",\n          children: \"**** **** 2847\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/60 text-sm\",\n          children: \"Valid thru 12/25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-3 gap-4 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white font-semibold text-lg\",\n          children: \"$ 2850.75\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/60 text-sm\",\n          children: \"Dabartinis balansas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-green-400 font-semibold text-lg\",\n          children: \"$ 1950.50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/60 text-sm\",\n          children: \"Pajamos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-400 font-semibold text-lg\",\n          children: \"$ 350.60\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/60 text-sm\",\n          children: \"I\\u0161laidos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white/80 text-sm\",\n          children: \"Savaitinis mok\\u0117jimo limitas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white font-medium\",\n          children: \"$350.60 / $4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-white/20 rounded-full h-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-500 h-2 rounded-full\",\n          style: {\n            width: '8.8%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-white/80 text-sm\",\n        children: \"Deaktyvuoti kortel\\u0119\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToggleLeft, {\n        className: \"h-5 w-5 text-white/60\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = CardsSection;\nexport default CardsSection;\nvar _c;\n$RefreshReg$(_c, \"CardsSection\");", "map": {"version": 3, "names": ["React", "CreditCard", "ToggleLeft", "jsxDEV", "_jsxDEV", "CardsSection", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/CardsSection.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { CreditCard, ToggleLeft } from 'lucide-react';\r\n\r\nconst CardsSection: React.FC = () => {\r\n  return (\r\n    <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n      <h2 className=\"text-white font-semibold text-lg mb-4\">Ko<PERSON><PERSON>ės</h2>\r\n      \r\n      {/* Main Card */}\r\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-6 mb-6 relative overflow-hidden\">\r\n        <div className=\"absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16\"></div>\r\n        <div className=\"relative z-10\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <span className=\"text-white/80 text-sm\">cloudcash</span>\r\n            <CreditCard className=\"h-6 w-6 text-white/80\" />\r\n          </div>\r\n          <div className=\"text-white text-xl font-semibold mb-2\">**** **** 2847</div>\r\n          <div className=\"text-white/60 text-sm\">Valid thru 12/25</div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Financial Info */}\r\n      <div className=\"grid grid-cols-3 gap-4 mb-6\">\r\n        <div className=\"text-center\">\r\n          <div className=\"text-white font-semibold text-lg\">$ 2850.75</div>\r\n          <div className=\"text-white/60 text-sm\">Dabartinis balansas</div>\r\n        </div>\r\n        <div className=\"text-center\">\r\n          <div className=\"text-green-400 font-semibold text-lg\">$ 1950.50</div>\r\n          <div className=\"text-white/60 text-sm\">Pajamos</div>\r\n        </div>\r\n        <div className=\"text-center\">\r\n          <div className=\"text-red-400 font-semibold text-lg\">$ 350.60</div>\r\n          <div className=\"text-white/60 text-sm\">Išlaidos</div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Payment Limit */}\r\n      <div className=\"mb-4\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <span className=\"text-white/80 text-sm\">Savaitinis mokėjimo limitas</span>\r\n          <span className=\"text-white font-medium\">$350.60 / $4000</span>\r\n        </div>\r\n        <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n          <div className=\"bg-blue-500 h-2 rounded-full\" style={{ width: '8.8%' }}></div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Deactivate Card */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <span className=\"text-white/80 text-sm\">Deaktyvuoti kortelę</span>\r\n        <ToggleLeft className=\"h-5 w-5 text-white/60\" />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CardsSection; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EACnC,oBACED,OAAA;IAAKE,SAAS,EAAC,qEAAqE;IAAAC,QAAA,gBAClFH,OAAA;MAAIE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGnEP,OAAA;MAAKE,SAAS,EAAC,0FAA0F;MAAAC,QAAA,gBACvGH,OAAA;QAAKE,SAAS,EAAC;MAA0F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChHP,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAKE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDH,OAAA;YAAME,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDP,OAAA,CAACH,UAAU;YAACK,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3EP,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNP,OAAA;MAAKE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAKE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjEP,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAKE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrEP,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAKE,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClEP,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNP,OAAA;MAAKE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBH,OAAA;QAAKE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDH,OAAA;UAAME,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1EP,OAAA;UAAME,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDH,OAAA;UAAKE,SAAS,EAAC,8BAA8B;UAACM,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNP,OAAA;MAAKE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDH,OAAA;QAAME,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClEP,OAAA,CAACF,UAAU;QAACI,SAAS,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GApDIT,YAAsB;AAsD5B,eAAeA,YAAY;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}