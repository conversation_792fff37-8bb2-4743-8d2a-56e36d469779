{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Mic2, Headphones, Settings, Play, Square, Plus, List, BarChart3, Edit, Trash2, X, Check } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RecordingPage = ({\n  recordingState,\n  currentMeeting,\n  onStartRecording,\n  onStopRecording,\n  onPauseRecording,\n  onResumeRecording\n}) => {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: '<PERSON><PERSON><PERSON><PERSON><PERSON> prezenta<PERSON>jai',\n    completed: false,\n    date: '2024-01-20',\n    priority: 'high',\n    project: 'Aexn CRM analitika',\n    owner: '<PERSON>',\n    type: 'App',\n    location: 'Apps'\n  }, {\n    id: 2,\n    text: 'Susitikimas su klientu',\n    completed: true,\n    date: '2024-01-18',\n    priority: 'medium',\n    project: 'Aexn | Analitika',\n    owner: 'Petras Petraitis',\n    type: 'Workspace',\n    location: 'Workspaces'\n  }, {\n    id: 3,\n    text: 'Peržiūrėti dokumentus',\n    completed: false,\n    date: '2024-01-22',\n    priority: 'low',\n    project: 'Aexn_analitika',\n    owner: 'Ona Onaitytė',\n    type: 'Report',\n    location: 'Aexn | Analitika'\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false,\n    priority: 'medium',\n    project: 'Aexn_analitika',\n    owner: 'Jonas Jonaitis',\n    type: 'Semantic model',\n    location: 'Aexn | Analitika'\n  }]);\n  const [recordedConversations, setRecordedConversations] = useState([{\n    id: 1,\n    title: 'Susitikimas su komanda',\n    duration: '15:30',\n    date: '2024-01-15'\n  }, {\n    id: 2,\n    title: 'Klientų aptarimas',\n    duration: '22:15',\n    date: '2024-01-14'\n  }, {\n    id: 3,\n    title: 'Projekto planavimas',\n    duration: '18:45',\n    date: '2024-01-13'\n  }]);\n\n  // TODO editing states\n  const [editingTodo, setEditingTodo] = useState(null);\n  const [editingText, setEditingText] = useState('');\n  const [editingProject, setEditingProject] = useState('');\n  const [editingOwner, setEditingOwner] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState('medium');\n  const [newTodoProject, setNewTodoProject] = useState('');\n  const [newTodoOwner, setNewTodoOwner] = useState('');\n  const [sortType, setSortType] = useState('date_desc');\n  const handleStartRecording = async () => {\n    setIsRecording(true);\n    setRecordingTime(0);\n    // Simulate recording time\n    const interval = setInterval(() => {\n      setRecordingTime(prev => prev + 1);\n    }, 1000);\n\n    // Store interval for cleanup\n    window.recordingInterval = interval;\n  };\n  const handleStopRecording = () => {\n    setIsRecording(false);\n    if (window.recordingInterval) {\n      clearInterval(window.recordingInterval);\n    }\n    // Add new recording to list\n    const newRecording = {\n      id: Date.now(),\n      title: `Pokalbis ${new Date().toLocaleString('lt-LT')}`,\n      duration: `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`,\n      date: new Date().toISOString().split('T')[0]\n    };\n    setRecordedConversations(prev => [newRecording, ...prev]);\n    setRecordingTime(0);\n  };\n  const toggleTodo = id => {\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const deleteTodo = id => {\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n  const startEditingTodo = todo => {\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n    setEditingProject(todo.project || '');\n    setEditingOwner(todo.owner || '');\n  };\n  const saveEditedTodo = () => {\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => todo.id === editingTodo ? {\n        ...todo,\n        text: editingText.trim(),\n        project: editingProject,\n        owner: editingOwner\n      } : todo));\n      setEditingTodo(null);\n      setEditingText('');\n      setEditingProject('');\n      setEditingOwner('');\n    }\n  };\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n    setEditingProject('');\n    setEditingOwner('');\n  };\n  const addNewTodo = () => {\n    if (newTodoText.trim()) {\n      const newTodo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority,\n        project: newTodoProject,\n        owner: newTodoOwner\n      };\n      setTodos(prev => [...prev, newTodo]);\n      setNewTodoText('');\n      setNewTodoDate('');\n      setNewTodoPriority('medium');\n      setNewTodoProject('');\n      setNewTodoOwner('');\n      setShowNewTodoForm(false);\n    }\n  };\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n    setNewTodoProject('');\n    setNewTodoOwner('');\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-400';\n      case 'medium':\n        return 'text-yellow-400';\n      case 'low':\n        return 'text-green-400';\n      default:\n        return 'text-white/60';\n    }\n  };\n  const getPriorityBg = priority => {\n    switch (priority) {\n      case 'high':\n        return 'bg-red-500/20 border-red-500/30';\n      case 'medium':\n        return 'bg-yellow-500/20 border-yellow-500/30';\n      case 'low':\n        return 'bg-green-500/20 border-green-500/30';\n      default:\n        return 'bg-white/5 border-white/10';\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Rūšiavimo funkcija\n  const sortedTodos = [...todos].sort((a, b) => {\n    if (sortType === 'date_desc') {\n      if (!a.date) return 1;\n      if (!b.date) return -1;\n      return b.date.localeCompare(a.date);\n    }\n    if (sortType === 'date_asc') {\n      if (!a.date) return 1;\n      if (!b.date) return -1;\n      return a.date.localeCompare(b.date);\n    }\n    if (sortType === 'alpha_asc') {\n      return a.text.localeCompare(b.text, 'lt');\n    }\n    if (sortType === 'alpha_desc') {\n      return b.text.localeCompare(a.text, 'lt');\n    }\n    return 0;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",\n          children: /*#__PURE__*/_jsxDEV(Mic2, {\n            className: \"h-6 w-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-white\",\n            children: \"Pokalbi\\u0173 \\u012Fra\\u0161ymas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-white/60\",\n            children: \"Profesionalus garso \\u012Fra\\u0161ymas su automatine transkribavimo technologija\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Mic2, {\n            className: \"h-5 w-5 text-blue-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"Pokalbi\\u0173 \\u012Fra\\u0161ymas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-medium\",\n                children: \"\\u012Era\\u0161ymo statusas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-white mb-2\",\n              children: formatTime(recordingTime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/60 text-sm\",\n              children: isRecording ? 'Įrašoma...' : 'Pasiruošta įrašymui'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: !isRecording ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStartRecording,\n              className: \"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStopRecording,\n              className: \"flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-red-600 hover:to-pink-600 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(Square, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), currentMeeting && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-medium mb-2\",\n              children: \"Dabartinis pokalbis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/80 text-sm\",\n              children: currentMeeting.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/60 text-xs mt-1\",\n              children: [\"Prad\\u0117tas: \", currentMeeting.date.toLocaleTimeString('lt-LT')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(List, {\n              className: \"h-5 w-5 text-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-white\",\n              children: \"Susitikimo u\\u017Eduotys\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNewTodoForm(true),\n            className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\",\n            children: /*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 flex gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"sort-todos\",\n            className: \"text-white/60 text-sm\",\n            children: \"R\\u016B\\u0161iuoti:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"sort-todos\",\n            value: sortType,\n            onChange: e => setSortType(e.target.value),\n            className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white focus:outline-none focus:border-blue-400\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"date_desc\",\n              children: \"Pagal dat\\u0105 (naujausios vir\\u0161uje)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"date_asc\",\n              children: \"Pagal dat\\u0105 (seniausios vir\\u0161uje)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"alpha_asc\",\n              children: \"Pagal ab\\u0117c\\u0117l\\u0119 (A\\u2013Z)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"alpha_desc\",\n              children: \"Pagal ab\\u0117c\\u0117l\\u0119 (Z\\u2013A)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), showNewTodoForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-4 bg-white/5 rounded-xl border border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoText,\n              onChange: e => setNewTodoText(e.target.value),\n              placeholder: \"\\u012Eveskite u\\u017Eduot\\u012F...\",\n              className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\",\n              onKeyPress: e => e.key === 'Enter' && addNewTodo()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: newTodoDate,\n                onChange: e => setNewTodoDate(e.target.value),\n                className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newTodoPriority,\n                onChange: e => setNewTodoPriority(e.target.value),\n                className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"low\",\n                  children: \"\\u017Demas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"medium\",\n                  children: \"Vidutinis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high\",\n                  children: \"Auk\\u0161tas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newTodoProject,\n                onChange: e => setNewTodoProject(e.target.value),\n                placeholder: \"Projekto pavadinimas\",\n                className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newTodoOwner,\n                onChange: e => setNewTodoOwner(e.target.value),\n                placeholder: \"Atsakingas asmuo\",\n                className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: addNewTodo,\n                className: \"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(Check, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this), \"Prid\\u0117ti\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: cancelNewTodo,\n                className: \"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(X, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this), \"At\\u0161aukti\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full text-sm text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Pavadinimas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Projekto pavadinimas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Atsakingas asmuo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Prioritetas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"B\\u016Bsena\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Veiksmai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: sortedTodos.map(todo => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-white/10 hover:bg-white/5\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: editingText,\n                    onChange: e => setEditingText(e.target.value),\n                    className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 25\n                  }, this) : todo.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: editingProject,\n                    onChange: e => setEditingProject(e.target.value),\n                    className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 25\n                  }, this) : todo.project\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: editingOwner,\n                    onChange: e => setEditingOwner(e.target.value),\n                    className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 25\n                  }, this) : todo.owner\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: todo.date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`,\n                    children: todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: todo.completed,\n                    onChange: () => toggleTodo(todo.id),\n                    className: \"accent-green-500\",\n                    disabled: editingTodo === todo.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2 flex gap-1\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: saveEditedTodo,\n                      className: \"p-1 text-green-400 hover:text-green-600\",\n                      children: /*#__PURE__*/_jsxDEV(Check, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 112\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: cancelEditing,\n                      className: \"p-1 text-red-400 hover:text-red-600\",\n                      children: /*#__PURE__*/_jsxDEV(X, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 442,\n                        columnNumber: 107\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => startEditingTodo(todo),\n                      className: \"p-1 text-white/60 hover:text-white\",\n                      children: /*#__PURE__*/_jsxDEV(Edit, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 446,\n                        columnNumber: 121\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => deleteTodo(todo.id),\n                      className: \"p-1 text-white/60 hover:text-red-400\",\n                      children: /*#__PURE__*/_jsxDEV(Trash2, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 447,\n                        columnNumber: 120\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, todo.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: [\"U\\u017Ebaigta: \", todos.filter(t => t.completed).length, \" i\\u0161 \", todos.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Headphones, {\n            className: \"h-5 w-5 text-purple-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"\\u012Era\\u0161yti pokalbiai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recordedConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-medium text-sm truncate\",\n                children: conversation.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-xs\",\n                children: conversation.duration\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-xs\",\n                children: conversation.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)]\n          }, conversation.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: [\"I\\u0161 viso: \", recordedConversations.length, \" pokalbi\\u0173\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"h-5 w-5 text-orange-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"Analitika\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 rounded-xl p-4 border border-white/10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-white\",\n                children: recordedConversations.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"\\u012Era\\u0161yti pokalbiai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 rounded-xl p-4 border border-white/10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-white\",\n                children: recordedConversations.reduce((total, conv) => {\n                  const [mins, secs] = conv.duration.split(':').map(Number);\n                  return total + mins * 60 + secs;\n                }, 0) / 60 | 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"Minut\\u0117s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"U\\u017Eduo\\u010Di\\u0173 progresas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: [Math.round(todos.filter(t => t.completed).length / todos.length * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${todos.filter(t => t.completed).length / todos.length * 100}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"\\u012Era\\u0161ymo kokyb\\u0117\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: \"95%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-500 h-2 rounded-full\",\n                  style: {\n                    width: '95%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"Transkribavimo tikslumas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: \"98%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-500 h-2 rounded-full\",\n                  style: {\n                    width: '98%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s(RecordingPage, \"psNDGMcVX1/l0EdiHl5cLU76kZc=\");\n_c = RecordingPage;\nexport default RecordingPage;\nvar _c;\n$RefreshReg$(_c, \"RecordingPage\");", "map": {"version": 3, "names": ["React", "useState", "Mic2", "Headphones", "Settings", "Play", "Square", "Plus", "List", "BarChart3", "Edit", "Trash2", "X", "Check", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RecordingPage", "recordingState", "currentMeeting", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "_s", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "date", "priority", "project", "owner", "type", "location", "recordedConversations", "setRecordedConversations", "title", "duration", "editingTodo", "setEditingTodo", "editingText", "setEditingText", "editingProject", "setEditingProject", "<PERSON><PERSON><PERSON><PERSON>", "setEditingOwner", "newTodoText", "setNewTodoText", "showNewTodoForm", "setShowNewTodoForm", "newTodoDate", "setNewTodoDate", "newTodoPriority", "setNewTodoPriority", "newTodoProject", "setNewTodoProject", "newTodoOwner", "setNewTodoOwner", "sortType", "setSortType", "handleStartRecording", "interval", "setInterval", "prev", "window", "recordingInterval", "handleStopRecording", "clearInterval", "newRecording", "Date", "now", "toLocaleString", "Math", "floor", "toString", "padStart", "toISOString", "split", "toggleTodo", "map", "todo", "deleteTodo", "filter", "startEditingTodo", "saveEditedTodo", "trim", "cancelEditing", "addNewTodo", "newTodo", "undefined", "cancelNewTodo", "getPriorityColor", "getPriorityBg", "formatTime", "seconds", "mins", "secs", "sortedTodos", "sort", "a", "b", "localeCompare", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "toLocaleTimeString", "htmlFor", "value", "onChange", "e", "target", "placeholder", "onKeyPress", "key", "checked", "disabled", "t", "length", "conversation", "reduce", "total", "conv", "Number", "round", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Mic2, Zap, Headphones, Settings, Play, Pause, Square, Plus, List, BarChart3, Edit, Trash2, Calendar, X, Check } from 'lucide-react';\r\nimport { RecordingPanel } from './index';\r\nimport { RecordingState, Meeting } from '../types/meeting';\r\n\r\ninterface Todo {\r\n  id: number;\r\n  text: string;\r\n  completed: boolean;\r\n  date?: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n  project?: string;\r\n  owner?: string;\r\n  type?: string;\r\n  location?: string;\r\n}\r\n\r\ninterface RecordingPageProps {\r\n  recordingState: RecordingState;\r\n  currentMeeting: Meeting | null;\r\n  onStartRecording: (title: string) => Promise<void>;\r\n  onStopRecording: () => Promise<void>;\r\n  onPauseRecording: () => void;\r\n  onResumeRecording: () => void;\r\n}\r\n\r\nconst RecordingPage: React.FC<RecordingPageProps> = ({\r\n  recordingState,\r\n  currentMeeting,\r\n  onStartRecording,\r\n  onStopRecording,\r\n  onPauseRecording,\r\n  onResumeRecording\r\n}) => {\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [todos, setTodos] = useState<Todo[]>([\r\n    { id: 1, text: 'Pasiruošti prezentacijai', completed: false, date: '2024-01-20', priority: 'high', project: 'Aexn CRM analitika', owner: 'Jonas Jonaitis', type: 'App', location: 'Apps' },\r\n    { id: 2, text: 'Susitikimas su klientu', completed: true, date: '2024-01-18', priority: 'medium', project: 'Aexn | Analitika', owner: 'Petras Petraitis', type: 'Workspace', location: 'Workspaces' },\r\n    { id: 3, text: 'Peržiūrėti dokumentus', completed: false, date: '2024-01-22', priority: 'low', project: 'Aexn_analitika', owner: 'Ona Onaitytė', type: 'Report', location: 'Aexn | Analitika' },\r\n    { id: 4, text: 'Planuoti kitą savaitę', completed: false, priority: 'medium', project: 'Aexn_analitika', owner: 'Jonas Jonaitis', type: 'Semantic model', location: 'Aexn | Analitika' }\r\n  ]);\r\n  const [recordedConversations, setRecordedConversations] = useState([\r\n    { id: 1, title: 'Susitikimas su komanda', duration: '15:30', date: '2024-01-15' },\r\n    { id: 2, title: 'Klientų aptarimas', duration: '22:15', date: '2024-01-14' },\r\n    { id: 3, title: 'Projekto planavimas', duration: '18:45', date: '2024-01-13' }\r\n  ]);\r\n\r\n  // TODO editing states\r\n  const [editingTodo, setEditingTodo] = useState<number | null>(null);\r\n  const [editingText, setEditingText] = useState('');\r\n  const [editingProject, setEditingProject] = useState('');\r\n  const [editingOwner, setEditingOwner] = useState('');\r\n  const [newTodoText, setNewTodoText] = useState('');\r\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\r\n  const [newTodoDate, setNewTodoDate] = useState('');\r\n  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');\r\n  const [newTodoProject, setNewTodoProject] = useState('');\r\n  const [newTodoOwner, setNewTodoOwner] = useState('');\r\n  const [sortType, setSortType] = useState<'date_desc' | 'date_asc' | 'alpha_asc' | 'alpha_desc'>('date_desc');\r\n\r\n  const handleStartRecording = async () => {\r\n    setIsRecording(true);\r\n    setRecordingTime(0);\r\n    // Simulate recording time\r\n    const interval = setInterval(() => {\r\n      setRecordingTime(prev => prev + 1);\r\n    }, 1000);\r\n    \r\n    // Store interval for cleanup\r\n    (window as any).recordingInterval = interval;\r\n  };\r\n\r\n  const handleStopRecording = () => {\r\n    setIsRecording(false);\r\n    if ((window as any).recordingInterval) {\r\n      clearInterval((window as any).recordingInterval);\r\n    }\r\n    // Add new recording to list\r\n    const newRecording = {\r\n      id: Date.now(),\r\n      title: `Pokalbis ${new Date().toLocaleString('lt-LT')}`,\r\n      duration: `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`,\r\n      date: new Date().toISOString().split('T')[0]\r\n    };\r\n    setRecordedConversations(prev => [newRecording, ...prev]);\r\n    setRecordingTime(0);\r\n  };\r\n\r\n  const toggleTodo = (id: number) => {\r\n    setTodos(prev => prev.map(todo => \r\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\r\n    ));\r\n  };\r\n\r\n  const deleteTodo = (id: number) => {\r\n    setTodos(prev => prev.filter(todo => todo.id !== id));\r\n  };\r\n\r\n  const startEditingTodo = (todo: Todo) => {\r\n    setEditingTodo(todo.id);\r\n    setEditingText(todo.text);\r\n    setEditingProject(todo.project || '');\r\n    setEditingOwner(todo.owner || '');\r\n  };\r\n\r\n  const saveEditedTodo = () => {\r\n    if (editingTodo && editingText.trim()) {\r\n      setTodos(prev => prev.map(todo => \r\n        todo.id === editingTodo ? { ...todo, text: editingText.trim(), project: editingProject, owner: editingOwner } : todo\r\n      ));\r\n      setEditingTodo(null);\r\n      setEditingText('');\r\n      setEditingProject('');\r\n      setEditingOwner('');\r\n    }\r\n  };\r\n\r\n  const cancelEditing = () => {\r\n    setEditingTodo(null);\r\n    setEditingText('');\r\n    setEditingProject('');\r\n    setEditingOwner('');\r\n  };\r\n\r\n  const addNewTodo = () => {\r\n    if (newTodoText.trim()) {\r\n      const newTodo: Todo = {\r\n        id: Date.now(),\r\n        text: newTodoText.trim(),\r\n        completed: false,\r\n        date: newTodoDate || undefined,\r\n        priority: newTodoPriority,\r\n        project: newTodoProject,\r\n        owner: newTodoOwner\r\n      };\r\n      setTodos(prev => [...prev, newTodo]);\r\n      setNewTodoText('');\r\n      setNewTodoDate('');\r\n      setNewTodoPriority('medium');\r\n      setNewTodoProject('');\r\n      setNewTodoOwner('');\r\n      setShowNewTodoForm(false);\r\n    }\r\n  };\r\n\r\n  const cancelNewTodo = () => {\r\n    setShowNewTodoForm(false);\r\n    setNewTodoText('');\r\n    setNewTodoDate('');\r\n    setNewTodoPriority('medium');\r\n    setNewTodoProject('');\r\n    setNewTodoOwner('');\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'text-red-400';\r\n      case 'medium': return 'text-yellow-400';\r\n      case 'low': return 'text-green-400';\r\n      default: return 'text-white/60';\r\n    }\r\n  };\r\n\r\n  const getPriorityBg = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'bg-red-500/20 border-red-500/30';\r\n      case 'medium': return 'bg-yellow-500/20 border-yellow-500/30';\r\n      case 'low': return 'bg-green-500/20 border-green-500/30';\r\n      default: return 'bg-white/5 border-white/10';\r\n    }\r\n  };\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  // Rūšiavimo funkcija\r\n  const sortedTodos = [...todos].sort((a, b) => {\r\n    if (sortType === 'date_desc') {\r\n      if (!a.date) return 1;\r\n      if (!b.date) return -1;\r\n      return b.date.localeCompare(a.date);\r\n    }\r\n    if (sortType === 'date_asc') {\r\n      if (!a.date) return 1;\r\n      if (!b.date) return -1;\r\n      return a.date.localeCompare(b.date);\r\n    }\r\n    if (sortType === 'alpha_asc') {\r\n      return a.text.localeCompare(b.text, 'lt');\r\n    }\r\n    if (sortType === 'alpha_desc') {\r\n      return b.text.localeCompare(a.text, 'lt');\r\n    }\r\n    return 0;\r\n  });\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\r\n            <Mic2 className=\"h-6 w-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-white\">Pokalbių įrašymas</h2>\r\n            <p className=\"text-sm text-white/60\">Profesionalus garso įrašymas su automatine transkribavimo technologija</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 2x2 Grid Layout */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        \r\n        {/* Top-Left: Pokalbių įrašymo komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <Mic2 className=\"h-5 w-5 text-blue-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Pokalbių įrašymas</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-4\">\r\n            {/* Recording Status */}\r\n            <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <span className=\"text-white font-medium\">Įrašymo statusas</span>\r\n                <div className={`w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>\r\n              </div>\r\n              <div className=\"text-2xl font-bold text-white mb-2\">\r\n                {formatTime(recordingTime)}\r\n              </div>\r\n              <div className=\"text-white/60 text-sm\">\r\n                {isRecording ? 'Įrašoma...' : 'Pasiruošta įrašymui'}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Recording Controls */}\r\n            <div className=\"flex gap-3\">\r\n              {!isRecording ? (\r\n                <button\r\n                  onClick={handleStartRecording}\r\n                  className=\"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200\"\r\n                >\r\n                  <Play className=\"h-4 w-4\" />\r\n                  Pradėti įrašymą\r\n                </button>\r\n              ) : (\r\n                <button\r\n                  onClick={handleStopRecording}\r\n                  className=\"flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-red-600 hover:to-pink-600 transition-all duration-200\"\r\n                >\r\n                  <Square className=\"h-4 w-4\" />\r\n                  Sustabdyti įrašymą\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Current Meeting Info */}\r\n            {currentMeeting && (\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n                <div className=\"text-white font-medium mb-2\">Dabartinis pokalbis</div>\r\n                <div className=\"text-white/80 text-sm\">{currentMeeting.title}</div>\r\n                <div className=\"text-white/60 text-xs mt-1\">\r\n                  Pradėtas: {currentMeeting.date.toLocaleTimeString('lt-LT')}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Top-Right: Susitikimo užduotys - TODOS list komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <List className=\"h-5 w-5 text-green-400\" />\r\n              <h3 className=\"text-lg font-semibold text-white\">Susitikimo užduotys</h3>\r\n            </div>\r\n            <button\r\n              onClick={() => setShowNewTodoForm(true)}\r\n              className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\"\r\n            >\r\n              <Plus className=\"h-4 w-4 text-white\" />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Sort select */}\r\n          <div className=\"mb-4 flex gap-2 items-center\">\r\n            <label htmlFor=\"sort-todos\" className=\"text-white/60 text-sm\">Rūšiuoti:</label>\r\n            <select\r\n              id=\"sort-todos\"\r\n              value={sortType}\r\n              onChange={e => setSortType(e.target.value as any)}\r\n              className=\"bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n            >\r\n              <option value=\"date_desc\">Pagal datą (naujausios viršuje)</option>\r\n              <option value=\"date_asc\">Pagal datą (seniausios viršuje)</option>\r\n              <option value=\"alpha_asc\">Pagal abėcėlę (A–Z)</option>\r\n              <option value=\"alpha_desc\">Pagal abėcėlę (Z–A)</option>\r\n            </select>\r\n          </div>\r\n\r\n          {/* New Todo Form */}\r\n          {showNewTodoForm && (\r\n            <div className=\"mb-4 p-4 bg-white/5 rounded-xl border border-white/10\">\r\n              <div className=\"space-y-3\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoText}\r\n                  onChange={(e) => setNewTodoText(e.target.value)}\r\n                  placeholder=\"Įveskite užduotį...\"\r\n                  className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                  onKeyPress={(e) => e.key === 'Enter' && addNewTodo()}\r\n                />\r\n                <div className=\"flex gap-2\">\r\n                  <input\r\n                    type=\"date\"\r\n                    value={newTodoDate}\r\n                    onChange={(e) => setNewTodoDate(e.target.value)}\r\n                    className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                  />\r\n                  <select\r\n                    value={newTodoPriority}\r\n                    onChange={(e) => setNewTodoPriority(e.target.value as 'low' | 'medium' | 'high')}\r\n                    className=\"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                  >\r\n                    <option value=\"low\">Žemas</option>\r\n                    <option value=\"medium\">Vidutinis</option>\r\n                    <option value=\"high\">Aukštas</option>\r\n                  </select>\r\n                </div>\r\n                <div className=\"flex gap-2\">\r\n                  <input\r\n                    type=\"text\"\r\n                    value={newTodoProject}\r\n                    onChange={e => setNewTodoProject(e.target.value)}\r\n                    placeholder=\"Projekto pavadinimas\"\r\n                    className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                  />\r\n                  <input\r\n                    type=\"text\"\r\n                    value={newTodoOwner}\r\n                    onChange={e => setNewTodoOwner(e.target.value)}\r\n                    placeholder=\"Atsakingas asmuo\"\r\n                    className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                  />\r\n                </div>\r\n                <div className=\"flex gap-2\">\r\n                  <button\r\n                    onClick={addNewTodo}\r\n                    className=\"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                  >\r\n                    <Check className=\"h-4 w-4\" />\r\n                    Pridėti\r\n                  </button>\r\n                  <button\r\n                    onClick={cancelNewTodo}\r\n                    className=\"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                  >\r\n                    <X className=\"h-4 w-4\" />\r\n                    Atšaukti\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          \r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"min-w-full text-sm text-white\">\r\n              <thead>\r\n                <tr className=\"bg-white/10\">\r\n                  <th className=\"px-3 py-2 text-left\">Pavadinimas</th>\r\n                  <th className=\"px-3 py-2 text-left\">Projekto pavadinimas</th>\r\n                  <th className=\"px-3 py-2 text-left\">Atsakingas asmuo</th>\r\n                  <th className=\"px-3 py-2 text-left\">Data</th>\r\n                  <th className=\"px-3 py-2 text-left\">Prioritetas</th>\r\n                  <th className=\"px-3 py-2 text-left\">Būsena</th>\r\n                  <th className=\"px-3 py-2 text-left\">Veiksmai</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {sortedTodos.map((todo) => (\r\n                  <tr key={todo.id} className=\"border-b border-white/10 hover:bg-white/5\">\r\n                    <td className=\"px-3 py-2\">\r\n                      {editingTodo === todo.id ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editingText}\r\n                          onChange={e => setEditingText(e.target.value)}\r\n                          className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                        />\r\n                      ) : (\r\n                        todo.text\r\n                      )}\r\n                    </td>\r\n                    <td className=\"px-3 py-2\">\r\n                      {editingTodo === todo.id ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editingProject}\r\n                          onChange={e => setEditingProject(e.target.value)}\r\n                          className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                        />\r\n                      ) : (\r\n                        todo.project\r\n                      )}\r\n                    </td>\r\n                    <td className=\"px-3 py-2\">\r\n                      {editingTodo === todo.id ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editingOwner}\r\n                          onChange={e => setEditingOwner(e.target.value)}\r\n                          className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                        />\r\n                      ) : (\r\n                        todo.owner\r\n                      )}\r\n                    </td>\r\n                    <td className=\"px-3 py-2\">{todo.date}</td>\r\n                    <td className=\"px-3 py-2\">\r\n                      <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`}>\r\n                        {todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'}\r\n                      </span>\r\n                    </td>\r\n                    <td className=\"px-3 py-2\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={todo.completed}\r\n                        onChange={() => toggleTodo(todo.id)}\r\n                        className=\"accent-green-500\"\r\n                        disabled={editingTodo === todo.id}\r\n                      />\r\n                    </td>\r\n                    <td className=\"px-3 py-2 flex gap-1\">\r\n                      {editingTodo === todo.id ? (\r\n                        <>\r\n                          <button onClick={saveEditedTodo} className=\"p-1 text-green-400 hover:text-green-600\"><Check className=\"h-4 w-4\" /></button>\r\n                          <button onClick={cancelEditing} className=\"p-1 text-red-400 hover:text-red-600\"><X className=\"h-4 w-4\" /></button>\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <button onClick={() => startEditingTodo(todo)} className=\"p-1 text-white/60 hover:text-white\"><Edit className=\"h-4 w-4\" /></button>\r\n                          <button onClick={() => deleteTodo(todo.id)} className=\"p-1 text-white/60 hover:text-red-400\"><Trash2 className=\"h-4 w-4\" /></button>\r\n                        </>\r\n                      )}\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n          \r\n          <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n            <div className=\"text-white/60 text-sm\">\r\n              Užbaigta: {todos.filter(t => t.completed).length} iš {todos.length}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom-Left: Įrašyti pokalbiai komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <Headphones className=\"h-5 w-5 text-purple-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Įrašyti pokalbiai</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-3\">\r\n            {recordedConversations.map((conversation) => (\r\n              <div\r\n                key={conversation.id}\r\n                className=\"p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\"\r\n              >\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <div className=\"text-white font-medium text-sm truncate\">\r\n                    {conversation.title}\r\n                  </div>\r\n                  <div className=\"text-white/60 text-xs\">\r\n                    {conversation.duration}\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"text-white/60 text-xs\">\r\n                    {conversation.date}\r\n                  </div>\r\n                  <div className=\"flex gap-2\">\r\n                    <button className=\"p-1 text-white/60 hover:text-white transition-colors\">\r\n                      <Play className=\"h-3 w-3\" />\r\n                    </button>\r\n                    <button className=\"p-1 text-white/60 hover:text-white transition-colors\">\r\n                      <Settings className=\"h-3 w-3\" />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          \r\n          <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n            <div className=\"text-white/60 text-sm\">\r\n              Iš viso: {recordedConversations.length} pokalbių\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom-Right: Analitikos komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <BarChart3 className=\"h-5 w-5 text-orange-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Analitika</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-4\">\r\n            {/* Recording Stats */}\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10 text-center\">\r\n                <div className=\"text-2xl font-bold text-white\">{recordedConversations.length}</div>\r\n                <div className=\"text-white/60 text-sm\">Įrašyti pokalbiai</div>\r\n              </div>\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10 text-center\">\r\n                <div className=\"text-2xl font-bold text-white\">\r\n                  {recordedConversations.reduce((total, conv) => {\r\n                    const [mins, secs] = conv.duration.split(':').map(Number);\r\n                    return total + mins * 60 + secs;\r\n                  }, 0) / 60 | 0}\r\n                </div>\r\n                <div className=\"text-white/60 text-sm\">Minutės</div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Progress Bars */}\r\n            <div className=\"space-y-3\">\r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Užduočių progresas</span>\r\n                  <span className=\"text-white/60 text-sm\">\r\n                    {Math.round((todos.filter(t => t.completed).length / todos.length) * 100)}%\r\n                  </span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div \r\n                    className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\r\n                    style={{ width: `${(todos.filter(t => t.completed).length / todos.length) * 100}%` }}\r\n                  ></div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Įrašymo kokybė</span>\r\n                  <span className=\"text-white/60 text-sm\">95%</span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{ width: '95%' }}></div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Transkribavimo tikslumas</span>\r\n                  <span className=\"text-white/60 text-sm\">98%</span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div className=\"bg-purple-500 h-2 rounded-full\" style={{ width: '98%' }}></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RecordingPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAOC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAYC,CAAC,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAyB7I,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,cAAc;EACdC,cAAc;EACdC,gBAAgB;EAChBC,eAAe;EACfC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAS,CACzC;IAAE+B,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,MAAM;IAAEC,OAAO,EAAE,oBAAoB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,KAAK;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAC1L;IAAER,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE,WAAW;IAAEC,QAAQ,EAAE;EAAa,CAAC,EACrM;IAAER,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,KAAK;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAmB,CAAC,EAC/L;IAAER,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEE,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,QAAQ,EAAE;EAAmB,CAAC,CACzL,CAAC;EACF,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzC,QAAQ,CAAC,CACjE;IAAE+B,EAAE,EAAE,CAAC;IAAEW,KAAK,EAAE,wBAAwB;IAAEC,QAAQ,EAAE,OAAO;IAAET,IAAI,EAAE;EAAa,CAAC,EACjF;IAAEH,EAAE,EAAE,CAAC;IAAEW,KAAK,EAAE,mBAAmB;IAAEC,QAAQ,EAAE,OAAO;IAAET,IAAI,EAAE;EAAa,CAAC,EAC5E;IAAEH,EAAE,EAAE,CAAC;IAAEW,KAAK,EAAE,qBAAqB;IAAEC,QAAQ,EAAE,OAAO;IAAET,IAAI,EAAE;EAAa,CAAC,CAC/E,CAAC;;EAEF;EACA,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAA4B,QAAQ,CAAC;EAC3F,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAwD,WAAW,CAAC;EAE5G,MAAMkE,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvCxC,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,CAAC,CAAC;IACnB;IACA,MAAMuC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCxC,gBAAgB,CAACyC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC;;IAER;IACCC,MAAM,CAASC,iBAAiB,GAAGJ,QAAQ;EAC9C,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChC9C,cAAc,CAAC,KAAK,CAAC;IACrB,IAAK4C,MAAM,CAASC,iBAAiB,EAAE;MACrCE,aAAa,CAAEH,MAAM,CAASC,iBAAiB,CAAC;IAClD;IACA;IACA,MAAMG,YAAY,GAAG;MACnB3C,EAAE,EAAE4C,IAAI,CAACC,GAAG,CAAC,CAAC;MACdlC,KAAK,EAAE,YAAY,IAAIiC,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC,EAAE;MACvDlC,QAAQ,EAAE,GAAGmC,IAAI,CAACC,KAAK,CAACpD,aAAa,GAAG,EAAE,CAAC,IAAI,CAACA,aAAa,GAAG,EAAE,EAAEqD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACjG/C,IAAI,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD1C,wBAAwB,CAAC4B,IAAI,IAAI,CAACK,YAAY,EAAE,GAAGL,IAAI,CAAC,CAAC;IACzDzC,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAMwD,UAAU,GAAIrD,EAAU,IAAK;IACjCD,QAAQ,CAACuC,IAAI,IAAIA,IAAI,CAACgB,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACvD,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGuD,IAAI;MAAErD,SAAS,EAAE,CAACqD,IAAI,CAACrD;IAAU,CAAC,GAAGqD,IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIxD,EAAU,IAAK;IACjCD,QAAQ,CAACuC,IAAI,IAAIA,IAAI,CAACmB,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACvD,EAAE,KAAKA,EAAE,CAAC,CAAC;EACvD,CAAC;EAED,MAAM0D,gBAAgB,GAAIH,IAAU,IAAK;IACvCzC,cAAc,CAACyC,IAAI,CAACvD,EAAE,CAAC;IACvBgB,cAAc,CAACuC,IAAI,CAACtD,IAAI,CAAC;IACzBiB,iBAAiB,CAACqC,IAAI,CAAClD,OAAO,IAAI,EAAE,CAAC;IACrCe,eAAe,CAACmC,IAAI,CAACjD,KAAK,IAAI,EAAE,CAAC;EACnC,CAAC;EAED,MAAMqD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9C,WAAW,IAAIE,WAAW,CAAC6C,IAAI,CAAC,CAAC,EAAE;MACrC7D,QAAQ,CAACuC,IAAI,IAAIA,IAAI,CAACgB,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACvD,EAAE,KAAKa,WAAW,GAAG;QAAE,GAAG0C,IAAI;QAAEtD,IAAI,EAAEc,WAAW,CAAC6C,IAAI,CAAC,CAAC;QAAEvD,OAAO,EAAEY,cAAc;QAAEX,KAAK,EAAEa;MAAa,CAAC,GAAGoC,IAClH,CAAC,CAAC;MACFzC,cAAc,CAAC,IAAI,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC;EAED,MAAMyC,aAAa,GAAGA,CAAA,KAAM;IAC1B/C,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;IAClBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAM0C,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIzC,WAAW,CAACuC,IAAI,CAAC,CAAC,EAAE;MACtB,MAAMG,OAAa,GAAG;QACpB/D,EAAE,EAAE4C,IAAI,CAACC,GAAG,CAAC,CAAC;QACd5C,IAAI,EAAEoB,WAAW,CAACuC,IAAI,CAAC,CAAC;QACxB1D,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAEsB,WAAW,IAAIuC,SAAS;QAC9B5D,QAAQ,EAAEuB,eAAe;QACzBtB,OAAO,EAAEwB,cAAc;QACvBvB,KAAK,EAAEyB;MACT,CAAC;MACDhC,QAAQ,CAACuC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEyB,OAAO,CAAC,CAAC;MACpCzC,cAAc,CAAC,EAAE,CAAC;MAClBI,cAAc,CAAC,EAAE,CAAC;MAClBE,kBAAkB,CAAC,QAAQ,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;MACnBR,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMyC,aAAa,GAAGA,CAAA,KAAM;IAC1BzC,kBAAkB,CAAC,KAAK,CAAC;IACzBF,cAAc,CAAC,EAAE,CAAC;IAClBI,cAAc,CAAC,EAAE,CAAC;IAClBE,kBAAkB,CAAC,QAAQ,CAAC;IAC5BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAMkC,gBAAgB,GAAI9D,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAM+D,aAAa,GAAI/D,QAAgB,IAAK;IAC1C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,iCAAiC;MACrD,KAAK,QAAQ;QAAE,OAAO,uCAAuC;MAC7D,KAAK,KAAK;QAAE,OAAO,qCAAqC;MACxD;QAAS,OAAO,4BAA4B;IAC9C;EACF,CAAC;EAED,MAAMgE,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAGvB,IAAI,CAACC,KAAK,CAACqB,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACtB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;;EAED;EACA,MAAMsB,WAAW,GAAG,CAAC,GAAG1E,KAAK,CAAC,CAAC2E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC5C,IAAI1C,QAAQ,KAAK,WAAW,EAAE;MAC5B,IAAI,CAACyC,CAAC,CAACvE,IAAI,EAAE,OAAO,CAAC;MACrB,IAAI,CAACwE,CAAC,CAACxE,IAAI,EAAE,OAAO,CAAC,CAAC;MACtB,OAAOwE,CAAC,CAACxE,IAAI,CAACyE,aAAa,CAACF,CAAC,CAACvE,IAAI,CAAC;IACrC;IACA,IAAI8B,QAAQ,KAAK,UAAU,EAAE;MAC3B,IAAI,CAACyC,CAAC,CAACvE,IAAI,EAAE,OAAO,CAAC;MACrB,IAAI,CAACwE,CAAC,CAACxE,IAAI,EAAE,OAAO,CAAC,CAAC;MACtB,OAAOuE,CAAC,CAACvE,IAAI,CAACyE,aAAa,CAACD,CAAC,CAACxE,IAAI,CAAC;IACrC;IACA,IAAI8B,QAAQ,KAAK,WAAW,EAAE;MAC5B,OAAOyC,CAAC,CAACzE,IAAI,CAAC2E,aAAa,CAACD,CAAC,CAAC1E,IAAI,EAAE,IAAI,CAAC;IAC3C;IACA,IAAIgC,QAAQ,KAAK,YAAY,EAAE;MAC7B,OAAO0C,CAAC,CAAC1E,IAAI,CAAC2E,aAAa,CAACF,CAAC,CAACzE,IAAI,EAAE,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC;EACV,CAAC,CAAC;EAEF,oBACElB,OAAA;IAAK8F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/F,OAAA;MAAK8F,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClF/F,OAAA;QAAK8F,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC/F,OAAA;UAAK8F,SAAS,EAAC,6JAA6J;UAAAC,QAAA,eAC1K/F,OAAA,CAACb,IAAI;YAAC2G,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNnG,OAAA;UAAA+F,QAAA,gBACE/F,OAAA;YAAI8F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEnG,OAAA;YAAG8F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnG,OAAA;MAAK8F,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAGpD/F,OAAA;QAAK8F,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF/F,OAAA;UAAK8F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C/F,OAAA,CAACb,IAAI;YAAC2G,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CnG,OAAA;YAAI8F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENnG,OAAA;UAAK8F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB/F,OAAA;YAAK8F,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/D/F,OAAA;cAAK8F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD/F,OAAA;gBAAM8F,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChEnG,OAAA;gBAAK8F,SAAS,EAAE,wBAAwBnF,WAAW,GAAG,0BAA0B,GAAG,aAAa;cAAG;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC,eACNnG,OAAA;cAAK8F,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDV,UAAU,CAACxE,aAAa;YAAC;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNnG,OAAA;cAAK8F,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCpF,WAAW,GAAG,YAAY,GAAG;YAAqB;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnG,OAAA;YAAK8F,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxB,CAACpF,WAAW,gBACXX,OAAA;cACEoG,OAAO,EAAEhD,oBAAqB;cAC9B0C,SAAS,EAAC,4MAA4M;cAAAC,QAAA,gBAEtN/F,OAAA,CAACV,IAAI;gBAACwG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uCAE9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETnG,OAAA;cACEoG,OAAO,EAAE1C,mBAAoB;cAC7BoC,SAAS,EAAC,sMAAsM;cAAAC,QAAA,gBAEhN/F,OAAA,CAACT,MAAM;gBAACuG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qCAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL9F,cAAc,iBACbL,OAAA;YAAK8F,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/D/F,OAAA;cAAK8F,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtEnG,OAAA;cAAK8F,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE1F,cAAc,CAACuB;YAAK;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnEnG,OAAA;cAAK8F,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,iBAChC,EAAC1F,cAAc,CAACe,IAAI,CAACiF,kBAAkB,CAAC,OAAO,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnG,OAAA;QAAK8F,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF/F,OAAA;UAAK8F,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD/F,OAAA;YAAK8F,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC/F,OAAA,CAACP,IAAI;cAACqG,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CnG,OAAA;cAAI8F,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACNnG,OAAA;YACEoG,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,IAAI,CAAE;YACxCqD,SAAS,EAAC,0EAA0E;YAAAC,QAAA,eAEpF/F,OAAA,CAACR,IAAI;cAACsG,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnG,OAAA;UAAK8F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C/F,OAAA;YAAOsG,OAAO,EAAC,YAAY;YAACR,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/EnG,OAAA;YACEiB,EAAE,EAAC,YAAY;YACfsF,KAAK,EAAErD,QAAS;YAChBsD,QAAQ,EAAEC,CAAC,IAAItD,WAAW,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAY,CAAE;YAClDT,SAAS,EAAC,6GAA6G;YAAAC,QAAA,gBAEvH/F,OAAA;cAAQuG,KAAK,EAAC,WAAW;cAAAR,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClEnG,OAAA;cAAQuG,KAAK,EAAC,UAAU;cAAAR,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjEnG,OAAA;cAAQuG,KAAK,EAAC,WAAW;cAAAR,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtDnG,OAAA;cAAQuG,KAAK,EAAC,YAAY;cAAAR,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGL3D,eAAe,iBACdxC,OAAA;UAAK8F,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpE/F,OAAA;YAAK8F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/F,OAAA;cACEwB,IAAI,EAAC,MAAM;cACX+E,KAAK,EAAEjE,WAAY;cACnBkE,QAAQ,EAAGC,CAAC,IAAKlE,cAAc,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDI,WAAW,EAAC,oCAAqB;cACjCb,SAAS,EAAC,yIAAyI;cACnJc,UAAU,EAAGH,CAAC,IAAKA,CAAC,CAACI,GAAG,KAAK,OAAO,IAAI9B,UAAU,CAAC;YAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACFnG,OAAA;cAAK8F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/F,OAAA;gBACEwB,IAAI,EAAC,MAAM;gBACX+E,KAAK,EAAE7D,WAAY;gBACnB8D,QAAQ,EAAGC,CAAC,IAAK9D,cAAc,CAAC8D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDT,SAAS,EAAC;cAAoH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H,CAAC,eACFnG,OAAA;gBACEuG,KAAK,EAAE3D,eAAgB;gBACvB4D,QAAQ,EAAGC,CAAC,IAAK5D,kBAAkB,CAAC4D,CAAC,CAACC,MAAM,CAACH,KAAkC,CAAE;gBACjFT,SAAS,EAAC,6GAA6G;gBAAAC,QAAA,gBAEvH/F,OAAA;kBAAQuG,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCnG,OAAA;kBAAQuG,KAAK,EAAC,QAAQ;kBAAAR,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCnG,OAAA;kBAAQuG,KAAK,EAAC,MAAM;kBAAAR,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNnG,OAAA;cAAK8F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/F,OAAA;gBACEwB,IAAI,EAAC,MAAM;gBACX+E,KAAK,EAAEzD,cAAe;gBACtB0D,QAAQ,EAAEC,CAAC,IAAI1D,iBAAiB,CAAC0D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACjDI,WAAW,EAAC,sBAAsB;gBAClCb,SAAS,EAAC;cAAyI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpJ,CAAC,eACFnG,OAAA;gBACEwB,IAAI,EAAC,MAAM;gBACX+E,KAAK,EAAEvD,YAAa;gBACpBwD,QAAQ,EAAEC,CAAC,IAAIxD,eAAe,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CI,WAAW,EAAC,kBAAkB;gBAC9Bb,SAAS,EAAC;cAAyI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnG,OAAA;cAAK8F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/F,OAAA;gBACEoG,OAAO,EAAErB,UAAW;gBACpBe,SAAS,EAAC,iIAAiI;gBAAAC,QAAA,gBAE3I/F,OAAA,CAACF,KAAK;kBAACgG,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnG,OAAA;gBACEoG,OAAO,EAAElB,aAAc;gBACvBY,SAAS,EAAC,6HAA6H;gBAAAC,QAAA,gBAEvI/F,OAAA,CAACH,CAAC;kBAACiG,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAE3B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDnG,OAAA;UAAK8F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B/F,OAAA;YAAO8F,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC9C/F,OAAA;cAAA+F,QAAA,eACE/F,OAAA;gBAAI8F,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACzB/F,OAAA;kBAAI8F,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpDnG,OAAA;kBAAI8F,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7DnG,OAAA;kBAAI8F,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzDnG,OAAA;kBAAI8F,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7CnG,OAAA;kBAAI8F,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpDnG,OAAA;kBAAI8F,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/CnG,OAAA;kBAAI8F,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRnG,OAAA;cAAA+F,QAAA,EACGN,WAAW,CAAClB,GAAG,CAAEC,IAAI,iBACpBxE,OAAA;gBAAkB8F,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBACrE/F,OAAA;kBAAI8F,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACtBjE,WAAW,KAAK0C,IAAI,CAACvD,EAAE,gBACtBjB,OAAA;oBACEwB,IAAI,EAAC,MAAM;oBACX+E,KAAK,EAAEvE,WAAY;oBACnBwE,QAAQ,EAAEC,CAAC,IAAIxE,cAAc,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC9CT,SAAS,EAAC;kBAAoH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/H,CAAC,GAEF3B,IAAI,CAACtD;gBACN;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLnG,OAAA;kBAAI8F,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACtBjE,WAAW,KAAK0C,IAAI,CAACvD,EAAE,gBACtBjB,OAAA;oBACEwB,IAAI,EAAC,MAAM;oBACX+E,KAAK,EAAErE,cAAe;oBACtBsE,QAAQ,EAAEC,CAAC,IAAItE,iBAAiB,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDT,SAAS,EAAC;kBAAoH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/H,CAAC,GAEF3B,IAAI,CAAClD;gBACN;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLnG,OAAA;kBAAI8F,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACtBjE,WAAW,KAAK0C,IAAI,CAACvD,EAAE,gBACtBjB,OAAA;oBACEwB,IAAI,EAAC,MAAM;oBACX+E,KAAK,EAAEnE,YAAa;oBACpBoE,QAAQ,EAAEC,CAAC,IAAIpE,eAAe,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CT,SAAS,EAAC;kBAAoH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/H,CAAC,GAEF3B,IAAI,CAACjD;gBACN;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLnG,OAAA;kBAAI8F,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEvB,IAAI,CAACpD;gBAAI;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CnG,OAAA;kBAAI8F,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACvB/F,OAAA;oBAAM8F,SAAS,EAAE,kCAAkCX,gBAAgB,CAACX,IAAI,CAACnD,QAAQ,CAAC,cAAe;oBAAA0E,QAAA,EAC9FvB,IAAI,CAACnD,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAGmD,IAAI,CAACnD,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG;kBAAO;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLnG,OAAA;kBAAI8F,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACvB/F,OAAA;oBACEwB,IAAI,EAAC,UAAU;oBACfsF,OAAO,EAAEtC,IAAI,CAACrD,SAAU;oBACxBqF,QAAQ,EAAEA,CAAA,KAAMlC,UAAU,CAACE,IAAI,CAACvD,EAAE,CAAE;oBACpC6E,SAAS,EAAC,kBAAkB;oBAC5BiB,QAAQ,EAAEjF,WAAW,KAAK0C,IAAI,CAACvD;kBAAG;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLnG,OAAA;kBAAI8F,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EACjCjE,WAAW,KAAK0C,IAAI,CAACvD,EAAE,gBACtBjB,OAAA,CAAAE,SAAA;oBAAA6F,QAAA,gBACE/F,OAAA;sBAAQoG,OAAO,EAAExB,cAAe;sBAACkB,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,eAAC/F,OAAA,CAACF,KAAK;wBAACgG,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC3HnG,OAAA;sBAAQoG,OAAO,EAAEtB,aAAc;sBAACgB,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,eAAC/F,OAAA,CAACH,CAAC;wBAACiG,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eAClH,CAAC,gBAEHnG,OAAA,CAAAE,SAAA;oBAAA6F,QAAA,gBACE/F,OAAA;sBAAQoG,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACH,IAAI,CAAE;sBAACsB,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,eAAC/F,OAAA,CAACL,IAAI;wBAACmG,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACnInG,OAAA;sBAAQoG,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAACD,IAAI,CAACvD,EAAE,CAAE;sBAAC6E,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,eAAC/F,OAAA,CAACJ,MAAM;wBAACkG,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACpI;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAhEE3B,IAAI,CAACvD,EAAE;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiEZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnG,OAAA;UAAK8F,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD/F,OAAA;YAAK8F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,iBAC3B,EAAChF,KAAK,CAAC2D,MAAM,CAACsC,CAAC,IAAIA,CAAC,CAAC7F,SAAS,CAAC,CAAC8F,MAAM,EAAC,WAAI,EAAClG,KAAK,CAACkG,MAAM;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnG,OAAA;QAAK8F,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF/F,OAAA;UAAK8F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C/F,OAAA,CAACZ,UAAU;YAAC0G,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDnG,OAAA;YAAI8F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENnG,OAAA;UAAK8F,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBrE,qBAAqB,CAAC6C,GAAG,CAAE2C,YAAY,iBACtClH,OAAA;YAEE8F,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBAEzH/F,OAAA;cAAK8F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD/F,OAAA;gBAAK8F,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACrDmB,YAAY,CAACtF;cAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNnG,OAAA;gBAAK8F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCmB,YAAY,CAACrF;cAAQ;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnG,OAAA;cAAK8F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/F,OAAA;gBAAK8F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCmB,YAAY,CAAC9F;cAAI;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNnG,OAAA;gBAAK8F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/F,OAAA;kBAAQ8F,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACtE/F,OAAA,CAACV,IAAI;oBAACwG,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACTnG,OAAA;kBAAQ8F,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACtE/F,OAAA,CAACX,QAAQ;oBAACyG,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAvBDe,YAAY,CAACjG,EAAE;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBjB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnG,OAAA;UAAK8F,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD/F,OAAA;YAAK8F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,gBAC5B,EAACrE,qBAAqB,CAACuF,MAAM,EAAC,gBACzC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnG,OAAA;QAAK8F,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF/F,OAAA;UAAK8F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C/F,OAAA,CAACN,SAAS;YAACoG,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDnG,OAAA;YAAI8F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENnG,OAAA;UAAK8F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB/F,OAAA;YAAK8F,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC/F,OAAA;cAAK8F,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3E/F,OAAA;gBAAK8F,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAErE,qBAAqB,CAACuF;cAAM;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnFnG,OAAA;gBAAK8F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNnG,OAAA;cAAK8F,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3E/F,OAAA;gBAAK8F,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC3CrE,qBAAqB,CAACyF,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAK;kBAC7C,MAAM,CAAC9B,IAAI,EAAEC,IAAI,CAAC,GAAG6B,IAAI,CAACxF,QAAQ,CAACwC,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAAC+C,MAAM,CAAC;kBACzD,OAAOF,KAAK,GAAG7B,IAAI,GAAG,EAAE,GAAGC,IAAI;gBACjC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACNnG,OAAA;gBAAK8F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnG,OAAA;YAAK8F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/F,OAAA;cAAA+F,QAAA,gBACE/F,OAAA;gBAAK8F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD/F,OAAA;kBAAM8F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjEnG,OAAA;kBAAM8F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACpC/B,IAAI,CAACuD,KAAK,CAAExG,KAAK,CAAC2D,MAAM,CAACsC,CAAC,IAAIA,CAAC,CAAC7F,SAAS,CAAC,CAAC8F,MAAM,GAAGlG,KAAK,CAACkG,MAAM,GAAI,GAAG,CAAC,EAAC,GAC5E;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNnG,OAAA;gBAAK8F,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD/F,OAAA;kBACE8F,SAAS,EAAC,2DAA2D;kBACrE0B,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAI1G,KAAK,CAAC2D,MAAM,CAACsC,CAAC,IAAIA,CAAC,CAAC7F,SAAS,CAAC,CAAC8F,MAAM,GAAGlG,KAAK,CAACkG,MAAM,GAAI,GAAG;kBAAI;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnG,OAAA;cAAA+F,QAAA,gBACE/F,OAAA;gBAAK8F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD/F,OAAA;kBAAM8F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7DnG,OAAA;kBAAM8F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNnG,OAAA;gBAAK8F,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD/F,OAAA;kBAAK8F,SAAS,EAAC,8BAA8B;kBAAC0B,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnG,OAAA;cAAA+F,QAAA,gBACE/F,OAAA;gBAAK8F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD/F,OAAA;kBAAM8F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEnG,OAAA;kBAAM8F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNnG,OAAA;gBAAK8F,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD/F,OAAA;kBAAK8F,SAAS,EAAC,gCAAgC;kBAAC0B,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzF,EAAA,CAriBIP,aAA2C;AAAAuH,EAAA,GAA3CvH,aAA2C;AAuiBjD,eAAeA,aAAa;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}