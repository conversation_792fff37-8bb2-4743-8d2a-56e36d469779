{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\TranscriptionManager.tsx\";\nimport React from 'react';\nimport { Play, <PERSON>, Clock, Zap, XCircle, Trash2, Check<PERSON>ircle, Headphones } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TranscriptionManager = ({\n  meetings,\n  onStartTranscription,\n  onCancelTranscription,\n  isTranscribing,\n  currentTranscriptionId,\n  onDeleteMeeting,\n  onViewResults\n}) => {\n  const pendingMeetings = meetings.filter(m => m.transcriptionStatus.state === 'not_started' || m.transcriptionStatus.state === 'pending' || m.transcriptionStatus.state === 'processing');\n  const completedMeetings = meetings.filter(m => m.transcriptionStatus.state === 'completed');\n  const failedMeetings = meetings.filter(m => m.transcriptionStatus.state === 'failed');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col space-y-8 animate-fade-in\",\n    children: [pendingMeetings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-5 animate-fade-in-up\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(Clock, {\n          className: \"h-6 w-6 text-yellow-400 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Laukia transkribavimo (\", pendingMeetings.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: pendingMeetings.map((meeting, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"glassmorphic-subtle p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up\",\n          style: {\n            animationDelay: `${index * 100}ms`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-white mb-2\",\n                children: meeting.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-base text-white/70 mb-2\",\n                children: [meeting.date.toLocaleString('lt-LT'), \" \\u2022 \", meeting.duration, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 21\n              }, this), meeting.transcriptionStatus.state === 'processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 bg-blue-400 rounded-full animate-pulse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-base text-blue-400\",\n                    children: [\"Transkribuojama... \", meeting.transcriptionStatus.progress || 0, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 64,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [meeting.transcriptionStatus.state === 'not_started' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onStartTranscription(meeting.id),\n                disabled: isTranscribing,\n                className: \"p-3 text-white/60 hover:text-green-400 hover:bg-green-500/10 rounded-lg transition-all duration-200 transform hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed\",\n                title: \"Prad\\u0117ti transkribavim\\u0105\",\n                children: /*#__PURE__*/_jsxDEV(Play, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 23\n              }, this), meeting.transcriptionStatus.state === 'processing' && currentTranscriptionId === meeting.id && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onCancelTranscription(meeting.id),\n                className: \"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\",\n                title: \"At\\u0161aukti transkribavim\\u0105\",\n                children: /*#__PURE__*/_jsxDEV(Square, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onDeleteMeeting(meeting.id),\n                className: \"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\",\n                title: \"I\\u0161trinti pokalb\\u012F\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 17\n          }, this)\n        }, meeting.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this), completedMeetings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-5 animate-fade-in-up animation-delay-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            className: \"h-6 w-6 text-green-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"S\\u0117kmingai transkribuota (\", completedMeetings.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), onViewResults && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onViewResults,\n          className: \"inline-flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-white bg-gradient-to-r from-green-500/80 via-green-600/70 to-emerald-600/80 hover:from-green-500/90 hover:via-green-600/80 hover:to-emerald-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-white/30 ring-1 ring-white/15 hover:border-green-300/50 transform hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(Headphones, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Per\\u017Ei\\u016Br\\u0117ti rezultatus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: completedMeetings.map((meeting, index) => {\n          var _meeting$participants;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glassmorphic-subtle p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up\",\n            style: {\n              animationDelay: `${index * 100}ms`\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-white mb-2\",\n                  children: meeting.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-base text-white/70 mb-2\",\n                  children: [meeting.date.toLocaleString('lt-LT'), \" \\u2022 \", meeting.duration, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 21\n                }, this), meeting.transcript && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-base text-green-400\",\n                  children: [meeting.transcript.length, \" segment\\u0173 \\u2022 \", ((_meeting$participants = meeting.participants) === null || _meeting$participants === void 0 ? void 0 : _meeting$participants.length) || 0, \" dalyvi\\u0173\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onDeleteMeeting(meeting.id),\n                className: \"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\",\n                title: \"I\\u0161trinti pokalb\\u012F\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this)\n          }, meeting.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 9\n    }, this), failedMeetings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-5 animate-fade-in-up animation-delay-400\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-6 w-6 text-red-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Nepavyko transkribuoti (\", failedMeetings.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: failedMeetings.map((meeting, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 backdrop-blur-md border border-white/20 ring-1 ring-white/10 rounded-xl p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up\",\n          style: {\n            animationDelay: `${index * 100}ms`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-white mb-2\",\n                children: meeting.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-base text-white/70 mb-2\",\n                children: [meeting.date.toLocaleString('lt-LT'), \" \\u2022 \", meeting.duration, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this), meeting.transcriptionStatus.error && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-base text-red-400\",\n                children: [\"Klaida: \", meeting.transcriptionStatus.error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onDeleteMeeting(meeting.id),\n              className: \"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\",\n              title: \"I\\u0161trinti pokalb\\u012F\",\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 17\n          }, this)\n        }, meeting.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this), meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-20 h-20 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center animate-pulse\",\n        children: /*#__PURE__*/_jsxDEV(Zap, {\n          className: \"h-10 w-10 text-purple-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl sm:text-2xl font-semibold text-white\",\n          children: \"N\\u0117ra pokalbi\\u0173\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg text-white/70\",\n          children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_c = TranscriptionManager;\nvar _c;\n$RefreshReg$(_c, \"TranscriptionManager\");", "map": {"version": 3, "names": ["React", "Play", "Square", "Clock", "Zap", "XCircle", "Trash2", "CheckCircle", "Headphones", "jsxDEV", "_jsxDEV", "TranscriptionManager", "meetings", "onStartTranscription", "onCancelTranscription", "isTranscribing", "currentTranscriptionId", "onDeleteMeeting", "onViewResults", "pendingMeetings", "filter", "m", "transcriptionStatus", "state", "completedMeetings", "failedMeetings", "className", "children", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "meeting", "index", "style", "animationDelay", "title", "date", "toLocaleString", "duration", "progress", "onClick", "id", "disabled", "_meeting$participants", "transcript", "participants", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TranscriptionManager.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Play, Square, Clock, Users, FileText, Zap, AlertTriangle, CheckCircle2, Loader2, XCircle, Trash2, CheckCircle, Headphones } from 'lucide-react';\r\nimport { Meeting, TranscriptionStatus } from '../types/meeting';\r\n\r\ninterface TranscriptionManagerProps {\r\n  meetings: Meeting[];\r\n  onStartTranscription: (meetingId: string) => void;\r\n  onCancelTranscription: (meetingId: string) => void;\r\n  isTranscribing: boolean;\r\n  currentTranscriptionId?: string | null;\r\n  onDeleteMeeting: (meetingId: string) => void;\r\n  onViewResults?: () => void;\r\n}\r\n\r\nexport const TranscriptionManager: React.FC<TranscriptionManagerProps> = ({\r\n  meetings,\r\n  onStartTranscription,\r\n  onCancelTranscription,\r\n  isTranscribing,\r\n  currentTranscriptionId,\r\n  onDeleteMeeting,\r\n  onViewResults,\r\n}) => {\r\n  const pendingMeetings = meetings.filter(m => \r\n    m.transcriptionStatus.state === 'not_started' || \r\n    m.transcriptionStatus.state === 'pending' ||\r\n    m.transcriptionStatus.state === 'processing'\r\n  );\r\n\r\n  const completedMeetings = meetings.filter(m => \r\n    m.transcriptionStatus.state === 'completed'\r\n  );\r\n\r\n  const failedMeetings = meetings.filter(m => \r\n    m.transcriptionStatus.state === 'failed'\r\n  );\r\n\r\n  return (\r\n    <div className=\"flex-1 flex flex-col space-y-8 animate-fade-in\">\r\n      {/* Pending Transcriptions */}\r\n      {pendingMeetings.length > 0 && (\r\n        <div className=\"space-y-5 animate-fade-in-up\">\r\n          <h3 className=\"text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3\">\r\n            <Clock className=\"h-6 w-6 text-yellow-400 animate-pulse\" />\r\n            <span>Laukia transkribavimo ({pendingMeetings.length})</span>\r\n          </h3>\r\n          <div className=\"space-y-4\">\r\n            {pendingMeetings.map((meeting, index) => (\r\n              <div \r\n                key={meeting.id} \r\n                className=\"glassmorphic-subtle p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up\"\r\n                style={{ animationDelay: `${index * 100}ms` }}\r\n              >\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"text-lg font-semibold text-white mb-2\">{meeting.title}</h4>\r\n                    <p className=\"text-base text-white/70 mb-2\">\r\n                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s\r\n                    </p>\r\n                    {meeting.transcriptionStatus.state === 'processing' && (\r\n                      <div className=\"mt-3\">\r\n                        <div className=\"flex items-center space-x-3\">\r\n                          <div className=\"w-3 h-3 bg-blue-400 rounded-full animate-pulse\"></div>\r\n                          <span className=\"text-base text-blue-400\">\r\n                            Transkribuojama... {meeting.transcriptionStatus.progress || 0}%\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    {meeting.transcriptionStatus.state === 'not_started' && (\r\n                      <button\r\n                        onClick={() => onStartTranscription(meeting.id)}\r\n                        disabled={isTranscribing}\r\n                        className=\"p-3 text-white/60 hover:text-green-400 hover:bg-green-500/10 rounded-lg transition-all duration-200 transform hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                        title=\"Pradėti transkribavimą\"\r\n                      >\r\n                        <Play className=\"h-5 w-5\" />\r\n                      </button>\r\n                    )}\r\n                    {meeting.transcriptionStatus.state === 'processing' && currentTranscriptionId === meeting.id && (\r\n                      <button\r\n                        onClick={() => onCancelTranscription(meeting.id)}\r\n                        className=\"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\"\r\n                        title=\"Atšaukti transkribavimą\"\r\n                      >\r\n                        <Square className=\"h-5 w-5\" />\r\n                      </button>\r\n                    )}\r\n                    <button\r\n                      onClick={() => onDeleteMeeting(meeting.id)}\r\n                      className=\"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\"\r\n                      title=\"Ištrinti pokalbį\"\r\n                    >\r\n                      <Trash2 className=\"h-5 w-5\" />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Completed Transcriptions */}\r\n      {completedMeetings.length > 0 && (\r\n        <div className=\"space-y-5 animate-fade-in-up animation-delay-200\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h3 className=\"text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3\">\r\n              <CheckCircle className=\"h-6 w-6 text-green-400\" />\r\n              <span>Sėkmingai transkribuota ({completedMeetings.length})</span>\r\n            </h3>\r\n            {onViewResults && (\r\n              <button\r\n                onClick={onViewResults}\r\n                className=\"inline-flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-white bg-gradient-to-r from-green-500/80 via-green-600/70 to-emerald-600/80 hover:from-green-500/90 hover:via-green-600/80 hover:to-emerald-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-white/30 ring-1 ring-white/15 hover:border-green-300/50 transform hover:scale-105\"\r\n              >\r\n                <Headphones className=\"h-4 w-4\" />\r\n                <span>Peržiūrėti rezultatus</span>\r\n              </button>\r\n            )}\r\n          </div>\r\n          <div className=\"space-y-4\">\r\n            {completedMeetings.map((meeting, index) => (\r\n              <div \r\n                key={meeting.id} \r\n                className=\"glassmorphic-subtle p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up\"\r\n                style={{ animationDelay: `${index * 100}ms` }}\r\n              >\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"text-lg font-semibold text-white mb-2\">{meeting.title}</h4>\r\n                    <p className=\"text-base text-white/70 mb-2\">\r\n                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s\r\n                    </p>\r\n                    {meeting.transcript && (\r\n                      <p className=\"text-base text-green-400\">\r\n                        {meeting.transcript.length} segmentų • {meeting.participants?.length || 0} dalyvių\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <button\r\n                    onClick={() => onDeleteMeeting(meeting.id)}\r\n                    className=\"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\"\r\n                    title=\"Ištrinti pokalbį\"\r\n                  >\r\n                    <Trash2 className=\"h-5 w-5\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Failed Transcriptions */}\r\n      {failedMeetings.length > 0 && (\r\n        <div className=\"space-y-5 animate-fade-in-up animation-delay-400\">\r\n          <h3 className=\"text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3\">\r\n            <XCircle className=\"h-6 w-6 text-red-400\" />\r\n            <span>Nepavyko transkribuoti ({failedMeetings.length})</span>\r\n          </h3>\r\n          <div className=\"space-y-4\">\r\n            {failedMeetings.map((meeting, index) => (\r\n              <div \r\n                key={meeting.id} \r\n                className=\"bg-white/5 backdrop-blur-md border border-white/20 ring-1 ring-white/10 rounded-xl p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up\"\r\n                style={{ animationDelay: `${index * 100}ms` }}\r\n              >\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"text-lg font-semibold text-white mb-2\">{meeting.title}</h4>\r\n                    <p className=\"text-base text-white/70 mb-2\">\r\n                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s\r\n                    </p>\r\n                    {meeting.transcriptionStatus.error && (\r\n                      <p className=\"text-base text-red-400\">\r\n                        Klaida: {meeting.transcriptionStatus.error}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <button\r\n                    onClick={() => onDeleteMeeting(meeting.id)}\r\n                    className=\"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\"\r\n                    title=\"Ištrinti pokalbį\"\r\n                  >\r\n                    <Trash2 className=\"h-5 w-5\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Empty State */}\r\n      {meetings.length === 0 && (\r\n        <div className=\"flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up\">\r\n          <div className=\"w-20 h-20 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center animate-pulse\">\r\n            <Zap className=\"h-10 w-10 text-purple-400\" />\r\n          </div>\r\n          <div>\r\n            <h3 className=\"text-xl sm:text-2xl font-semibold text-white\">Nėra pokalbių</h3>\r\n            <p className=\"text-base sm:text-lg text-white/70\">\r\n              Pradėkite naują pokalbį, kad pamatytumėte jį čia\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAmBC,GAAG,EAAwCC,OAAO,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAazJ,OAAO,MAAMC,oBAAyD,GAAGA,CAAC;EACxEC,QAAQ;EACRC,oBAAoB;EACpBC,qBAAqB;EACrBC,cAAc;EACdC,sBAAsB;EACtBC,eAAe;EACfC;AACF,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAGP,QAAQ,CAACQ,MAAM,CAACC,CAAC,IACvCA,CAAC,CAACC,mBAAmB,CAACC,KAAK,KAAK,aAAa,IAC7CF,CAAC,CAACC,mBAAmB,CAACC,KAAK,KAAK,SAAS,IACzCF,CAAC,CAACC,mBAAmB,CAACC,KAAK,KAAK,YAClC,CAAC;EAED,MAAMC,iBAAiB,GAAGZ,QAAQ,CAACQ,MAAM,CAACC,CAAC,IACzCA,CAAC,CAACC,mBAAmB,CAACC,KAAK,KAAK,WAClC,CAAC;EAED,MAAME,cAAc,GAAGb,QAAQ,CAACQ,MAAM,CAACC,CAAC,IACtCA,CAAC,CAACC,mBAAmB,CAACC,KAAK,KAAK,QAClC,CAAC;EAED,oBACEb,OAAA;IAAKgB,SAAS,EAAC,gDAAgD;IAAAC,QAAA,GAE5DR,eAAe,CAACS,MAAM,GAAG,CAAC,iBACzBlB,OAAA;MAAKgB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CjB,OAAA;QAAIgB,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBACtFjB,OAAA,CAACP,KAAK;UAACuB,SAAS,EAAC;QAAuC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DtB,OAAA;UAAAiB,QAAA,GAAM,yBAAuB,EAACR,eAAe,CAACS,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACLtB,OAAA;QAAKgB,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBR,eAAe,CAACc,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAClCzB,OAAA;UAEEgB,SAAS,EAAC,uHAAuH;UACjIU,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAK,CAAE;UAAAR,QAAA,eAE9CjB,OAAA;YAAKgB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjB,OAAA;cAAKgB,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBjB,OAAA;gBAAIgB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEO,OAAO,CAACI;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1EtB,OAAA;gBAAGgB,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,GACxCO,OAAO,CAACK,IAAI,CAACC,cAAc,CAAC,OAAO,CAAC,EAAC,UAAG,EAACN,OAAO,CAACO,QAAQ,EAAC,GAC7D;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACHE,OAAO,CAACZ,mBAAmB,CAACC,KAAK,KAAK,YAAY,iBACjDb,OAAA;gBAAKgB,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBjB,OAAA;kBAAKgB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjB,OAAA;oBAAKgB,SAAS,EAAC;kBAAgD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtEtB,OAAA;oBAAMgB,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,GAAC,qBACrB,EAACO,OAAO,CAACZ,mBAAmB,CAACoB,QAAQ,IAAI,CAAC,EAAC,GAChE;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtB,OAAA;cAAKgB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GACzCO,OAAO,CAACZ,mBAAmB,CAACC,KAAK,KAAK,aAAa,iBAClDb,OAAA;gBACEiC,OAAO,EAAEA,CAAA,KAAM9B,oBAAoB,CAACqB,OAAO,CAACU,EAAE,CAAE;gBAChDC,QAAQ,EAAE9B,cAAe;gBACzBW,SAAS,EAAC,+KAA+K;gBACzLY,KAAK,EAAC,kCAAwB;gBAAAX,QAAA,eAE9BjB,OAAA,CAACT,IAAI;kBAACyB,SAAS,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACT,EACAE,OAAO,CAACZ,mBAAmB,CAACC,KAAK,KAAK,YAAY,IAAIP,sBAAsB,KAAKkB,OAAO,CAACU,EAAE,iBAC1FlC,OAAA;gBACEiC,OAAO,EAAEA,CAAA,KAAM7B,qBAAqB,CAACoB,OAAO,CAACU,EAAE,CAAE;gBACjDlB,SAAS,EAAC,2HAA2H;gBACrIY,KAAK,EAAC,mCAAyB;gBAAAX,QAAA,eAE/BjB,OAAA,CAACR,MAAM;kBAACwB,SAAS,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CACT,eACDtB,OAAA;gBACEiC,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACiB,OAAO,CAACU,EAAE,CAAE;gBAC3ClB,SAAS,EAAC,2HAA2H;gBACrIY,KAAK,EAAC,4BAAkB;gBAAAX,QAAA,eAExBjB,OAAA,CAACJ,MAAM;kBAACoB,SAAS,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjDDE,OAAO,CAACU,EAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkDZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAR,iBAAiB,CAACI,MAAM,GAAG,CAAC,iBAC3BlB,OAAA;MAAKgB,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC/DjB,OAAA;QAAKgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDjB,OAAA;UAAIgB,SAAS,EAAC,0EAA0E;UAAAC,QAAA,gBACtFjB,OAAA,CAACH,WAAW;YAACmB,SAAS,EAAC;UAAwB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDtB,OAAA;YAAAiB,QAAA,GAAM,gCAAyB,EAACH,iBAAiB,CAACI,MAAM,EAAC,GAAC;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,EACJd,aAAa,iBACZR,OAAA;UACEiC,OAAO,EAAEzB,aAAc;UACvBQ,SAAS,EAAC,8YAA8Y;UAAAC,QAAA,gBAExZjB,OAAA,CAACF,UAAU;YAACkB,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClCtB,OAAA;YAAAiB,QAAA,EAAM;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNtB,OAAA;QAAKgB,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBH,iBAAiB,CAACS,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;UAAA,IAAAW,qBAAA;UAAA,oBACpCpC,OAAA;YAEEgB,SAAS,EAAC,uHAAuH;YACjIU,KAAK,EAAE;cAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;YAAK,CAAE;YAAAR,QAAA,eAE9CjB,OAAA;cAAKgB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjB,OAAA;gBAAKgB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBjB,OAAA;kBAAIgB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAEO,OAAO,CAACI;gBAAK;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1EtB,OAAA;kBAAGgB,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,GACxCO,OAAO,CAACK,IAAI,CAACC,cAAc,CAAC,OAAO,CAAC,EAAC,UAAG,EAACN,OAAO,CAACO,QAAQ,EAAC,GAC7D;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACHE,OAAO,CAACa,UAAU,iBACjBrC,OAAA;kBAAGgB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,GACpCO,OAAO,CAACa,UAAU,CAACnB,MAAM,EAAC,wBAAY,EAAC,EAAAkB,qBAAA,GAAAZ,OAAO,CAACc,YAAY,cAAAF,qBAAA,uBAApBA,qBAAA,CAAsBlB,MAAM,KAAI,CAAC,EAAC,eAC5E;gBAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtB,OAAA;gBACEiC,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACiB,OAAO,CAACU,EAAE,CAAE;gBAC3ClB,SAAS,EAAC,2HAA2H;gBACrIY,KAAK,EAAC,4BAAkB;gBAAAX,QAAA,eAExBjB,OAAA,CAACJ,MAAM;kBAACoB,SAAS,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC,GAvBDE,OAAO,CAACU,EAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBZ,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAP,cAAc,CAACG,MAAM,GAAG,CAAC,iBACxBlB,OAAA;MAAKgB,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC/DjB,OAAA;QAAIgB,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBACtFjB,OAAA,CAACL,OAAO;UAACqB,SAAS,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CtB,OAAA;UAAAiB,QAAA,GAAM,0BAAwB,EAACF,cAAc,CAACG,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACLtB,OAAA;QAAKgB,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBF,cAAc,CAACQ,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACjCzB,OAAA;UAEEgB,SAAS,EAAC,sLAAsL;UAChMU,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAK,CAAE;UAAAR,QAAA,eAE9CjB,OAAA;YAAKgB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjB,OAAA;cAAKgB,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBjB,OAAA;gBAAIgB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEO,OAAO,CAACI;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1EtB,OAAA;gBAAGgB,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,GACxCO,OAAO,CAACK,IAAI,CAACC,cAAc,CAAC,OAAO,CAAC,EAAC,UAAG,EAACN,OAAO,CAACO,QAAQ,EAAC,GAC7D;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACHE,OAAO,CAACZ,mBAAmB,CAAC2B,KAAK,iBAChCvC,OAAA;gBAAGgB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAAC,UAC5B,EAACO,OAAO,CAACZ,mBAAmB,CAAC2B,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtB,OAAA;cACEiC,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACiB,OAAO,CAACU,EAAE,CAAE;cAC3ClB,SAAS,EAAC,2HAA2H;cACrIY,KAAK,EAAC,4BAAkB;cAAAX,QAAA,eAExBjB,OAAA,CAACJ,MAAM;gBAACoB,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,GAvBDE,OAAO,CAACU,EAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGApB,QAAQ,CAACgB,MAAM,KAAK,CAAC,iBACpBlB,OAAA;MAAKgB,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBACxGjB,OAAA;QAAKgB,SAAS,EAAC,2HAA2H;QAAAC,QAAA,eACxIjB,OAAA,CAACN,GAAG;UAACsB,SAAS,EAAC;QAA2B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACNtB,OAAA;QAAAiB,QAAA,gBACEjB,OAAA;UAAIgB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EtB,OAAA;UAAGgB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAElD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACkB,EAAA,GAtMWvC,oBAAyD;AAAA,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}