import React, { useState, useMemo, useEffect } from 'react';
import { Plus, Edit, Trash2, X, Check, Search, ChevronDown, ChevronRight } from 'lucide-react';

interface Todo {
  id: number;
  text: string;
  completed: boolean;
  date?: string;
  priority: 'low' | 'medium' | 'high';
  project?: string;
  owner?: string;
}

const TodosPage: React.FC = () => {
  // Load todos from localStorage or use default data
  const [todos, setTodos] = useState<Todo[]>(() => {
    try {
      const savedTodos = localStorage.getItem('mom-app-todos');
      if (savedTodos) {
        return JSON.parse(savedTodos);
      }
    } catch (error) {
      console.error('Error loading todos from localStorage:', error);
    }
    
    // Default todos if nothing in localStorage
    return [
      { id: 1, text: 'Pasiruošti prezentacijai', completed: false, date: '2024-01-20', priority: 'high', project: 'Aexn CRM analitika', owner: '<PERSON>' },
      { id: 2, text: '<PERSON><PERSON><PERSON><PERSON><PERSON> su klientu', completed: true, date: '2024-01-18', priority: 'medium', project: 'Aexn | Analitika', owner: '<PERSON>s <PERSON>itis' },
      { id: 3, text: 'Peržiūrėti dokumentus', completed: false, date: '2024-01-22', priority: 'low', project: 'Aexn_analitika', owner: 'Ona Onaitytė' },
      { id: 4, text: 'Planuoti kitą savaitę', completed: false, priority: 'medium', project: 'Aexn_analitika', owner: 'Jonas Jonaitis' }
    ];
  });
  
  const [editingTodo, setEditingTodo] = useState<number | null>(null);
  const [editingText, setEditingText] = useState('');
  const [editingProject, setEditingProject] = useState('');
  const [editingOwner, setEditingOwner] = useState('');
  const [newTodoText, setNewTodoText] = useState('');
  const [showNewTodoForm, setShowNewTodoForm] = useState(false);
  const [newTodoDate, setNewTodoDate] = useState('');
  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [newTodoProject, setNewTodoProject] = useState('');
  const [newTodoOwner, setNewTodoOwner] = useState('');
  const [search, setSearch] = useState('');
  const [filterProject, setFilterProject] = useState('');
  const [collapsedProjects, setCollapsedProjects] = useState<Record<string, boolean>>({});

  // Save todos to localStorage whenever todos change
  useEffect(() => {
    try {
      localStorage.setItem('mom-app-todos', JSON.stringify(todos));
    } catch (error) {
      console.error('Error saving todos to localStorage:', error);
    }
  }, [todos]);

  // Funkcijos
  const toggleTodo = (id: number) => {
    console.log('Toggle todo:', id); // Debug
    setTodos(prev => prev.map(todo => 
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const deleteTodo = (id: number) => {
    console.log('Delete todo:', id); // Debug
    setTodos(prev => prev.filter(todo => todo.id !== id));
  };

  const startEditingTodo = (todo: Todo) => {
    console.log('Start editing:', todo); // Debug
    setEditingTodo(todo.id);
    setEditingText(todo.text);
    setEditingProject(todo.project || '');
    setEditingOwner(todo.owner || '');
  };

  const saveEditedTodo = () => {
    console.log('Save edited todo'); // Debug
    if (editingTodo && editingText.trim()) {
      setTodos(prev => prev.map(todo => 
        todo.id === editingTodo ? { 
          ...todo, 
          text: editingText.trim(), 
          project: editingProject.trim() || undefined, 
          owner: editingOwner.trim() || undefined 
        } : todo
      ));
      cancelEditing();
    }
  };

  const cancelEditing = () => {
    setEditingTodo(null);
    setEditingText('');
    setEditingProject('');
    setEditingOwner('');
  };

  const addNewTodo = () => {
    console.log('Add new todo'); // Debug
    if (newTodoText.trim()) {
      const newTodo: Todo = {
        id: Date.now(),
        text: newTodoText.trim(),
        completed: false,
        date: newTodoDate || undefined,
        priority: newTodoPriority,
        project: newTodoProject.trim() || undefined,
        owner: newTodoOwner.trim() || undefined
      };
      setTodos(prev => [...prev, newTodo]);
      cancelNewTodo();
    }
  };

  const cancelNewTodo = () => {
    setShowNewTodoForm(false);
    setNewTodoText('');
    setNewTodoDate('');
    setNewTodoPriority('medium');
    setNewTodoProject('');
    setNewTodoOwner('');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-white/60';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return 'Aukštas';
      case 'medium': return 'Vidutinis';
      case 'low': return 'Žemas';
      default: return 'Nežinomas';
    }
  };

  const toggleCollapse = (project: string) => {
    console.log('Toggle collapse:', project); // Debug
    setCollapsedProjects(prev => ({ ...prev, [project]: !prev[project] }));
  };

  const completeAllInProject = (project: string) => {
    console.log('Complete all in project:', project); // Debug
    setTodos(prev => prev.map(todo => 
      todo.project === project ? { ...todo, completed: true } : todo
    ));
  };

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      action();
    }
  };

  // Memoized values
  const uniqueProjects = useMemo(() => {
    const projects = todos
      .map(todo => todo.project)
      .filter((project): project is string => Boolean(project))
      .filter((project, index, array) => array.indexOf(project) === index)
      .sort();
    return projects;
  }, [todos]);

  const filteredTodos = useMemo(() => {
    return todos.filter(todo => {
      const matchesSearch = search === '' || 
        todo.text.toLowerCase().includes(search.toLowerCase()) ||
        (todo.owner && todo.owner.toLowerCase().includes(search.toLowerCase())) ||
        (todo.project && todo.project.toLowerCase().includes(search.toLowerCase()));
      
      const matchesProject = filterProject === '' || todo.project === filterProject;
      
      return matchesSearch && matchesProject;
    });
  }, [todos, search, filterProject]);

  const groupedTodos = useMemo(() => {
    const groups = filteredTodos.reduce((groups, todo) => {
      const key = todo.project || 'Kiti projektai';
      if (!groups[key]) groups[key] = [];
      groups[key].push(todo);
      return groups;
    }, {} as Record<string, Todo[]>);
    
    // Sort todos within each group by priority and date
    Object.keys(groups).forEach(key => {
      groups[key].sort((a, b) => {
        // Sort by completion status first (incomplete first)
        if (a.completed !== b.completed) {
          return a.completed ? 1 : -1;
        }
        
        // Then by priority (high, medium, low)
        const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
        const aPriority = priorityOrder[a.priority];
        const bPriority = priorityOrder[b.priority];
        
        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }
        
        // Finally by date if available
        if (a.date && b.date) {
          return new Date(a.date).getTime() - new Date(b.date).getTime();
        }
        
        return 0;
      });
    });
    
    return groups;
  }, [filteredTodos]);

  const stats = useMemo(() => {
    const projectCount = Object.keys(groupedTodos).length;
    const totalCount = filteredTodos.length;
    const doneCount = filteredTodos.filter(t => t.completed).length;
    const undoneCount = totalCount - doneCount;
    const progress = totalCount > 0 ? Math.round((doneCount / totalCount) * 100) : 0;
    
    return { projectCount, totalCount, doneCount, undoneCount, progress };
  }, [filteredTodos, groupedTodos]);

  return (
    <div className="space-y-6">
      {/* Apžvalgos panelė */}
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6">
        <div className="flex flex-wrap gap-6 items-center">
          <div>
            <div className="text-white text-2xl font-bold">{stats.projectCount}</div>
            <div className="text-white/60 text-sm">Projektų</div>
          </div>
          <div>
            <div className="text-white text-2xl font-bold">{stats.totalCount}</div>
            <div className="text-white/60 text-sm">Užduočių</div>
          </div>
          <div>
            <div className="text-green-400 text-2xl font-bold">{stats.doneCount}</div>
            <div className="text-white/60 text-sm">Atlikta</div>
          </div>
          <div>
            <div className="text-red-400 text-2xl font-bold">{stats.undoneCount}</div>
            <div className="text-white/60 text-sm">Neatlikta</div>
          </div>
        </div>
        <div className="flex-1 flex flex-col gap-2 min-w-[200px]">
          <div className="flex items-center gap-2">
            <span className="text-white/60 text-sm">Progresas:</span>
            <span className="text-white font-semibold">{stats.progress}%</span>
          </div>
          <div className="w-full bg-white/20 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full transition-all duration-500" 
              style={{ width: `${stats.progress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Filtravimas ir paieška */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="flex gap-2 items-center w-full md:w-auto">
          {/* Paieškos laukas */}
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 pointer-events-none z-10" />
            <input
              type="text"
              value={search}
              onChange={e => setSearch(e.target.value)}
              placeholder="Ieškoti užduoties, asmens ar projekto..."
              className="w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
            />
          </div>
          
          {/* Projektų filtras */}
          <select
            value={filterProject}
            onChange={e => setFilterProject(e.target.value)}
            className="bg-white/10 border border-white/20 rounded-xl px-3 py-3 text-white focus:outline-none focus:border-blue-400 min-w-[150px] [&>option]:bg-slate-800 [&>option]:text-white"
          >
            <option value="">Visi projektai</option>
            {uniqueProjects.map(project => (
              <option key={project} value={project}>{project}</option>
            ))}
          </select>
        </div>
        
        {/* Pridėti užduotį mygtukas */}
        <button
          onClick={() => {
            console.log('Show new todo form'); // Debug
            setShowNewTodoForm(true);
          }}
          className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md"
        >
          <Plus className="h-4 w-4" />
          Pridėti užduotį
        </button>
      </div>

      {/* Užduočių grupės */}
      <div className="overflow-x-auto">
        {Object.keys(groupedTodos).length === 0 ? (
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-12 text-center">
            <div className="text-white/60 text-lg mb-2">Užduočių nerasta</div>
            <div className="text-white/40 text-sm">
              {search || filterProject ? 'Pakeiskite paieškos kriterijus' : 'Pridėkite pirmą užduotį'}
            </div>
          </div>
        ) : (
          Object.entries(groupedTodos).map(([project, projectTodos]) => {
            const projectDone = projectTodos.filter(t => t.completed).length;
            const projectTotal = projectTodos.length;
            const projectProgress = projectTotal > 0 ? Math.round((projectDone / projectTotal) * 100) : 0;
            const isCollapsed = collapsedProjects[project];

            return (
              <div key={project} className="mb-8">
                <div className="flex items-center justify-between mb-2 mt-4 border-b border-white/20 pb-2 pl-1">
                  <div className="flex items-center gap-3">
                    <button 
                      onClick={() => toggleCollapse(project)} 
                      className="focus:outline-none hover:bg-white/10 p-1 rounded-lg transition-colors"
                      aria-label={isCollapsed ? 'Išplėsti projektą' : 'Suskleisti projektą'}
                    >
                      {isCollapsed ? 
                        <ChevronRight className="h-5 w-5 text-white/60" /> : 
                        <ChevronDown className="h-5 w-5 text-white/60" />
                      }
                    </button>
                    <span className="text-white/80 text-base font-semibold">{project}</span>
                    <span className="text-xs text-white/50 bg-white/10 px-2 py-1 rounded-full">
                      {projectDone} / {projectTotal} atlikta
                    </span>
                    <div className="w-32 bg-white/20 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-500" 
                        style={{ width: `${projectProgress}%` }}
                      />
                    </div>
                  </div>
                  
                  {projectTotal > projectDone && (
                    <button
                      onClick={() => completeAllInProject(project)}
                      className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md text-sm"
                    >
                      Atlikti visas
                    </button>
                  )}
                </div>

                {!isCollapsed && (
                  <div className="bg-white/5 rounded-xl overflow-hidden">
                    <table className="min-w-full text-sm text-white">
                      <thead>
                        <tr className="bg-white/10">
                          <th className="px-4 py-3 text-left font-medium">Užduotis</th>
                          <th className="px-4 py-3 text-left font-medium">Atsakingas</th>
                          <th className="px-4 py-3 text-left font-medium">Data</th>
                          <th className="px-4 py-3 text-left font-medium">Prioritetas</th>
                          <th className="px-4 py-3 text-left font-medium">Būsena</th>
                          <th className="px-4 py-3 text-left font-medium">Veiksmai</th>
                        </tr>
                      </thead>
                      <tbody>
                        {projectTodos.map((todo) => (
                          <tr
                            key={todo.id}
                            className={`border-b border-white/5 hover:bg-white/5 transition-colors ${
                              todo.completed ? 'bg-green-500/5 text-white/70' : ''
                            }`}
                          >
                            <td className={`px-4 py-3 ${todo.completed ? 'line-through' : ''}`}>
                              {editingTodo === todo.id ? (
                                <input
                                  type="text"
                                  value={editingText}
                                  onChange={e => setEditingText(e.target.value)}
                                  onKeyPress={e => handleKeyPress(e, saveEditedTodo)}
                                  className="w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400"
                                  autoFocus
                                />
                              ) : (
                                <span className="block py-1">{todo.text}</span>
                              )}
                            </td>
                            <td className="px-4 py-3">
                              {editingTodo === todo.id ? (
                                <input
                                  type="text"
                                  value={editingOwner}
                                  onChange={e => setEditingOwner(e.target.value)}
                                  onKeyPress={e => handleKeyPress(e, saveEditedTodo)}
                                  className="w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400"
                                />
                              ) : (
                                <span className="text-white/80">{todo.owner || '—'}</span>
                              )}
                            </td>
                            <td className="px-4 py-3">
                              <span className="text-white/60">{todo.date || '—'}</span>
                            </td>
                            <td className="px-4 py-3">
                              <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`}>
                                {getPriorityLabel(todo.priority)}
                              </span>
                            </td>
                            <td className="px-4 py-3">
                              <button
                                onClick={() => toggleTodo(todo.id)}
                                className={`relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${
                                  todo.completed 
                                    ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg focus:ring-green-400' 
                                    : 'bg-white/20 focus:ring-white/40'
                                }`}
                                aria-label="Pažymėti kaip atliktą"
                              >
                                <span
                                  className={`absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center ${
                                    todo.completed ? 'translate-x-4 bg-green-400' : ''
                                  }`}
                                >
                                  {todo.completed && <Check className="h-3 w-3 text-white" />}
                                </span>
                              </button>
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex gap-2">
                                {editingTodo === todo.id ? (
                                  <>
                                    <button 
                                      onClick={saveEditedTodo} 
                                      className="bg-gradient-to-r from-green-500 to-emerald-500 text-white p-2 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center"
                                      title="Išsaugoti pakeitimus"
                                    >
                                      <Check className="h-4 w-4" />
                                    </button>
                                    <button 
                                      onClick={cancelEditing} 
                                      className="bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center"
                                      title="Atšaukti redagavimą"
                                    >
                                      <X className="h-4 w-4" />
                                    </button>
                                  </>
                                ) : (
                                  <>
                                    <button 
                                      onClick={() => startEditingTodo(todo)} 
                                      className="bg-gradient-to-r from-blue-500 to-purple-500 text-white p-2 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center"
                                      title="Redaguoti užduotį"
                                    >
                                      <Edit className="h-4 w-4" />
                                    </button>
                                    <button 
                                      onClick={() => deleteTodo(todo.id)} 
                                      className="bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center"
                                      title="Ištrinti užduotį"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </button>
                                  </>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>

      {/* Suvestinė */}
      {stats.totalCount > 0 && (
        <div className="bg-white/5 rounded-xl p-4 border border-white/10">
          <div className="text-white/60 text-sm">
            Rodoma: <span className="text-white font-medium">{stats.totalCount}</span> užduočių • 
            Užbaigta: <span className="text-green-400 font-medium">{stats.doneCount}</span> • 
            Liko: <span className="text-red-400 font-medium">{stats.undoneCount}</span>
          </div>
        </div>
      )}

      {/* Nauja užduoties forma */}
      {showNewTodoForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-white mb-4">Pridėti naują užduotį</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white/70 mb-2">Užduoties pavadinimas *</label>
                <input
                  type="text"
                  value={newTodoText}
                  onChange={e => setNewTodoText(e.target.value)}
                  onKeyPress={e => handleKeyPress(e, addNewTodo)}
                  placeholder="Įveskite užduoties pavadinimą..."
                  className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                  autoFocus
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white/70 mb-2">Atsakingas asmuo</label>
                <input
                  type="text"
                  value={newTodoOwner}
                  onChange={e => setNewTodoOwner(e.target.value)}
                  placeholder="Įveskite atsakingo asmens vardą..."
                  className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white/70 mb-2">Projektas</label>
                <input
                  type="text"
                  value={newTodoProject}
                  onChange={e => setNewTodoProject(e.target.value)}
                  placeholder="Įveskite projekto pavadinimą..."
                  className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                  list="existing-projects"
                />
                <datalist id="existing-projects">
                  {uniqueProjects.map(project => (
                    <option key={project} value={project} />
                  ))}
                </datalist>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">Data</label>
                  <input
                    type="date"
                    value={newTodoDate}
                    onChange={e => setNewTodoDate(e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">Prioritetas</label>
                  <select
                    value={newTodoPriority}
                    onChange={e => setNewTodoPriority(e.target.value as 'low' | 'medium' | 'high')}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 [&>option]:bg-slate-800 [&>option]:text-white"
                  >
                    <option value="low">Žemas</option>
                    <option value="medium">Vidutinis</option>
                    <option value="high">Aukštas</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={cancelNewTodo}
                className="flex-1 bg-white/10 text-white px-4 py-3 rounded-xl font-medium hover:bg-white/20 transition-all duration-200 border border-white/20"
              >
                Atšaukti
              </button>
              <button
                onClick={addNewTodo}
                disabled={!newTodoText.trim()}
                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Pridėti
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TodosPage; 