{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\Button.tsx\";\nimport React from 'react';\nimport clsx from 'clsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  icon,\n  variant = 'primary',\n  fullWidth = false,\n  loading = false,\n  className,\n  disabled,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: clsx('inline-flex items-center justify-center gap-2 font-medium rounded-lg transition-all duration-200 select-none', 'text-sm px-3 py-1.5 shadow-sm', fullWidth && 'w-full', variant === 'primary' && 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 focus:ring-2 focus:ring-blue-400/40', variant === 'secondary' && 'bg-white/10 text-white hover:bg-white/20 border border-white/20', (disabled || loading) && 'opacity-60 cursor-not-allowed', className),\n    disabled: disabled || loading,\n    ...props,\n    children: [icon && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"h-4 w-4 flex items-center justify-center\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 16\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"animate-pulse\",\n      children: \"Kraunasi...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 9\n    }, this) : children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "clsx", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "icon", "variant", "fullWidth", "loading", "className", "disabled", "props", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Button.tsx"], "sourcesContent": ["import React from 'react';\r\nimport clsx from 'clsx';\r\n\r\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  children: React.ReactNode;\r\n  icon?: React.ReactNode;\r\n  variant?: 'primary' | 'secondary';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  icon,\r\n  variant = 'primary',\r\n  fullWidth = false,\r\n  loading = false,\r\n  className,\r\n  disabled,\r\n  ...props\r\n}) => {\r\n  return (\r\n    <button\r\n      className={clsx(\r\n        'inline-flex items-center justify-center gap-2 font-medium rounded-lg transition-all duration-200 select-none',\r\n        'text-sm px-3 py-1.5 shadow-sm',\r\n        fullWidth && 'w-full',\r\n        variant === 'primary' &&\r\n          'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 focus:ring-2 focus:ring-blue-400/40',\r\n        variant === 'secondary' &&\r\n          'bg-white/10 text-white hover:bg-white/20 border border-white/20',\r\n        (disabled || loading) && 'opacity-60 cursor-not-allowed',\r\n        className\r\n      )}\r\n      disabled={disabled || loading}\r\n      {...props}\r\n    >\r\n      {icon && <span className=\"h-4 w-4 flex items-center justify-center\">{icon}</span>}\r\n      {loading ? (\r\n        <span className=\"animate-pulse\">Kraunasi...</span>\r\n      ) : (\r\n        children\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default Button; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUxB,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,QAAQ;EACRC,IAAI;EACJC,OAAO,GAAG,SAAS;EACnBC,SAAS,GAAG,KAAK;EACjBC,OAAO,GAAG,KAAK;EACfC,SAAS;EACTC,QAAQ;EACR,GAAGC;AACL,CAAC,KAAK;EACJ,oBACET,OAAA;IACEO,SAAS,EAAET,IAAI,CACb,8GAA8G,EAC9G,+BAA+B,EAC/BO,SAAS,IAAI,QAAQ,EACrBD,OAAO,KAAK,SAAS,IACnB,qIAAqI,EACvIA,OAAO,KAAK,WAAW,IACrB,iEAAiE,EACnE,CAACI,QAAQ,IAAIF,OAAO,KAAK,+BAA+B,EACxDC,SACF,CAAE;IACFC,QAAQ,EAAEA,QAAQ,IAAIF,OAAQ;IAAA,GAC1BG,KAAK;IAAAP,QAAA,GAERC,IAAI,iBAAIH,OAAA;MAAMO,SAAS,EAAC,0CAA0C;MAAAL,QAAA,EAAEC;IAAI;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,EAChFP,OAAO,gBACNN,OAAA;MAAMO,SAAS,EAAC,eAAe;MAAAL,QAAA,EAAC;IAAW;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,GAElDX,QACD;EAAA;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACC,EAAA,GAlCIb,MAA6B;AAoCnC,eAAeA,MAAM;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}