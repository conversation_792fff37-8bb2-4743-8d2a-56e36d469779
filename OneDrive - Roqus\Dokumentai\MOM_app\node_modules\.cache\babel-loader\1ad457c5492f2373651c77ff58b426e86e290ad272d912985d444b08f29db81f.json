{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from 'react';\nimport { MeetingsList, ErrorBoundary, WhisperStatusIndicator, TranscriptionManager, ProfessionalTranscriptViewer, DynamicAudioVisualizer } from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { createDemoMeetings } from './utils/demoData';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List, Square, Menu, X } from 'lucide-react';\nimport './styles/background.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [meetings, setMeetings] = useState([]);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState(null);\n  const [activeView, setActiveView] = useState('recording');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showTitleModal, setShowTitleModal] = useState(false);\n  const [newMeetingTitle, setNewMeetingTitle] = useState('');\n  const {\n    recordingState,\n    startRecording,\n    stopRecording,\n    pauseRecording,\n    resumeRecording\n  } = useAudioRecorder();\n  const {\n    transcript,\n    isTranscribing,\n    transcribeAudioEnhanced,\n    cancelTranscription,\n    editSegment,\n    clearTranscript,\n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n  const handleStartRecording = useCallback(async title => {\n    try {\n      await startRecording();\n      const newMeeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started'\n        }\n      };\n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n  const handleStartRecordingWithTitle = useCallback(() => {\n    setNewMeetingTitle(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n    setShowTitleModal(true);\n  }, []);\n  const confirmStartRecording = useCallback(() => {\n    if (newMeetingTitle.trim()) {\n      handleStartRecording(newMeetingTitle.trim());\n      setShowTitleModal(false);\n      setNewMeetingTitle('');\n    }\n  }, [newMeetingTitle, handleStartRecording]);\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      if (currentMeeting && audioBlob) {\n        const updatedMeeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started'\n          }\n        };\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m));\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n  const handleStartTranscription = useCallback(async meetingId => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date()\n      }\n    };\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: progress => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: {\n              ...m.transcriptionStatus,\n              progress,\n              state: 'processing'\n            }\n          } : m));\n        },\n        onStatusUpdate: status => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: status\n          } : m));\n        },\n        enhanceSpeakers: true\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date()\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n\n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence\n      });\n    } catch (error) {\n      console.error('❌ Transkribavimo klaida:', error);\n      const errorMeeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n  const handleCancelTranscription = useCallback(meetingId => {\n    cancelTranscription();\n    setMeetings(prev => prev.map(m => m.id === meetingId ? {\n      ...m,\n      transcriptionStatus: {\n        ...m.transcriptionStatus,\n        state: 'cancelled'\n      }\n    } : m));\n  }, [cancelTranscription]);\n  const handleEditSegment = useCallback((meetingId, segmentId, newText) => {\n    editSegment(segmentId, newText);\n\n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => {\n      var _meeting$transcript;\n      return meeting.id === meetingId ? {\n        ...meeting,\n        transcript: (_meeting$transcript = meeting.transcript) === null || _meeting$transcript === void 0 ? void 0 : _meeting$transcript.map(segment => segment.id === segmentId ? {\n          ...segment,\n          text: newText,\n          isEdited: true,\n          editedAt: new Date(),\n          editedBy: 'user'\n        } : segment)\n      } : meeting;\n    }));\n  }, [editSegment]);\n  const handleSelectMeeting = useCallback(meeting => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n  const handleDeleteMeeting = useCallback(meetingId => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if ((currentMeeting === null || currentMeeting === void 0 ? void 0 : currentMeeting.id) === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if ((selectedMeetingForTranscript === null || selectedMeetingForTranscript === void 0 ? void 0 : selectedMeetingForTranscript.id) === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n  const handleExportMeeting = useCallback(meeting => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus\n    };\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);\n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed',\n        progress: 100,\n        completedAt: meeting.date\n      }\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  // Close mobile menu when clicking outside or on escape\n  useEffect(() => {\n    const handleClickOutside = event => {\n      const target = event.target;\n      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n    const handleEscape = event => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n    if (isMobileMenuOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when mobile menu is open\n      document.body.classList.add('mobile-menu-open');\n    } else {\n      document.body.classList.remove('mobile-menu-open');\n    }\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('keydown', handleEscape);\n      document.body.classList.remove('mobile-menu-open');\n    };\n  }, [isMobileMenuOpen]);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: [showTitleModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl max-w-md w-full p-6 animate-scale-in\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-xl border border-blue-400/20\",\n              children: /*#__PURE__*/_jsxDEV(Mic2, {\n                className: \"h-6 w-6 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-white\",\n              children: \"Naujas pokalbis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-white/60\",\n              children: \"\\u012Eveskite pokalbio pavadinim\\u0105\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newMeetingTitle,\n              onChange: e => setNewMeetingTitle(e.target.value),\n              onKeyDown: e => e.key === 'Enter' && confirmStartRecording(),\n              className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200\",\n              placeholder: \"Pokalbio pavadinimas...\",\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setShowTitleModal(false);\n                  setNewMeetingTitle('');\n                },\n                className: \"flex-1 px-4 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white/70 hover:text-white rounded-xl transition-all duration-200 text-sm font-medium\",\n                children: \"At\\u0161aukti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: confirmStartRecording,\n                disabled: !newMeetingTitle.trim(),\n                className: \"flex-1 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 disabled:from-blue-600/50 disabled:to-indigo-600/50 text-white rounded-xl transition-all duration-200 text-sm font-medium disabled:cursor-not-allowed\",\n                children: \"Prad\\u0117ti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen elegant-background font-inter\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"elegant-grid\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          className: \"fixed top-0 left-0 right-0 z-50 notion-navbar\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between h-16\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-9 h-9 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm border border-white/10 relative overflow-hidden\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Mic2, {\n                    className: \"h-4 w-4 text-white relative z-10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-white font-semibold text-base tracking-tight\",\n                    children: \"MOM App\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/50 text-xs font-medium hidden sm:block\",\n                    children: \"Meeting Recording & Transcription\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"notion-card px-3 py-2\",\n                  children: /*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"notion-nav-group flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('recording'),\n                    className: `notion-nav-item ${activeView === 'recording' ? 'active' : 'inactive'}`,\n                    children: \"\\u012Era\\u0161ymas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('transcription'),\n                    className: `notion-nav-item ${activeView === 'transcription' ? 'active' : 'inactive'}`,\n                    children: \"Transkribavimas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('transcript'),\n                    className: `notion-nav-item ${activeView === 'transcript' ? 'active' : 'inactive'}`,\n                    children: \"Rezultatai\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:flex items-center gap-3\",\n                children: [meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: loadDemoData,\n                  className: \"notion-btn notion-btn-ghost flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 23\n                  }, this), \"Demo\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!recordingState.isRecording) {\n                      handleStartRecordingWithTitle();\n                    }\n                  },\n                  disabled: recordingState.isRecording,\n                  className: \"notion-btn notion-btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: [/*#__PURE__*/_jsxDEV(Plus, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 21\n                  }, this), \"Naujas pokalbis\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:hidden flex items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n                  className: \"notion-btn notion-btn-secondary p-2\",\n                  \"aria-expanded\": isMobileMenuOpen,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Open main menu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 21\n                  }, this), isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n                    className: \"h-5 w-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                    className: \"h-5 w-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 pt-2 pb-4 space-y-3 notion-card m-2 mobile-menu-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('recording');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `notion-btn w-full justify-start gap-3 py-3 ${activeView === 'recording' ? 'notion-btn-primary' : 'notion-btn-ghost'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Mic2, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 21\n                  }, this), \"\\u012Era\\u0161ymas\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('transcription');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `notion-btn w-full justify-start gap-3 py-3 ${activeView === 'transcription' ? 'notion-btn-primary' : 'notion-btn-ghost'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Zap, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this), \"Transkribavimas\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setActiveView('transcript');\n                    setIsMobileMenuOpen(false);\n                  },\n                  className: `notion-btn w-full justify-start gap-3 py-3 ${activeView === 'transcript' ? 'notion-btn-primary' : 'notion-btn-ghost'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Headphones, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 21\n                  }, this), \"Rezultatai\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pt-2 border-t border-white/10\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col space-y-2\",\n                  children: [meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      loadDemoData();\n                      setIsMobileMenuOpen(false);\n                    },\n                    className: \"px-4 py-3 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                      className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 25\n                    }, this), \"Demo\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setIsMobileMenuOpen(false);\n                      if (!recordingState.isRecording) {\n                        handleStartRecordingWithTitle();\n                      }\n                    },\n                    disabled: recordingState.isRecording,\n                    className: \"px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 disabled:from-gray-500/50 disabled:to-gray-600/50 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20 disabled:cursor-not-allowed\",\n                    children: [/*#__PURE__*/_jsxDEV(Plus, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 23\n                    }, this), \"Naujas pokalbis\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"px-4 py-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white/70 text-sm mr-2\",\n                        children: \"Whisper Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 554,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 555,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 py-8 pt-24 md:pt-24\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-8 min-h-[calc(100vh-140px)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-3\",\n              children: [activeView === 'recording' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5 border-b border-white/15\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(Mic2, {\n                        className: \"h-5 w-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 581,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-base font-semibold text-white\",\n                        children: \"Pokalbio \\u012Fra\\u0161ymas\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 584,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-white/50 font-medium\",\n                        children: \"Prad\\u0117kite nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 585,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6 flex-1 flex flex-col justify-center items-center min-h-[400px]\",\n                  children: !recordingState.isRecording ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"max-w-md mx-auto text-center space-y-7 animate-fade-in group\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-5\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br from-blue-500/8 via-indigo-500/6 to-purple-600/8 backdrop-blur-xl rounded-xl border border-white/4 shadow-lg group-hover:scale-105 transition-transform duration-300\",\n                        children: /*#__PURE__*/_jsxDEV(Mic2, {\n                          className: \"h-6 w-6 text-white/80\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 597,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 596,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                          className: \"text-lg sm:text-xl font-semibold text-white leading-tight tracking-tight\",\n                          children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 601,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-white/55 leading-relaxed font-medium max-w-xs mx-auto\",\n                          children: \"Profesionalus garso \\u012Fra\\u0161ymas su automatine transkribavimo technologija\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 604,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-3 gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center space-y-2 group cursor-default\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-9 h-9 bg-gradient-to-br from-blue-500/12 to-blue-600/12 rounded-lg flex items-center justify-center mx-auto border border-blue-400/8 group-hover:scale-105 group-hover:bg-blue-500/15 transition-all duration-200\",\n                          children: /*#__PURE__*/_jsxDEV(Zap, {\n                            className: \"h-3.5 w-3.5 text-blue-400/70\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 614,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 613,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-white/45 font-medium leading-tight\",\n                          children: [\"Automatinis\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 616,\n                            columnNumber: 103\n                          }, this), \"transkribavimas\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 612,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center space-y-2 group cursor-default\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-9 h-9 bg-gradient-to-br from-purple-500/12 to-purple-600/12 rounded-lg flex items-center justify-center mx-auto border border-purple-400/8 group-hover:scale-105 group-hover:bg-purple-500/15 transition-all duration-200\",\n                          children: /*#__PURE__*/_jsxDEV(Headphones, {\n                            className: \"h-3.5 w-3.5 text-purple-400/70\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 620,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 619,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-white/45 font-medium leading-tight\",\n                          children: [\"Auk\\u0161ta garso\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 622,\n                            columnNumber: 104\n                          }, this), \"kokyb\\u0117\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 622,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 618,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center space-y-2 group cursor-default\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-9 h-9 bg-gradient-to-br from-indigo-500/12 to-indigo-600/12 rounded-lg flex items-center justify-center mx-auto border border-indigo-400/8 group-hover:scale-105 group-hover:bg-indigo-500/15 transition-all duration-200\",\n                          children: /*#__PURE__*/_jsxDEV(Settings, {\n                            className: \"h-3.5 w-3.5 text-indigo-400/70\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 626,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 625,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-white/45 font-medium leading-tight\",\n                          children: [\"Pa\\u017Eang\\u016Bs\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 628,\n                            columnNumber: 100\n                          }, this), \"nustatymai\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 628,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 624,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleStartRecordingWithTitle,\n                      className: \"group relative inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-600/90 via-blue-700/90 to-indigo-700/90 hover:from-blue-500 hover:via-blue-600 hover:to-indigo-600 text-white text-sm font-medium rounded-xl transition-all duration-300 gap-2.5 shadow-lg border border-blue-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] hover:shadow-xl hover:shadow-blue-500/25\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-400/10 to-indigo-400/10 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 637,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Plus, {\n                        className: \"h-4 w-4 transition-transform duration-200 group-hover:rotate-90\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 638,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"relative z-10\",\n                        children: \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"max-w-md mx-auto text-center space-y-7 animate-fade-in\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-5\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"relative inline-flex items-center justify-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-14 h-14 bg-gradient-to-br from-red-500/12 via-red-600/8 to-red-700/12 backdrop-blur-xl rounded-xl flex items-center justify-center border border-red-400/8 shadow-lg animate-pulse\",\n                          children: /*#__PURE__*/_jsxDEV(Mic2, {\n                            className: \"h-6 w-6 text-red-400/80\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 648,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 647,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -top-0.5 -right-0.5\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 653,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 w-3 h-3 bg-red-500/40 rounded-full animate-ping\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 654,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 652,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                          className: \"text-lg sm:text-xl font-semibold text-white leading-tight tracking-tight\",\n                          children: \"Pokalbis \\u012Fra\\u0161omas\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 659,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-white/55 leading-relaxed font-medium max-w-xs mx-auto\",\n                          children: \"J\\u016Bs\\u0173 pokalbis s\\u0117kmingai \\u012Fra\\u0161omas ir bus automati\\u0161kai transkribuojamas\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 662,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 658,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white/3 backdrop-blur-md rounded-xl p-4 border border-white/5\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-2 gap-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-lg font-semibold text-white mb-1\",\n                            children: [Math.floor(recordingState.duration / 60), \":\", String(recordingState.duration % 60).padStart(2, '0')]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 672,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-white/50 font-medium\",\n                            children: \"Trukm\\u0117\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 675,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 671,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center space-x-0.5 mb-1 h-5\",\n                            children: /*#__PURE__*/_jsxDEV(DynamicAudioVisualizer, {\n                              recordingState: recordingState\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 679,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 678,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-white/50 font-medium\",\n                            children: \"Garso lygis\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 681,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 677,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleStopRecording,\n                      className: \"group relative inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-red-600/90 via-red-700/90 to-red-800/90 hover:from-red-500 hover:via-red-600 hover:to-red-700 text-white text-sm font-medium rounded-xl transition-all duration-300 gap-2.5 shadow-lg border border-red-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] hover:shadow-xl hover:shadow-red-500/25\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-red-400/10 to-red-500/10 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Square, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"relative z-10\",\n                        children: \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 693,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 17\n              }, this), activeView === 'transcription' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6 border-b border-white/20\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(Zap, {\n                        className: \"h-6 w-6 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 708,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 707,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-semibold text-white\",\n                        children: \"Transkribavimas\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-white/60\",\n                        children: \"Audio fail\\u0173 konvertavimas \\u012F tekst\\u0105 naudojant AI\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 712,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 710,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: /*#__PURE__*/_jsxDEV(TranscriptionManager, {\n                    meetings: meetings,\n                    onStartTranscription: handleStartTranscription,\n                    onCancelTranscription: handleCancelTranscription,\n                    isTranscribing: isTranscribing,\n                    currentTranscriptionId: currentTranscriptionId,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onViewResults: () => setActiveView('transcript')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 17\n              }, this), activeView === 'transcript' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6 border-b border-white/20\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(Headphones, {\n                        className: \"h-6 w-6 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 739,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-semibold text-white\",\n                        children: \"Rezultatai\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 742,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-white/60\",\n                        children: \"Per\\u017Ei\\u016Br\\u0117kite ir redaguokite transkribavimo rezultatus\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 741,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: /*#__PURE__*/_jsxDEV(ProfessionalTranscriptViewer, {\n                    meetings: meetings,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onGoToTranscription: () => setActiveView('transcription')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphic-card rounded-2xl h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5 border-b border-white/15\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-gradient-to-br from-indigo-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20\",\n                      children: /*#__PURE__*/_jsxDEV(List, {\n                        className: \"h-5 w-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 767,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 766,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-base font-semibold text-white\",\n                        children: \"Pokalbiai\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 770,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-white/50 font-medium\",\n                        children: [\"Visi pokalbiai (\", meetings.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 771,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5\",\n                  children: meetings.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-10\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 bg-white/8 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-3 border border-white/15 shadow-lg\",\n                      children: /*#__PURE__*/_jsxDEV(List, {\n                        className: \"h-5 w-5 text-white/50\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-white font-medium mb-1.5 text-sm\",\n                      children: \"N\\u0117ra pokalbi\\u0173\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-white/50 text-xs leading-relaxed\",\n                      children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 784,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/_jsxDEV(MeetingsList, {\n                    meetings: meetings,\n                    currentMeeting: currentMeeting,\n                    onSelectMeeting: handleSelectMeeting,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onExportMeeting: () => {},\n                    activeView: \"list\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 789,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 323,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"q4lqd/4P/vbhdnJrcW+YtP8rp3g=\", false, function () {\n  return [useAudioRecorder, useTranscription];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "MeetingsList", "Error<PERSON>ou<PERSON><PERSON>", "WhisperStatusIndicator", "TranscriptionManager", "ProfessionalTranscriptViewer", "DynamicAudioVisualizer", "useAudioRecorder", "useTranscription", "createDemoMeetings", "Headphones", "Plus", "Mic2", "TestTube", "Zap", "Settings", "List", "Square", "<PERSON><PERSON>", "X", "jsxDEV", "_jsxDEV", "App", "_s", "meetings", "setMeetings", "currentMeeting", "setCurrentMeeting", "selectedMeetingForTranscript", "setSelectedMeetingForTranscript", "activeView", "setActiveView", "isMobileMenuOpen", "setIsMobileMenuOpen", "showTitleModal", "setShowTitleModal", "newMeetingTitle", "setNewMeetingTitle", "recordingState", "startRecording", "stopRecording", "pauseRecording", "resumeRecording", "transcript", "isTranscribing", "transcribeAudioEnhanced", "cancelTranscription", "editSegment", "clearTranscript", "clearError", "currentTranscriptionId", "progress", "isWhisperConfigured", "handleStartRecording", "title", "newMeeting", "id", "Date", "now", "toString", "date", "duration", "status", "transcriptionStatus", "state", "prev", "error", "console", "handleStartRecordingWithTitle", "toLocaleString", "confirmStartRecording", "trim", "handleStopRecording", "audioBlob", "updatedMeeting", "Math", "floor", "getTime", "map", "m", "alert", "handleStartTranscription", "meetingId", "meeting", "find", "startedAt", "result", "onProgress", "onStatusUpdate", "enhanceSpeakers", "completedMeeting", "segments", "participants", "speakers", "metadata", "completedAt", "log", "length", "words", "totalWords", "confidence", "averageConfidence", "errorMeeting", "message", "handleCancelTranscription", "handleEditSegment", "segmentId", "newText", "_meeting$transcript", "segment", "text", "isEdited", "editedAt", "editedBy", "handleSelectMeeting", "handleDeleteMeeting", "filter", "handleExportMeeting", "exportData", "toISOString", "dataStr", "JSON", "stringify", "dataUri", "encodeURIComponent", "exportFileDefaultName", "replace", "toLowerCase", "split", "linkElement", "document", "createElement", "setAttribute", "click", "loadDemoData", "demoMeetings", "handleClickOutside", "event", "target", "closest", "handleEscape", "key", "addEventListener", "body", "classList", "add", "remove", "removeEventListener", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "onKeyDown", "placeholder", "autoFocus", "onClick", "disabled", "isRecording", "String", "padStart", "onStartTranscription", "onCancelTranscription", "onDeleteMeeting", "onViewResults", "onGoToTranscription", "onSelectMeeting", "onExportMeeting", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from 'react';\nimport {\n  <PERSON>s<PERSON>ist,\n  <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>,\n  WhisperStatusIndicator,\n  TranscriptionManager,\n  ProfessionalTranscriptViewer,\n  DynamicAudioVisualizer\n} from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { Meeting, TranscriptionStatus, Speaker } from './types/meeting';\nimport { createDemoMeetings } from './utils/demoData';\nimport { identifySpeakers } from './services/speakerService';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List, Square, Menu, X } from 'lucide-react';\nimport './styles/background.css';\n\nfunction App() {\n  const [meetings, setMeetings] = useState<Meeting[]>([]);\n  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);\n  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showTitleModal, setShowTitleModal] = useState(false);\n  const [newMeetingTitle, setNewMeetingTitle] = useState('');\n\n  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();\n  const { \n    transcript, \n    isTranscribing, \n    transcribeAudioEnhanced, \n    cancelTranscription,\n    editSegment,\n    clearTranscript, \n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n\n  const handleStartRecording = useCallback(async (title: string) => {\n    try {\n      await startRecording();\n      \n      const newMeeting: Meeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started',\n        },\n      };\n      \n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n\n  const handleStartRecordingWithTitle = useCallback(() => {\n    setNewMeetingTitle(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n    setShowTitleModal(true);\n  }, []);\n\n  const confirmStartRecording = useCallback(() => {\n    if (newMeetingTitle.trim()) {\n      handleStartRecording(newMeetingTitle.trim());\n      setShowTitleModal(false);\n      setNewMeetingTitle('');\n    }\n  }, [newMeetingTitle, handleStartRecording]);\n\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      \n      if (currentMeeting && audioBlob) {\n        const updatedMeeting: Meeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started',\n          },\n        };\n\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => \n          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)\n        );\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n\n  const handleStartTranscription = useCallback(async (meetingId: string) => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting: Meeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date(),\n      },\n    };\n\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: (progress) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: { \n                    ...m.transcriptionStatus, \n                    progress,\n                    state: 'processing' \n                  } \n                }\n              : m\n          ));\n        },\n        onStatusUpdate: (status) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: status \n                }\n              : m\n          ));\n        },\n        enhanceSpeakers: true,\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting: Meeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date(),\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n      \n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence,\n      });\n\n    } catch (error: any) {\n      console.error('❌ Transkribavimo klaida:', error);\n      \n      const errorMeeting: Meeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n\n  const handleCancelTranscription = useCallback((meetingId: string) => {\n    cancelTranscription();\n    \n    setMeetings(prev => prev.map(m => \n      m.id === meetingId \n        ? { \n            ...m, \n            transcriptionStatus: { \n              ...m.transcriptionStatus, \n              state: 'cancelled' as const \n            } \n          }\n        : m\n    ));\n  }, [cancelTranscription]);\n\n  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {\n    editSegment(segmentId, newText);\n    \n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => \n      meeting.id === meetingId\n        ? {\n            ...meeting,\n            transcript: meeting.transcript?.map(segment => \n              segment.id === segmentId \n                ? {\n                    ...segment,\n                    text: newText,\n                    isEdited: true,\n                    editedAt: new Date(),\n                    editedBy: 'user'\n                  }\n                : segment\n            ),\n          }\n        : meeting\n    ));\n  }, [editSegment]);\n\n  const handleSelectMeeting = useCallback((meeting: Meeting) => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n\n  const handleDeleteMeeting = useCallback((meetingId: string) => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if (currentMeeting?.id === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if (selectedMeetingForTranscript?.id === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n\n  const handleExportMeeting = useCallback((meeting: Meeting) => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus,\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);\n    \n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    \n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n\n\n\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed' as const,\n        progress: 100,\n        completedAt: meeting.date,\n      },\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  // Close mobile menu when clicking outside or on escape\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Element;\n      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    if (isMobileMenuOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when mobile menu is open\n      document.body.classList.add('mobile-menu-open');\n    } else {\n      document.body.classList.remove('mobile-menu-open');\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('keydown', handleEscape);\n      document.body.classList.remove('mobile-menu-open');\n    };\n  }, [isMobileMenuOpen]);\n\n  return (\n    <ErrorBoundary>\n      {/* Title Input Modal */}\n      {showTitleModal && (\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl max-w-md w-full p-6 animate-scale-in\">\n            <div className=\"space-y-4\">\n              <div className=\"text-center space-y-2\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-xl border border-blue-400/20\">\n                  <Mic2 className=\"h-6 w-6 text-blue-400\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-white\">Naujas pokalbis</h3>\n                <p className=\"text-sm text-white/60\">Įveskite pokalbio pavadinimą</p>\n              </div>\n\n              <div className=\"space-y-3\">\n                <input\n                  type=\"text\"\n                  value={newMeetingTitle}\n                  onChange={(e) => setNewMeetingTitle(e.target.value)}\n                  onKeyDown={(e) => e.key === 'Enter' && confirmStartRecording()}\n                  className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200\"\n                  placeholder=\"Pokalbio pavadinimas...\"\n                  autoFocus\n                />\n\n                <div className=\"flex gap-3\">\n                  <button\n                    onClick={() => {\n                      setShowTitleModal(false);\n                      setNewMeetingTitle('');\n                    }}\n                    className=\"flex-1 px-4 py-2.5 bg-white/5 hover:bg-white/10 border border-white/10 text-white/70 hover:text-white rounded-xl transition-all duration-200 text-sm font-medium\"\n                  >\n                    Atšaukti\n                  </button>\n                  <button\n                    onClick={confirmStartRecording}\n                    disabled={!newMeetingTitle.trim()}\n                    className=\"flex-1 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 disabled:from-blue-600/50 disabled:to-indigo-600/50 text-white rounded-xl transition-all duration-200 text-sm font-medium disabled:cursor-not-allowed\"\n                  >\n                    Pradėti\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"min-h-screen elegant-background font-inter\">\n        {/* Elegant Grid Pattern */}\n        <div className=\"elegant-grid\"></div>\n\n        {/* Sophisticated Background Elements */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl\"></div>\n          <div className=\"absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl\"></div>\n        </div>\n\n        {/* Content Wrapper */}\n        <div className=\"relative z-10\">\n          {/* Notion/Linear Style Header */}\n          <header className=\"fixed top-0 left-0 right-0 z-50 notion-navbar\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6\">\n              <div className=\"flex items-center justify-between h-16\">\n                {/* Notion/Linear Style Logo */}\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-9 h-9 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm border border-white/10 relative overflow-hidden\">\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent\"></div>\n                    <Mic2 className=\"h-4 w-4 text-white relative z-10\" />\n                  </div>\n                  <div>\n                    <h1 className=\"text-white font-semibold text-base tracking-tight\">\n                      MOM App\n                    </h1>\n                    <p className=\"text-white/50 text-xs font-medium hidden sm:block\">Meeting Recording & Transcription</p>\n                  </div>\n                </div>\n\n                {/* Notion/Linear Style Navigation */}\n                <div className=\"hidden md:flex items-center gap-4\">\n                  <div className=\"notion-card px-3 py-2\">\n                    <WhisperStatusIndicator />\n                  </div>\n                  <div className=\"notion-nav-group flex\">\n                    <button\n                      onClick={() => setActiveView('recording')}\n                      className={`notion-nav-item ${\n                        activeView === 'recording' ? 'active' : 'inactive'\n                      }`}\n                    >\n                      Įrašymas\n                    </button>\n                    <button\n                      onClick={() => setActiveView('transcription')}\n                      className={`notion-nav-item ${\n                        activeView === 'transcription' ? 'active' : 'inactive'\n                      }`}\n                    >\n                      Transkribavimas\n                    </button>\n                    <button\n                      onClick={() => setActiveView('transcript')}\n                      className={`notion-nav-item ${\n                        activeView === 'transcript' ? 'active' : 'inactive'\n                      }`}\n                    >\n                      Rezultatai\n                    </button>\n                  </div>\n                </div>\n\n                {/* Notion/Linear Style Action Buttons */}\n                <div className=\"hidden md:flex items-center gap-3\">\n                  {meetings.length === 0 && (\n                    <button\n                      onClick={loadDemoData}\n                      className=\"notion-btn notion-btn-ghost flex items-center gap-2\"\n                    >\n                      <TestTube className=\"h-4 w-4\" />\n                      Demo\n                    </button>\n                  )}\n                  <button\n                    onClick={() => {\n                      if (!recordingState.isRecording) {\n                        handleStartRecordingWithTitle();\n                      }\n                    }}\n                    disabled={recordingState.isRecording}\n                    className=\"notion-btn notion-btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <Plus className=\"h-4 w-4\" />\n                    Naujas pokalbis\n                  </button>\n                </div>\n\n                {/* Notion/Linear Style Mobile Menu Button */}\n                <div className=\"md:hidden flex items-center\">\n                  <button\n                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                    className=\"notion-btn notion-btn-secondary p-2\"\n                    aria-expanded={isMobileMenuOpen}\n                  >\n                    <span className=\"sr-only\">Open main menu</span>\n                    {isMobileMenuOpen ? (\n                      <X className=\"h-5 w-5 text-white\" />\n                    ) : (\n                      <Menu className=\"h-5 w-5 text-white\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Notion/Linear Style Mobile Menu */}\n            <div className={`md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`}>\n              <div className=\"px-4 pt-2 pb-4 space-y-3 notion-card m-2 mobile-menu-container\">\n                {/* Notion/Linear Style Mobile Navigation */}\n                <div className=\"flex flex-col space-y-1\">\n                  <button\n                    onClick={() => {\n                      setActiveView('recording');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`notion-btn w-full justify-start gap-3 py-3 ${\n                      activeView === 'recording' ? 'notion-btn-primary' : 'notion-btn-ghost'\n                    }`}\n                  >\n                    <Mic2 className=\"h-4 w-4\" />\n                    Įrašymas\n                  </button>\n                  <button\n                    onClick={() => {\n                      setActiveView('transcription');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`notion-btn w-full justify-start gap-3 py-3 ${\n                      activeView === 'transcription' ? 'notion-btn-primary' : 'notion-btn-ghost'\n                    }`}\n                  >\n                    <Zap className=\"h-4 w-4\" />\n                    Transkribavimas\n                  </button>\n                  <button\n                    onClick={() => {\n                      setActiveView('transcript');\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className={`notion-btn w-full justify-start gap-3 py-3 ${\n                      activeView === 'transcript' ? 'notion-btn-primary' : 'notion-btn-ghost'\n                    }`}\n                  >\n                    <Headphones className=\"h-4 w-4\" />\n                    Rezultatai\n                  </button>\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className=\"pt-2 border-t border-white/10\">\n                  <div className=\"flex flex-col space-y-2\">\n                    {meetings.length === 0 && (\n                      <button\n                        onClick={() => {\n                          loadDemoData();\n                          setIsMobileMenuOpen(false);\n                        }}\n                        className=\"px-4 py-3 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg flex items-center\"\n                      >\n                        <TestTube className=\"h-4 w-4 mr-2\" />\n                        Demo\n                      </button>\n                    )}\n                    <button\n                      onClick={() => {\n                        setIsMobileMenuOpen(false);\n                        if (!recordingState.isRecording) {\n                          handleStartRecordingWithTitle();\n                        }\n                      }}\n                      disabled={recordingState.isRecording}\n                      className=\"px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 disabled:from-gray-500/50 disabled:to-gray-600/50 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20 disabled:cursor-not-allowed\"\n                    >\n                      <Plus className=\"h-4 w-4\" />\n                      Naujas pokalbis\n                    </button>\n\n                    {/* Whisper Status in Mobile Menu */}\n                    <div className=\"px-4 py-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-white/70 text-sm mr-2\">Whisper Status:</span>\n                        <WhisperStatusIndicator />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n\n\n\n\n        {/* Main Content */}\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 py-8 pt-24 md:pt-24\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-8 min-h-[calc(100vh-140px)]\">\n\n            {/* Main Content Area */}\n            <div className=\"lg:col-span-3\">\n              {/* Recording View */}\n              {activeView === 'recording' && (\n                <div className=\"glassmorphic-card rounded-2xl h-full\">\n                  {/* Header */}\n                  <div className=\"p-5 border-b border-white/15\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20\">\n                        <Mic2 className=\"h-5 w-5 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-base font-semibold text-white\">Pokalbio įrašymas</h2>\n                        <p className=\"text-xs text-white/50 font-medium\">Pradėkite naują pokalbio įrašymą</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6 flex-1 flex flex-col justify-center items-center min-h-[400px]\">\n                    {!recordingState.isRecording ? (\n                      <div className=\"max-w-md mx-auto text-center space-y-7 animate-fade-in group\">\n                        {/* Elegant Header */}\n                        <div className=\"space-y-5\">\n                          <div className=\"inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br from-blue-500/8 via-indigo-500/6 to-purple-600/8 backdrop-blur-xl rounded-xl border border-white/4 shadow-lg group-hover:scale-105 transition-transform duration-300\">\n                            <Mic2 className=\"h-6 w-6 text-white/80\" />\n                          </div>\n\n                          <div className=\"space-y-3\">\n                            <h2 className=\"text-lg sm:text-xl font-semibold text-white leading-tight tracking-tight\">\n                              Pradėkite naują pokalbį\n                            </h2>\n                            <p className=\"text-xs text-white/55 leading-relaxed font-medium max-w-xs mx-auto\">\n                              Profesionalus garso įrašymas su automatine transkribavimo technologija\n                            </p>\n                          </div>\n                        </div>\n\n                        {/* Refined Features */}\n                        <div className=\"grid grid-cols-3 gap-3\">\n                          <div className=\"text-center space-y-2 group cursor-default\">\n                            <div className=\"w-9 h-9 bg-gradient-to-br from-blue-500/12 to-blue-600/12 rounded-lg flex items-center justify-center mx-auto border border-blue-400/8 group-hover:scale-105 group-hover:bg-blue-500/15 transition-all duration-200\">\n                              <Zap className=\"h-3.5 w-3.5 text-blue-400/70\" />\n                            </div>\n                            <p className=\"text-xs text-white/45 font-medium leading-tight\">Automatinis<br />transkribavimas</p>\n                          </div>\n                          <div className=\"text-center space-y-2 group cursor-default\">\n                            <div className=\"w-9 h-9 bg-gradient-to-br from-purple-500/12 to-purple-600/12 rounded-lg flex items-center justify-center mx-auto border border-purple-400/8 group-hover:scale-105 group-hover:bg-purple-500/15 transition-all duration-200\">\n                              <Headphones className=\"h-3.5 w-3.5 text-purple-400/70\" />\n                            </div>\n                            <p className=\"text-xs text-white/45 font-medium leading-tight\">Aukšta garso<br />kokybė</p>\n                          </div>\n                          <div className=\"text-center space-y-2 group cursor-default\">\n                            <div className=\"w-9 h-9 bg-gradient-to-br from-indigo-500/12 to-indigo-600/12 rounded-lg flex items-center justify-center mx-auto border border-indigo-400/8 group-hover:scale-105 group-hover:bg-indigo-500/15 transition-all duration-200\">\n                              <Settings className=\"h-3.5 w-3.5 text-indigo-400/70\" />\n                            </div>\n                            <p className=\"text-xs text-white/45 font-medium leading-tight\">Pažangūs<br />nustatymai</p>\n                          </div>\n                        </div>\n\n                        {/* Elegant CTA Button */}\n                        <button\n                          onClick={handleStartRecordingWithTitle}\n                          className=\"group relative inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-600/90 via-blue-700/90 to-indigo-700/90 hover:from-blue-500 hover:via-blue-600 hover:to-indigo-600 text-white text-sm font-medium rounded-xl transition-all duration-300 gap-2.5 shadow-lg border border-blue-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] hover:shadow-xl hover:shadow-blue-500/25\"\n                        >\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400/10 to-indigo-400/10 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                          <Plus className=\"h-4 w-4 transition-transform duration-200 group-hover:rotate-90\" />\n                          <span className=\"relative z-10\">Pradėti įrašymą</span>\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"max-w-md mx-auto text-center space-y-7 animate-fade-in\">\n                        {/* Elegant Recording Status */}\n                        <div className=\"space-y-5\">\n                          <div className=\"relative inline-flex items-center justify-center\">\n                            <div className=\"w-14 h-14 bg-gradient-to-br from-red-500/12 via-red-600/8 to-red-700/12 backdrop-blur-xl rounded-xl flex items-center justify-center border border-red-400/8 shadow-lg animate-pulse\">\n                              <Mic2 className=\"h-6 w-6 text-red-400/80\" />\n                            </div>\n\n                            {/* Subtle recording indicator */}\n                            <div className=\"absolute -top-0.5 -right-0.5\">\n                              <div className=\"w-3 h-3 bg-red-500 rounded-full animate-pulse\" />\n                              <div className=\"absolute inset-0 w-3 h-3 bg-red-500/40 rounded-full animate-ping\" />\n                            </div>\n                          </div>\n\n                          <div className=\"space-y-3\">\n                            <h2 className=\"text-lg sm:text-xl font-semibold text-white leading-tight tracking-tight\">\n                              Pokalbis įrašomas\n                            </h2>\n                            <p className=\"text-xs text-white/55 leading-relaxed font-medium max-w-xs mx-auto\">\n                              Jūsų pokalbis sėkmingai įrašomas ir bus automatiškai transkribuojamas\n                            </p>\n                          </div>\n                        </div>\n\n                        {/* Compact Recording Stats */}\n                        <div className=\"bg-white/3 backdrop-blur-md rounded-xl p-4 border border-white/5\">\n                          <div className=\"grid grid-cols-2 gap-4\">\n                            <div className=\"text-center\">\n                              <div className=\"text-lg font-semibold text-white mb-1\">\n                                {Math.floor(recordingState.duration / 60)}:{String(recordingState.duration % 60).padStart(2, '0')}\n                              </div>\n                              <div className=\"text-xs text-white/50 font-medium\">Trukmė</div>\n                            </div>\n                            <div className=\"text-center\">\n                              <div className=\"flex items-center justify-center space-x-0.5 mb-1 h-5\">\n                                <DynamicAudioVisualizer recordingState={recordingState} />\n                              </div>\n                              <div className=\"text-xs text-white/50 font-medium\">Garso lygis</div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Elegant Stop Button */}\n                        <button\n                          onClick={handleStopRecording}\n                          className=\"group relative inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-red-600/90 via-red-700/90 to-red-800/90 hover:from-red-500 hover:via-red-600 hover:to-red-700 text-white text-sm font-medium rounded-xl transition-all duration-300 gap-2.5 shadow-lg border border-red-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] hover:shadow-xl hover:shadow-red-500/25\"\n                        >\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-red-400/10 to-red-500/10 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                          <Square className=\"h-4 w-4\" />\n                          <span className=\"relative z-10\">Sustabdyti įrašymą</span>\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Transcription View */}\n              {activeView === 'transcription' && (\n                <div className=\"glassmorphic-card rounded-2xl h-full\">\n                  {/* Header */}\n                  <div className=\"p-6 border-b border-white/20\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\">\n                        <Zap className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-xl font-semibold text-white\">Transkribavimas</h2>\n                        <p className=\"text-sm text-white/60\">Audio failų konvertavimas į tekstą naudojant AI</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <TranscriptionManager\n                      meetings={meetings}\n                      onStartTranscription={handleStartTranscription}\n                      onCancelTranscription={handleCancelTranscription}\n                      isTranscribing={isTranscribing}\n                      currentTranscriptionId={currentTranscriptionId}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onViewResults={() => setActiveView('transcript')}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Transcript View */}\n              {activeView === 'transcript' && (\n                <div className=\"glassmorphic-card rounded-2xl h-full\">\n                  {/* Header */}\n                  <div className=\"p-6 border-b border-white/20\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20\">\n                        <Headphones className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-xl font-semibold text-white\">Rezultatai</h2>\n                        <p className=\"text-sm text-white/60\">Peržiūrėkite ir redaguokite transkribavimo rezultatus</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <ProfessionalTranscriptViewer\n                      meetings={meetings}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onGoToTranscription={() => setActiveView('transcription')}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Sidebar - Meetings List */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"glassmorphic-card rounded-2xl h-full\">\n                {/* Sidebar Header */}\n                <div className=\"p-5 border-b border-white/15\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-br from-indigo-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20\">\n                      <List className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <h2 className=\"text-base font-semibold text-white\">Pokalbiai</h2>\n                      <p className=\"text-xs text-white/50 font-medium\">Visi pokalbiai ({meetings.length})</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Sidebar Content */}\n                <div className=\"p-5\">\n                  {meetings.length === 0 ? (\n                    <div className=\"text-center py-10\">\n                      <div className=\"w-14 h-14 bg-white/8 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-3 border border-white/15 shadow-lg\">\n                        <List className=\"h-5 w-5 text-white/50\" />\n                      </div>\n                      <h3 className=\"text-white font-medium mb-1.5 text-sm\">Nėra pokalbių</h3>\n                      <p className=\"text-white/50 text-xs leading-relaxed\">\n                        Pradėkite naują pokalbį, kad pamatytumėte jį čia\n                      </p>\n                    </div>\n                  ) : (\n                    <MeetingsList\n                      meetings={meetings}\n                      currentMeeting={currentMeeting}\n                      onSelectMeeting={handleSelectMeeting}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onExportMeeting={() => {}}\n                      activeView=\"list\"\n                    />\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n        </div> {/* Close content wrapper */}\n      </div>\n    </ErrorBoundary>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,SACEC,YAAY,EACZC,aAAa,EACbC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,sBAAsB,QACjB,cAAc;AACrB,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,SAAS;AAE5D,SAASC,kBAAkB,QAAQ,kBAAkB;AAErD,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AACrG,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC8B,4BAA4B,EAAEC,+BAA+B,CAAC,GAAG/B,QAAQ,CAAiB,IAAI,CAAC;EACtG,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAA+C,WAAW,CAAC;EACvG,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM;IAAEwC,cAAc;IAAEC,cAAc;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAgB,CAAC,GAAGnC,gBAAgB,CAAC,CAAC;EAC7G,MAAM;IACJoC,UAAU;IACVC,cAAc;IACdC,uBAAuB;IACvBC,mBAAmB;IACnBC,WAAW;IACXC,eAAe;IACfC,UAAU;IACVC,sBAAsB;IACtBC,QAAQ;IACRC;EACF,CAAC,GAAG5C,gBAAgB,CAAC,CAAC;EAEtB,MAAM6C,oBAAoB,GAAGtD,WAAW,CAAC,MAAOuD,KAAa,IAAK;IAChE,IAAI;MACF,MAAMf,cAAc,CAAC,CAAC;MAEtB,MAAMgB,UAAmB,GAAG;QAC1BC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBL,KAAK,EAAEA,KAAK;QACZM,IAAI,EAAE,IAAIH,IAAI,CAAC,CAAC;QAChBI,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,WAAW;QACnBC,mBAAmB,EAAE;UACnBC,KAAK,EAAE;QACT;MACF,CAAC;MAEDrC,iBAAiB,CAAC4B,UAAU,CAAC;MAC7B9B,WAAW,CAACwC,IAAI,IAAI,CAACV,UAAU,EAAE,GAAGU,IAAI,CAAC,CAAC;MAC1ClC,aAAa,CAAC,WAAW,CAAC;MAC1BiB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAAC3B,cAAc,EAAES,eAAe,CAAC,CAAC;EAErC,MAAMoB,6BAA6B,GAAGrE,WAAW,CAAC,MAAM;IACtDsC,kBAAkB,CAAC,YAAY,IAAIoB,IAAI,CAAC,CAAC,CAACY,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;IACpElC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmC,qBAAqB,GAAGvE,WAAW,CAAC,MAAM;IAC9C,IAAIqC,eAAe,CAACmC,IAAI,CAAC,CAAC,EAAE;MAC1BlB,oBAAoB,CAACjB,eAAe,CAACmC,IAAI,CAAC,CAAC,CAAC;MAC5CpC,iBAAiB,CAAC,KAAK,CAAC;MACxBE,kBAAkB,CAAC,EAAE,CAAC;IACxB;EACF,CAAC,EAAE,CAACD,eAAe,EAAEiB,oBAAoB,CAAC,CAAC;EAE3C,MAAMmB,mBAAmB,GAAGzE,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAM0E,SAAS,GAAG,MAAMjC,aAAa,CAAC,CAAC;MAEvC,IAAId,cAAc,IAAI+C,SAAS,EAAE;QAC/B,MAAMC,cAAuB,GAAG;UAC9B,GAAGhD,cAAc;UACjBoC,MAAM,EAAE,WAAW;UACnBD,QAAQ,EAAEc,IAAI,CAACC,KAAK,CAAC,CAACnB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGhC,cAAc,CAACkC,IAAI,CAACiB,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;UACzEJ,SAAS;UACTV,mBAAmB,EAAE;YACnBC,KAAK,EAAE;UACT;QACF,CAAC;QAEDrC,iBAAiB,CAAC+C,cAAc,CAAC;QACjCjD,WAAW,CAACwC,IAAI,IACdA,IAAI,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK9B,cAAc,CAAC8B,EAAE,GAAGkB,cAAc,GAAGK,CAAC,CAC/D,CAAC;;QAED;QACAhD,aAAa,CAAC,eAAe,CAAC;MAChC;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDc,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC,EAAE,CAACxC,aAAa,EAAEd,cAAc,CAAC,CAAC;EAEnC,MAAMuD,wBAAwB,GAAGlF,WAAW,CAAC,MAAOmF,SAAiB,IAAK;IACxE,MAAMC,OAAO,GAAG3D,QAAQ,CAAC4D,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,CAAC;IACtD,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACV,SAAS,EAAE;;IAEpC;IACA,MAAMC,cAAuB,GAAG;MAC9B,GAAGS,OAAO;MACVpB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,SAAS;QAChBqB,SAAS,EAAE,IAAI5B,IAAI,CAAC;MACtB;IACF,CAAC;IAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GAAGR,cAAc,GAAGK,CAAC,CAAC,CAAC;IAC3ElD,+BAA+B,CAAC6C,cAAc,CAAC;IAE/C,IAAI;MACF;MACA,MAAMY,MAAM,GAAG,MAAMzC,uBAAuB,CAACsC,OAAO,CAACV,SAAS,EAAES,SAAS,EAAE;QACzEK,UAAU,EAAGpC,QAAQ,IAAK;UACxB1B,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GACd;YACE,GAAGH,CAAC;YACJhB,mBAAmB,EAAE;cACnB,GAAGgB,CAAC,CAAChB,mBAAmB;cACxBZ,QAAQ;cACRa,KAAK,EAAE;YACT;UACF,CAAC,GACDe,CACN,CAAC,CAAC;QACJ,CAAC;QACDS,cAAc,EAAG1B,MAAM,IAAK;UAC1BrC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GACd;YACE,GAAGH,CAAC;YACJhB,mBAAmB,EAAED;UACvB,CAAC,GACDiB,CACN,CAAC,CAAC;QACJ,CAAC;QACDU,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAMC,gBAAyB,GAAG;QAChC,GAAGhB,cAAc;QACjB/B,UAAU,EAAE2C,MAAM,CAACK,QAAQ;QAC3BC,YAAY,EAAEN,MAAM,CAACO,QAAQ;QAC7BC,QAAQ,EAAER,MAAM,CAACQ,QAAQ;QACzB/B,mBAAmB,EAAE;UACnBC,KAAK,EAAE,WAAW;UAClBb,QAAQ,EAAE,GAAG;UACbkC,SAAS,EAAEX,cAAc,CAACX,mBAAmB,CAACsB,SAAS;UACvDU,WAAW,EAAE,IAAItC,IAAI,CAAC;QACxB;MACF,CAAC;MAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GAAGQ,gBAAgB,GAAGX,CAAC,CAAC,CAAC;MAC7ElD,+BAA+B,CAAC6D,gBAAgB,CAAC;;MAEjD;MACA3D,aAAa,CAAC,YAAY,CAAC;MAE3BoC,OAAO,CAAC6B,GAAG,CAAC,sCAAsC,EAAE;QAClDL,QAAQ,EAAEL,MAAM,CAACK,QAAQ,CAACM,MAAM;QAChCJ,QAAQ,EAAEP,MAAM,CAACO,QAAQ,CAACI,MAAM;QAChCC,KAAK,EAAEZ,MAAM,CAACQ,QAAQ,CAACK,UAAU;QACjCC,UAAU,EAAEd,MAAM,CAACQ,QAAQ,CAACO;MAC9B,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOnC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAEhD,MAAMoC,YAAqB,GAAG;QAC5B,GAAG5B,cAAc;QACjBX,mBAAmB,EAAE;UACnBC,KAAK,EAAE,QAAQ;UACfE,KAAK,EAAEA,KAAK,CAACqC,OAAO;UACpBlB,SAAS,EAAEX,cAAc,CAACX,mBAAmB,CAACsB;QAChD;MACF,CAAC;MAED5D,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GAAGoB,YAAY,GAAGvB,CAAC,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAACvD,QAAQ,EAAEqB,uBAAuB,CAAC,CAAC;EAEvC,MAAM2D,yBAAyB,GAAGzG,WAAW,CAAEmF,SAAiB,IAAK;IACnEpC,mBAAmB,CAAC,CAAC;IAErBrB,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,GACd;MACE,GAAGH,CAAC;MACJhB,mBAAmB,EAAE;QACnB,GAAGgB,CAAC,CAAChB,mBAAmB;QACxBC,KAAK,EAAE;MACT;IACF,CAAC,GACDe,CACN,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjC,mBAAmB,CAAC,CAAC;EAEzB,MAAM2D,iBAAiB,GAAG1G,WAAW,CAAC,CAACmF,SAAiB,EAAEwB,SAAiB,EAAEC,OAAe,KAAK;IAC/F5D,WAAW,CAAC2D,SAAS,EAAEC,OAAO,CAAC;;IAE/B;IACAlF,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACa,GAAG,CAACK,OAAO;MAAA,IAAAyB,mBAAA;MAAA,OAClCzB,OAAO,CAAC3B,EAAE,KAAK0B,SAAS,GACpB;QACE,GAAGC,OAAO;QACVxC,UAAU,GAAAiE,mBAAA,GAAEzB,OAAO,CAACxC,UAAU,cAAAiE,mBAAA,uBAAlBA,mBAAA,CAAoB9B,GAAG,CAAC+B,OAAO,IACzCA,OAAO,CAACrD,EAAE,KAAKkD,SAAS,GACpB;UACE,GAAGG,OAAO;UACVC,IAAI,EAAEH,OAAO;UACbI,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,IAAIvD,IAAI,CAAC,CAAC;UACpBwD,QAAQ,EAAE;QACZ,CAAC,GACDJ,OACN;MACF,CAAC,GACD1B,OAAO;IAAA,CACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpC,WAAW,CAAC,CAAC;EAEjB,MAAMmE,mBAAmB,GAAGnH,WAAW,CAAEoF,OAAgB,IAAK;IAC5DxD,iBAAiB,CAACwD,OAAO,CAAC;IAC1B,IAAIA,OAAO,CAACxC,UAAU,IAAIwC,OAAO,CAACxC,UAAU,CAACsD,MAAM,GAAG,CAAC,EAAE;MACvDpE,+BAA+B,CAACsD,OAAO,CAAC;MACxCpD,aAAa,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoF,mBAAmB,GAAGpH,WAAW,CAAEmF,SAAiB,IAAK;IAC7DzD,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACmD,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAK0B,SAAS,CAAC,CAAC;IACzD,IAAI,CAAAxD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B,EAAE,MAAK0B,SAAS,EAAE;MACpCvD,iBAAiB,CAAC,IAAI,CAAC;IACzB;IACA,IAAI,CAAAC,4BAA4B,aAA5BA,4BAA4B,uBAA5BA,4BAA4B,CAAE4B,EAAE,MAAK0B,SAAS,EAAE;MAClDrD,+BAA+B,CAAC,IAAI,CAAC;IACvC;EACF,CAAC,EAAE,CAACH,cAAc,EAAEE,4BAA4B,CAAC,CAAC;EAElD,MAAMyF,mBAAmB,GAAGtH,WAAW,CAAEoF,OAAgB,IAAK;IAC5D,MAAMmC,UAAU,GAAG;MACjBhE,KAAK,EAAE6B,OAAO,CAAC7B,KAAK;MACpBM,IAAI,EAAEuB,OAAO,CAACvB,IAAI,CAAC2D,WAAW,CAAC,CAAC;MAChC1D,QAAQ,EAAEsB,OAAO,CAACtB,QAAQ;MAC1BlB,UAAU,EAAEwC,OAAO,CAACxC,UAAU,IAAIA,UAAU;MAC5CiD,YAAY,EAAET,OAAO,CAACS,YAAY,IAAI,EAAE;MACxCE,QAAQ,EAAEX,OAAO,CAACW,QAAQ,IAAI,CAAC,CAAC;MAChC/B,mBAAmB,EAAEoB,OAAO,CAACpB;IAC/B,CAAC;IAED,MAAMyD,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACJ,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMK,OAAO,GAAG,sCAAsC,GAAEC,kBAAkB,CAACJ,OAAO,CAAC;IAEnF,MAAMK,qBAAqB,GAAG,WAAW1C,OAAO,CAAC7B,KAAK,CAACwE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI5C,OAAO,CAACvB,IAAI,CAAC2D,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAE5I,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC/CF,WAAW,CAACG,YAAY,CAAC,MAAM,EAAET,OAAO,CAAC;IACzCM,WAAW,CAACG,YAAY,CAAC,UAAU,EAAEP,qBAAqB,CAAC;IAC3DI,WAAW,CAACI,KAAK,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC1F,UAAU,CAAC,CAAC;EAIhB,MAAM2F,YAAY,GAAGvI,WAAW,CAAC,MAAM;IACrC,MAAMwI,YAAY,GAAG9H,kBAAkB,CAAC,CAAC,CAACqE,GAAG,CAACK,OAAO,KAAK;MACxD,GAAGA,OAAO;MACVpB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,WAAoB;QAC3Bb,QAAQ,EAAE,GAAG;QACb4C,WAAW,EAAEZ,OAAO,CAACvB;MACvB;IACF,CAAC,CAAC,CAAC;IACHnC,WAAW,CAAC8G,YAAY,CAAC;IACzBxG,aAAa,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/B,SAAS,CAAC,MAAM;IACd,MAAMwI,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAiB;MACtC,IAAI1G,gBAAgB,IAAI,CAAC0G,MAAM,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;QACjE1G,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED,MAAM2G,YAAY,GAAIH,KAAoB,IAAK;MAC7C,IAAIA,KAAK,CAACI,GAAG,KAAK,QAAQ,IAAI7G,gBAAgB,EAAE;QAC9CC,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED,IAAID,gBAAgB,EAAE;MACpBkG,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1DN,QAAQ,CAACY,gBAAgB,CAAC,SAAS,EAAEF,YAAY,CAAC;MAClD;MACAV,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjD,CAAC,MAAM;MACLf,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC;IACpD;IAEA,OAAO,MAAM;MACXhB,QAAQ,CAACiB,mBAAmB,CAAC,WAAW,EAAEX,kBAAkB,CAAC;MAC7DN,QAAQ,CAACiB,mBAAmB,CAAC,SAAS,EAAEP,YAAY,CAAC;MACrDV,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAAClH,gBAAgB,CAAC,CAAC;EAEtB,oBACEX,OAAA,CAACnB,aAAa;IAAAkJ,QAAA,GAEXlH,cAAc,iBACbb,OAAA;MAAKgI,SAAS,EAAC,sFAAsF;MAAAD,QAAA,eACnG/H,OAAA;QAAKgI,SAAS,EAAC,qHAAqH;QAAAD,QAAA,eAClI/H,OAAA;UAAKgI,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB/H,OAAA;YAAKgI,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBACpC/H,OAAA;cAAKgI,SAAS,EAAC,4IAA4I;cAAAD,QAAA,eACzJ/H,OAAA,CAACT,IAAI;gBAACyI,SAAS,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNpI,OAAA;cAAIgI,SAAS,EAAC,kCAAkC;cAAAD,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEpI,OAAA;cAAGgI,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAENpI,OAAA;YAAKgI,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB/H,OAAA;cACEqI,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEvH,eAAgB;cACvBwH,QAAQ,EAAGC,CAAC,IAAKxH,kBAAkB,CAACwH,CAAC,CAACnB,MAAM,CAACiB,KAAK,CAAE;cACpDG,SAAS,EAAGD,CAAC,IAAKA,CAAC,CAAChB,GAAG,KAAK,OAAO,IAAIvE,qBAAqB,CAAC,CAAE;cAC/D+E,SAAS,EAAC,2MAA2M;cACrNU,WAAW,EAAC,yBAAyB;cACrCC,SAAS;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEFpI,OAAA;cAAKgI,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB/H,OAAA;gBACE4I,OAAO,EAAEA,CAAA,KAAM;kBACb9H,iBAAiB,CAAC,KAAK,CAAC;kBACxBE,kBAAkB,CAAC,EAAE,CAAC;gBACxB,CAAE;gBACFgH,SAAS,EAAC,kKAAkK;gBAAAD,QAAA,EAC7K;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpI,OAAA;gBACE4I,OAAO,EAAE3F,qBAAsB;gBAC/B4F,QAAQ,EAAE,CAAC9H,eAAe,CAACmC,IAAI,CAAC,CAAE;gBAClC8E,SAAS,EAAC,+PAA+P;gBAAAD,QAAA,EAC1Q;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDpI,OAAA;MAAKgI,SAAS,EAAC,4CAA4C;MAAAD,QAAA,gBAEzD/H,OAAA;QAAKgI,SAAS,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGpCpI,OAAA;QAAKgI,SAAS,EAAC,kCAAkC;QAAAD,QAAA,gBAC/C/H,OAAA;UAAKgI,SAAS,EAAC;QAAgF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGpI,OAAA;UAAKgI,SAAS,EAAC;QAAkF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxGpI,OAAA;UAAKgI,SAAS,EAAC;QAA2H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9I,CAAC,eAGNpI,OAAA;QAAKgI,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAE5B/H,OAAA;UAAQgI,SAAS,EAAC,+CAA+C;UAAAD,QAAA,gBAC/D/H,OAAA;YAAKgI,SAAS,EAAC,gCAAgC;YAAAD,QAAA,eAC7C/H,OAAA;cAAKgI,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBAErD/H,OAAA;gBAAKgI,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,gBACtC/H,OAAA;kBAAKgI,SAAS,EAAC,+JAA+J;kBAAAD,QAAA,gBAC5K/H,OAAA;oBAAKgI,SAAS,EAAC;kBAAiE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvFpI,OAAA,CAACT,IAAI;oBAACyI,SAAS,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACNpI,OAAA;kBAAA+H,QAAA,gBACE/H,OAAA;oBAAIgI,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAAC;kBAElE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLpI,OAAA;oBAAGgI,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpI,OAAA;gBAAKgI,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,gBAChD/H,OAAA;kBAAKgI,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,eACpC/H,OAAA,CAAClB,sBAAsB;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNpI,OAAA;kBAAKgI,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,gBACpC/H,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,WAAW,CAAE;oBAC1CsH,SAAS,EAAE,mBACTvH,UAAU,KAAK,WAAW,GAAG,QAAQ,GAAG,UAAU,EACjD;oBAAAsH,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpI,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,eAAe,CAAE;oBAC9CsH,SAAS,EAAE,mBACTvH,UAAU,KAAK,eAAe,GAAG,QAAQ,GAAG,UAAU,EACrD;oBAAAsH,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpI,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAMlI,aAAa,CAAC,YAAY,CAAE;oBAC3CsH,SAAS,EAAE,mBACTvH,UAAU,KAAK,YAAY,GAAG,QAAQ,GAAG,UAAU,EAClD;oBAAAsH,QAAA,EACJ;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpI,OAAA;gBAAKgI,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,GAC/C5H,QAAQ,CAACyE,MAAM,KAAK,CAAC,iBACpB5E,OAAA;kBACE4I,OAAO,EAAE3B,YAAa;kBACtBe,SAAS,EAAC,qDAAqD;kBAAAD,QAAA,gBAE/D/H,OAAA,CAACR,QAAQ;oBAACwI,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,QAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACDpI,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAAC3H,cAAc,CAAC6H,WAAW,EAAE;sBAC/B/F,6BAA6B,CAAC,CAAC;oBACjC;kBACF,CAAE;kBACF8F,QAAQ,EAAE5H,cAAc,CAAC6H,WAAY;kBACrCd,SAAS,EAAC,uGAAuG;kBAAAD,QAAA,gBAEjH/H,OAAA,CAACV,IAAI;oBAAC0I,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNpI,OAAA;gBAAKgI,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eAC1C/H,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAMhI,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBACtDqH,SAAS,EAAC,qCAAqC;kBAC/C,iBAAerH,gBAAiB;kBAAAoH,QAAA,gBAEhC/H,OAAA;oBAAMgI,SAAS,EAAC,SAAS;oBAAAD,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC9CzH,gBAAgB,gBACfX,OAAA,CAACF,CAAC;oBAACkI,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpCpI,OAAA,CAACH,IAAI;oBAACmI,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpI,OAAA;YAAKgI,SAAS,EAAE,aAAarH,gBAAgB,GAAG,OAAO,GAAG,QAAQ,EAAG;YAAAoH,QAAA,eACnE/H,OAAA;cAAKgI,SAAS,EAAC,gEAAgE;cAAAD,QAAA,gBAE7E/H,OAAA;gBAAKgI,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,gBACtC/H,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACblI,aAAa,CAAC,WAAW,CAAC;oBAC1BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACFoH,SAAS,EAAE,8CACTvH,UAAU,KAAK,WAAW,GAAG,oBAAoB,GAAG,kBAAkB,EACrE;kBAAAsH,QAAA,gBAEH/H,OAAA,CAACT,IAAI;oBAACyI,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACblI,aAAa,CAAC,eAAe,CAAC;oBAC9BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACFoH,SAAS,EAAE,8CACTvH,UAAU,KAAK,eAAe,GAAG,oBAAoB,GAAG,kBAAkB,EACzE;kBAAAsH,QAAA,gBAEH/H,OAAA,CAACP,GAAG;oBAACuI,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAE7B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpI,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACblI,aAAa,CAAC,YAAY,CAAC;oBAC3BE,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACFoH,SAAS,EAAE,8CACTvH,UAAU,KAAK,YAAY,GAAG,oBAAoB,GAAG,kBAAkB,EACtE;kBAAAsH,QAAA,gBAEH/H,OAAA,CAACX,UAAU;oBAAC2I,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAEpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNpI,OAAA;gBAAKgI,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,eAC5C/H,OAAA;kBAAKgI,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,GACrC5H,QAAQ,CAACyE,MAAM,KAAK,CAAC,iBACpB5E,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAM;sBACb3B,YAAY,CAAC,CAAC;sBACdrG,mBAAmB,CAAC,KAAK,CAAC;oBAC5B,CAAE;oBACFoH,SAAS,EAAC,uMAAuM;oBAAAD,QAAA,gBAEjN/H,OAAA,CAACR,QAAQ;sBAACwI,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,eACDpI,OAAA;oBACE4I,OAAO,EAAEA,CAAA,KAAM;sBACbhI,mBAAmB,CAAC,KAAK,CAAC;sBAC1B,IAAI,CAACK,cAAc,CAAC6H,WAAW,EAAE;wBAC/B/F,6BAA6B,CAAC,CAAC;sBACjC;oBACF,CAAE;oBACF8F,QAAQ,EAAE5H,cAAc,CAAC6H,WAAY;oBACrCd,SAAS,EAAC,0UAA0U;oBAAAD,QAAA,gBAEpV/H,OAAA,CAACV,IAAI;sBAAC0I,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAE9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAGTpI,OAAA;oBAAKgI,SAAS,EAAC,oFAAoF;oBAAAD,QAAA,eACjG/H,OAAA;sBAAKgI,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChC/H,OAAA;wBAAMgI,SAAS,EAAC,4BAA4B;wBAAAD,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnEpI,OAAA,CAAClB,sBAAsB;wBAAAmJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAOXpI,OAAA;UAAMgI,SAAS,EAAC,oDAAoD;UAAAD,QAAA,eAClE/H,OAAA;YAAKgI,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBAGvF/H,OAAA;cAAKgI,SAAS,EAAC,eAAe;cAAAD,QAAA,GAE3BtH,UAAU,KAAK,WAAW,iBACzBT,OAAA;gBAAKgI,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnD/H,OAAA;kBAAKgI,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3C/H,OAAA;oBAAKgI,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtC/H,OAAA;sBAAKgI,SAAS,EAAC,6JAA6J;sBAAAD,QAAA,eAC1K/H,OAAA,CAACT,IAAI;wBAACyI,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACNpI,OAAA;sBAAA+H,QAAA,gBACE/H,OAAA;wBAAIgI,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAiB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzEpI,OAAA;wBAAGgI,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,EAAC;sBAAgC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNpI,OAAA;kBAAKgI,SAAS,EAAC,oEAAoE;kBAAAD,QAAA,EAChF,CAAC9G,cAAc,CAAC6H,WAAW,gBAC1B9I,OAAA;oBAAKgI,SAAS,EAAC,8DAA8D;oBAAAD,QAAA,gBAE3E/H,OAAA;sBAAKgI,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACxB/H,OAAA;wBAAKgI,SAAS,EAAC,0OAA0O;wBAAAD,QAAA,eACvP/H,OAAA,CAACT,IAAI;0BAACyI,SAAS,EAAC;wBAAuB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eAENpI,OAAA;wBAAKgI,SAAS,EAAC,WAAW;wBAAAD,QAAA,gBACxB/H,OAAA;0BAAIgI,SAAS,EAAC,0EAA0E;0BAAAD,QAAA,EAAC;wBAEzF;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACLpI,OAAA;0BAAGgI,SAAS,EAAC,oEAAoE;0BAAAD,QAAA,EAAC;wBAElF;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNpI,OAAA;sBAAKgI,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,gBACrC/H,OAAA;wBAAKgI,SAAS,EAAC,4CAA4C;wBAAAD,QAAA,gBACzD/H,OAAA;0BAAKgI,SAAS,EAAC,qNAAqN;0BAAAD,QAAA,eAClO/H,OAAA,CAACP,GAAG;4BAACuI,SAAS,EAAC;0BAA8B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7C,CAAC,eACNpI,OAAA;0BAAGgI,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,GAAC,aAAW,eAAA/H,OAAA;4BAAAiI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,mBAAe;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChG,CAAC,eACNpI,OAAA;wBAAKgI,SAAS,EAAC,4CAA4C;wBAAAD,QAAA,gBACzD/H,OAAA;0BAAKgI,SAAS,EAAC,6NAA6N;0BAAAD,QAAA,eAC1O/H,OAAA,CAACX,UAAU;4BAAC2I,SAAS,EAAC;0BAAgC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtD,CAAC,eACNpI,OAAA;0BAAGgI,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,GAAC,mBAAY,eAAA/H,OAAA;4BAAAiI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAAM;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxF,CAAC,eACNpI,OAAA;wBAAKgI,SAAS,EAAC,4CAA4C;wBAAAD,QAAA,gBACzD/H,OAAA;0BAAKgI,SAAS,EAAC,6NAA6N;0BAAAD,QAAA,eAC1O/H,OAAA,CAACN,QAAQ;4BAACsI,SAAS,EAAC;0BAAgC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpD,CAAC,eACNpI,OAAA;0BAAGgI,SAAS,EAAC,iDAAiD;0BAAAD,QAAA,GAAC,oBAAQ,eAAA/H,OAAA;4BAAAiI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,cAAU;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNpI,OAAA;sBACE4I,OAAO,EAAE7F,6BAA8B;sBACvCiF,SAAS,EAAC,6ZAA6Z;sBAAAD,QAAA,gBAEva/H,OAAA;wBAAKgI,SAAS,EAAC;sBAA0J;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5KpI,OAAA,CAACV,IAAI;wBAAC0I,SAAS,EAAC;sBAAiE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpFpI,OAAA;wBAAMgI,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAENpI,OAAA;oBAAKgI,SAAS,EAAC,wDAAwD;oBAAAD,QAAA,gBAErE/H,OAAA;sBAAKgI,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACxB/H,OAAA;wBAAKgI,SAAS,EAAC,kDAAkD;wBAAAD,QAAA,gBAC/D/H,OAAA;0BAAKgI,SAAS,EAAC,sLAAsL;0BAAAD,QAAA,eACnM/H,OAAA,CAACT,IAAI;4BAACyI,SAAS,EAAC;0BAAyB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,eAGNpI,OAAA;0BAAKgI,SAAS,EAAC,8BAA8B;0BAAAD,QAAA,gBAC3C/H,OAAA;4BAAKgI,SAAS,EAAC;0BAA+C;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACjEpI,OAAA;4BAAKgI,SAAS,EAAC;0BAAkE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENpI,OAAA;wBAAKgI,SAAS,EAAC,WAAW;wBAAAD,QAAA,gBACxB/H,OAAA;0BAAIgI,SAAS,EAAC,0EAA0E;0BAAAD,QAAA,EAAC;wBAEzF;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACLpI,OAAA;0BAAGgI,SAAS,EAAC,oEAAoE;0BAAAD,QAAA,EAAC;wBAElF;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNpI,OAAA;sBAAKgI,SAAS,EAAC,kEAAkE;sBAAAD,QAAA,eAC/E/H,OAAA;wBAAKgI,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,gBACrC/H,OAAA;0BAAKgI,SAAS,EAAC,aAAa;0BAAAD,QAAA,gBAC1B/H,OAAA;4BAAKgI,SAAS,EAAC,uCAAuC;4BAAAD,QAAA,GACnDzE,IAAI,CAACC,KAAK,CAACtC,cAAc,CAACuB,QAAQ,GAAG,EAAE,CAAC,EAAC,GAAC,EAACuG,MAAM,CAAC9H,cAAc,CAACuB,QAAQ,GAAG,EAAE,CAAC,CAACwG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;0BAAA;4BAAAf,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9F,CAAC,eACNpI,OAAA;4BAAKgI,SAAS,EAAC,mCAAmC;4BAAAD,QAAA,EAAC;0BAAM;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC,eACNpI,OAAA;0BAAKgI,SAAS,EAAC,aAAa;0BAAAD,QAAA,gBAC1B/H,OAAA;4BAAKgI,SAAS,EAAC,uDAAuD;4BAAAD,QAAA,eACpE/H,OAAA,CAACf,sBAAsB;8BAACgC,cAAc,EAAEA;4BAAe;8BAAAgH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvD,CAAC,eACNpI,OAAA;4BAAKgI,SAAS,EAAC,mCAAmC;4BAAAD,QAAA,EAAC;0BAAW;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNpI,OAAA;sBACE4I,OAAO,EAAEzF,mBAAoB;sBAC7B6E,SAAS,EAAC,iZAAiZ;sBAAAD,QAAA,gBAE3Z/H,OAAA;wBAAKgI,SAAS,EAAC;sBAAsJ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACxKpI,OAAA,CAACJ,MAAM;wBAACoI,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC9BpI,OAAA;wBAAMgI,SAAS,EAAC,eAAe;wBAAAD,QAAA,EAAC;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA3H,UAAU,KAAK,eAAe,iBAC7BT,OAAA;gBAAKgI,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnD/H,OAAA;kBAAKgI,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3C/H,OAAA;oBAAKgI,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtC/H,OAAA;sBAAKgI,SAAS,EAAC,6JAA6J;sBAAAD,QAAA,eAC1K/H,OAAA,CAACP,GAAG;wBAACuI,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACNpI,OAAA;sBAAA+H,QAAA,gBACE/H,OAAA;wBAAIgI,SAAS,EAAC,kCAAkC;wBAAAD,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrEpI,OAAA;wBAAGgI,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,EAAC;sBAA+C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNpI,OAAA;kBAAKgI,SAAS,EAAC,KAAK;kBAAAD,QAAA,eAClB/H,OAAA,CAACjB,oBAAoB;oBACnBoB,QAAQ,EAAEA,QAAS;oBACnB8I,oBAAoB,EAAErF,wBAAyB;oBAC/CsF,qBAAqB,EAAE/D,yBAA0B;oBACjD5D,cAAc,EAAEA,cAAe;oBAC/BM,sBAAsB,EAAEA,sBAAuB;oBAC/CsH,eAAe,EAAErD,mBAAoB;oBACrCsD,aAAa,EAAEA,CAAA,KAAM1I,aAAa,CAAC,YAAY;kBAAE;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA3H,UAAU,KAAK,YAAY,iBAC1BT,OAAA;gBAAKgI,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnD/H,OAAA;kBAAKgI,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3C/H,OAAA;oBAAKgI,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtC/H,OAAA;sBAAKgI,SAAS,EAAC,+JAA+J;sBAAAD,QAAA,eAC5K/H,OAAA,CAACX,UAAU;wBAAC2I,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACNpI,OAAA;sBAAA+H,QAAA,gBACE/H,OAAA;wBAAIgI,SAAS,EAAC,kCAAkC;wBAAAD,QAAA,EAAC;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChEpI,OAAA;wBAAGgI,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,EAAC;sBAAqD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNpI,OAAA;kBAAKgI,SAAS,EAAC,KAAK;kBAAAD,QAAA,eAClB/H,OAAA,CAAChB,4BAA4B;oBAC3BmB,QAAQ,EAAEA,QAAS;oBACnBgJ,eAAe,EAAErD,mBAAoB;oBACrCuD,mBAAmB,EAAEA,CAAA,KAAM3I,aAAa,CAAC,eAAe;kBAAE;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNpI,OAAA;cAAKgI,SAAS,EAAC,eAAe;cAAAD,QAAA,eAC5B/H,OAAA;gBAAKgI,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,gBAEnD/H,OAAA;kBAAKgI,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,eAC3C/H,OAAA;oBAAKgI,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtC/H,OAAA;sBAAKgI,SAAS,EAAC,+JAA+J;sBAAAD,QAAA,eAC5K/H,OAAA,CAACL,IAAI;wBAACqI,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACNpI,OAAA;sBAAA+H,QAAA,gBACE/H,OAAA;wBAAIgI,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjEpI,OAAA;wBAAGgI,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,GAAC,kBAAgB,EAAC5H,QAAQ,CAACyE,MAAM,EAAC,GAAC;sBAAA;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNpI,OAAA;kBAAKgI,SAAS,EAAC,KAAK;kBAAAD,QAAA,EACjB5H,QAAQ,CAACyE,MAAM,KAAK,CAAC,gBACpB5E,OAAA;oBAAKgI,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,gBAChC/H,OAAA;sBAAKgI,SAAS,EAAC,mIAAmI;sBAAAD,QAAA,eAChJ/H,OAAA,CAACL,IAAI;wBAACqI,SAAS,EAAC;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNpI,OAAA;sBAAIgI,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxEpI,OAAA;sBAAGgI,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,gBAENpI,OAAA,CAACpB,YAAY;oBACXuB,QAAQ,EAAEA,QAAS;oBACnBE,cAAc,EAAEA,cAAe;oBAC/BiJ,eAAe,EAAEzD,mBAAoB;oBACrCsD,eAAe,EAAErD,mBAAoB;oBACrCyD,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;oBAC1B9I,UAAU,EAAC;kBAAM;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAClI,EAAA,CAtxBQD,GAAG;EAAA,QASiFf,gBAAgB,EAYvGC,gBAAgB;AAAA;AAAAqK,EAAA,GArBbvJ,GAAG;AAwxBZ,eAAeA,GAAG;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}