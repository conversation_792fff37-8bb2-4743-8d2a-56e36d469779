{"permissions": {"allow": ["mcp__sequential-thinking__sequentialthinking", "Bash(npm start)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(timeout 30s npm start)", "Bash(npx eslint:*)", "Bash(powershell.exe:*)", "Bash(PORT=1982 npm start)", "Bash(kill:*)", "<PERSON><PERSON>(timeout 30s npm run start)", "Bash(find:*)", "Bash(npm outdated)", "Bash(npm audit:*)", "<PERSON><PERSON>(sudo mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "Bash(npm install:*)", "Bash(timeout 30s npm run build)", "Bash(rm:*)", "Bash(rsync:*)", "Bash(timeout 60s npm run build)", "Bash(npx react-scripts:*)", "Bash(grep:*)", "Bash(DISABLE_ESLINT_PLUGIN=true npm run build)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm update:*)", "Bash(echo)", "<PERSON><PERSON>(echo:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm run typecheck:*)", "Bash(npm run lint)", "<PERSON><PERSON>(curl:*)", "Bash(ps:*)", "<PERSON><PERSON>(pkill:*)", "Bash(PORT=1800 npm start)", "Bash(ss:*)", "<PERSON><PERSON>(wget:*)", "Bash(fuser:*)", "Bash(FORCE_COLOR=0 BROWSER=none PORT=1800 FAST_REFRESH=true npm start)", "<PERSON><PERSON>(timeout:*)", "Bash(unset:*)", "Bash(export NODE_ENV=development)", "Bash(claude code mcp)", "Bash(FIRECRAWL_API_KEY=fc-2f574fdf48784cdca36e4fc85b6703e5 npx -y firecrawl-mcp --version)", "Bash(npm search:*)", "<PERSON><PERSON>(mcp list:*)", "Bash(npx:*)", "Bash(FIRECRAWL_API_KEY=\"fc-2f574fdf48784cdca36e4fc85b6703e5\" npx -y firecrawl-mcp --version)", "mcp__firecrawl__crawl", "mcp__firecrawl__scrape", "mcp__firecrawl__search", "mcp__bright-data__scrape_as_markdown", "mcp__bright-data__scrape_as_html", "mcp__bright-data__scraping_browser_navigate", "mcp__computer-use__list_directory", "mcp__computer-use__read_file", "mcp__computer-use__edit_block"], "deny": []}}