{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingPanel.tsx\";\nimport React from 'react';\nimport { Plus, Mic2, Square } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RecordingPanel = ({\n  recordingState,\n  onStartRecording,\n  onStopRecording\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col justify-center items-center space-y-12 p-8 animate-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-8 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative group\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `absolute inset-0 rounded-full transition-all duration-1000 ease-out ${recordingState.isRecording ? 'bg-gradient-to-br from-red-500/20 via-red-600/15 to-red-700/10 animate-pulse scale-125' : 'bg-gradient-to-br from-slate-500/10 via-blue-600/8 to-indigo-700/5 scale-110 group-hover:scale-125'}`,\n          style: {\n            width: '200px',\n            height: '200px',\n            marginLeft: '-100px',\n            marginTop: '-100px',\n            left: '50%',\n            top: '50%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `absolute inset-0 rounded-full transition-all duration-700 ease-out ${recordingState.isRecording ? 'bg-gradient-to-br from-red-500/30 via-red-600/25 to-red-700/20 scale-115' : 'bg-gradient-to-br from-slate-600/15 via-blue-600/12 to-indigo-700/8 scale-105 group-hover:scale-115'}`,\n          style: {\n            width: '160px',\n            height: '160px',\n            marginLeft: '-80px',\n            marginTop: '-80px',\n            left: '50%',\n            top: '50%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `relative w-32 h-32 sm:w-36 sm:h-36 rounded-full flex items-center justify-center transition-all duration-700 ease-out transform border-2 backdrop-blur-xl ${recordingState.isRecording ? 'bg-gradient-to-br from-red-500/90 via-red-600/85 to-red-700/80 border-red-400/40 shadow-2xl shadow-red-500/30 scale-110 animate-pulse' : 'bg-gradient-to-br from-slate-800/80 via-slate-700/75 to-slate-600/70 border-slate-400/30 shadow-2xl shadow-slate-500/20 scale-100 hover:scale-105 group-hover:border-blue-400/50'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `absolute inset-2 rounded-full transition-all duration-500 ${recordingState.isRecording ? 'bg-gradient-to-br from-red-400/40 to-red-600/20' : 'bg-gradient-to-br from-slate-400/20 to-slate-600/10 group-hover:from-blue-400/20 group-hover:to-indigo-600/10'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Mic2, {\n            className: `relative z-10 h-12 w-12 sm:h-14 sm:w-14 text-white transition-all duration-500 ${recordingState.isRecording ? 'animate-bounce drop-shadow-lg' : 'hover:scale-110 group-hover:drop-shadow-lg'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-4 left-4 w-6 h-6 bg-white/20 rounded-full blur-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4 animate-fade-in-up max-w-lg mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white via-slate-100 to-slate-200 bg-clip-text text-transparent transition-all duration-300 leading-tight tracking-tight\",\n          children: recordingState.isRecording ? 'Įrašoma...' : 'Pradėkite naują pokalbį'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg text-slate-300/80 max-w-md mx-auto transition-all duration-300 leading-relaxed font-medium tracking-wide\",\n          children: recordingState.isRecording ? 'Profesionalus garso įrašymas su automatine transkribavimo technologija' : 'Profesionalus garso įrašymas su automatine transkribavimo technologija'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row gap-6 animate-fade-in-up animation-delay-200\",\n      children: !recordingState.isRecording ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`),\n        className: \"group relative inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-blue-500/30\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-br from-slate-800/90 via-slate-700/85 to-slate-600/80 transition-all duration-500 group-hover:from-blue-600/90 group-hover:via-blue-700/85 group-hover:to-indigo-700/80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-[1px] bg-gradient-to-br from-white/10 to-transparent rounded-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-2xl border-2 border-slate-400/30 transition-all duration-500 group-hover:border-blue-400/50 group-hover:shadow-2xl group-hover:shadow-blue-500/25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 rounded-full bg-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\",\n            children: /*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 transition-transform duration-300 group-hover:rotate-90\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tracking-wide\",\n            children: \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onStopRecording,\n        className: \"group relative inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-red-500/30\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-br from-red-600/90 via-red-700/85 to-red-800/80 transition-all duration-500 group-hover:from-red-700/90 group-hover:via-red-800/85 group-hover:to-red-900/80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-[1px] bg-gradient-to-br from-white/10 to-transparent rounded-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-2xl border-2 border-red-400/40 transition-all duration-500 group-hover:border-red-300/60 group-hover:shadow-2xl group-hover:shadow-red-500/30\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 rounded-sm bg-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\",\n            children: /*#__PURE__*/_jsxDEV(Square, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tracking-wide\",\n            children: \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), recordingState.isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3 text-red-400 animate-fade-in-up animation-delay-400\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-3 h-3 bg-red-400 rounded-full animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-base font-medium\",\n        children: \"\\u012Era\\u0161oma...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = RecordingPanel;\nvar _c;\n$RefreshReg$(_c, \"RecordingPanel\");", "map": {"version": 3, "names": ["React", "Plus", "Mic2", "Square", "jsxDEV", "_jsxDEV", "RecordingPanel", "recordingState", "onStartRecording", "onStopRecording", "className", "children", "isRecording", "style", "width", "height", "marginLeft", "marginTop", "left", "top", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "Date", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPanel.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { Plus, Mic2, Square, Pause, Play, AlertCircle } from 'lucide-react';\r\nimport { RecordingButton } from './RecordingButton';\r\nimport { RecordingIndicator } from './RecordingIndicator';\r\nimport { Meeting, RecordingState } from '../types/meeting';\r\n\r\ninterface RecordingPanelProps {\r\n  recordingState: RecordingState;\r\n  currentMeeting: Meeting | null;\r\n  onStartRecording: (title: string) => Promise<void>;\r\n  onStopRecording: () => Promise<void>;\r\n  onPauseRecording: () => void;\r\n  onResumeRecording: () => void;\r\n}\r\n\r\nexport const RecordingPanel: React.FC<RecordingPanelProps> = ({\r\n  recordingState,\r\n  onStartRecording,\r\n  onStopRecording,\r\n}) => {\r\n  return (\r\n    <div className=\"flex-1 flex flex-col justify-center items-center space-y-12 p-8 animate-fade-in\">\r\n      {/* Elegant Recording Status Display */}\r\n      <div className=\"text-center space-y-8 relative\">\r\n        {/* Sophisticated Recording Orb */}\r\n        <div className=\"relative group\">\r\n          {/* Outer Ring with Subtle Animation */}\r\n          <div className={`absolute inset-0 rounded-full transition-all duration-1000 ease-out ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-br from-red-500/20 via-red-600/15 to-red-700/10 animate-pulse scale-125'\r\n              : 'bg-gradient-to-br from-slate-500/10 via-blue-600/8 to-indigo-700/5 scale-110 group-hover:scale-125'\r\n          }`} style={{ width: '200px', height: '200px', marginLeft: '-100px', marginTop: '-100px', left: '50%', top: '50%' }}></div>\r\n\r\n          {/* Middle Ring */}\r\n          <div className={`absolute inset-0 rounded-full transition-all duration-700 ease-out ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-br from-red-500/30 via-red-600/25 to-red-700/20 scale-115'\r\n              : 'bg-gradient-to-br from-slate-600/15 via-blue-600/12 to-indigo-700/8 scale-105 group-hover:scale-115'\r\n          }`} style={{ width: '160px', height: '160px', marginLeft: '-80px', marginTop: '-80px', left: '50%', top: '50%' }}></div>\r\n\r\n          {/* Main Recording Orb */}\r\n          <div className={`relative w-32 h-32 sm:w-36 sm:h-36 rounded-full flex items-center justify-center transition-all duration-700 ease-out transform border-2 backdrop-blur-xl ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-br from-red-500/90 via-red-600/85 to-red-700/80 border-red-400/40 shadow-2xl shadow-red-500/30 scale-110 animate-pulse'\r\n              : 'bg-gradient-to-br from-slate-800/80 via-slate-700/75 to-slate-600/70 border-slate-400/30 shadow-2xl shadow-slate-500/20 scale-100 hover:scale-105 group-hover:border-blue-400/50'\r\n          }`}>\r\n            {/* Inner Glow Effect */}\r\n            <div className={`absolute inset-2 rounded-full transition-all duration-500 ${\r\n              recordingState.isRecording\r\n                ? 'bg-gradient-to-br from-red-400/40 to-red-600/20'\r\n                : 'bg-gradient-to-br from-slate-400/20 to-slate-600/10 group-hover:from-blue-400/20 group-hover:to-indigo-600/10'\r\n            }`}></div>\r\n\r\n            {/* Microphone Icon */}\r\n            <Mic2 className={`relative z-10 h-12 w-12 sm:h-14 sm:w-14 text-white transition-all duration-500 ${\r\n              recordingState.isRecording ? 'animate-bounce drop-shadow-lg' : 'hover:scale-110 group-hover:drop-shadow-lg'\r\n            }`} />\r\n\r\n            {/* Subtle Inner Highlight */}\r\n            <div className=\"absolute top-4 left-4 w-6 h-6 bg-white/20 rounded-full blur-sm\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Typography Section */}\r\n        <div className=\"space-y-4 animate-fade-in-up max-w-lg mx-auto\">\r\n          <h3 className=\"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white via-slate-100 to-slate-200 bg-clip-text text-transparent transition-all duration-300 leading-tight tracking-tight\">\r\n            {recordingState.isRecording ? 'Įrašoma...' : 'Pradėkite naują pokalbį'}\r\n          </h3>\r\n          <p className=\"text-base sm:text-lg text-slate-300/80 max-w-md mx-auto transition-all duration-300 leading-relaxed font-medium tracking-wide\">\r\n            {recordingState.isRecording\r\n              ? 'Profesionalus garso įrašymas su automatine transkribavimo technologija'\r\n              : 'Profesionalus garso įrašymas su automatine transkribavimo technologija'\r\n            }\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Sophisticated Recording Controls */}\r\n      <div className=\"flex flex-col sm:flex-row gap-6 animate-fade-in-up animation-delay-200\">\r\n        {!recordingState.isRecording ? (\r\n          <button\r\n            onClick={() => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}\r\n            className=\"group relative inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-blue-500/30\"\r\n          >\r\n            {/* Sophisticated Background Layers */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-slate-800/90 via-slate-700/85 to-slate-600/80 transition-all duration-500 group-hover:from-blue-600/90 group-hover:via-blue-700/85 group-hover:to-indigo-700/80\"></div>\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"></div>\r\n            <div className=\"absolute inset-[1px] bg-gradient-to-br from-white/10 to-transparent rounded-2xl\"></div>\r\n\r\n            {/* Animated Border */}\r\n            <div className=\"absolute inset-0 rounded-2xl border-2 border-slate-400/30 transition-all duration-500 group-hover:border-blue-400/50 group-hover:shadow-2xl group-hover:shadow-blue-500/25\"></div>\r\n\r\n            {/* Content */}\r\n            <div className=\"relative z-10 flex items-center space-x-3\">\r\n              <div className=\"w-6 h-6 rounded-full bg-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\">\r\n                <Plus className=\"h-4 w-4 transition-transform duration-300 group-hover:rotate-90\" />\r\n              </div>\r\n              <span className=\"tracking-wide\">Pradėti įrašymą</span>\r\n            </div>\r\n\r\n            {/* Subtle Shine Effect */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out\"></div>\r\n          </button>\r\n        ) : (\r\n          <button\r\n            onClick={onStopRecording}\r\n            className=\"group relative inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-red-500/30\"\r\n          >\r\n            {/* Sophisticated Background Layers */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-red-600/90 via-red-700/85 to-red-800/80 transition-all duration-500 group-hover:from-red-700/90 group-hover:via-red-800/85 group-hover:to-red-900/80\"></div>\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"></div>\r\n            <div className=\"absolute inset-[1px] bg-gradient-to-br from-white/10 to-transparent rounded-2xl\"></div>\r\n\r\n            {/* Animated Border */}\r\n            <div className=\"absolute inset-0 rounded-2xl border-2 border-red-400/40 transition-all duration-500 group-hover:border-red-300/60 group-hover:shadow-2xl group-hover:shadow-red-500/30\"></div>\r\n\r\n            {/* Content */}\r\n            <div className=\"relative z-10 flex items-center space-x-3\">\r\n              <div className=\"w-6 h-6 rounded-sm bg-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\">\r\n                <Square className=\"h-4 w-4\" />\r\n              </div>\r\n              <span className=\"tracking-wide\">Sustabdyti įrašymą</span>\r\n            </div>\r\n\r\n            {/* Subtle Shine Effect */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out\"></div>\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Recording Indicator */}\r\n      {recordingState.isRecording && (\r\n        <div className=\"flex items-center space-x-3 text-red-400 animate-fade-in-up animation-delay-400\">\r\n          <div className=\"w-3 h-3 bg-red-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"text-base font-medium\">Įrašoma...</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";AAAA,OAAOA,KAAK,MAAiC,OAAO;AACpD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAkC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc5E,OAAO,MAAMC,cAA6C,GAAGA,CAAC;EAC5DC,cAAc;EACdC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EACJ,oBACEJ,OAAA;IAAKK,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAE9FN,OAAA;MAAKK,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAE7CN,OAAA;QAAKK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAE7BN,OAAA;UAAKK,SAAS,EAAE,uEACdH,cAAc,CAACK,WAAW,GACtB,wFAAwF,GACxF,oGAAoG,EACvG;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE,OAAO;YAAEC,UAAU,EAAE,QAAQ;YAAEC,SAAS,EAAE,QAAQ;YAAEC,IAAI,EAAE,KAAK;YAAEC,GAAG,EAAE;UAAM;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG1HlB,OAAA;UAAKK,SAAS,EAAE,sEACdH,cAAc,CAACK,WAAW,GACtB,0EAA0E,GAC1E,qGAAqG,EACxG;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE,OAAO;YAAEC,UAAU,EAAE,OAAO;YAAEC,SAAS,EAAE,OAAO;YAAEC,IAAI,EAAE,KAAK;YAAEC,GAAG,EAAE;UAAM;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGxHlB,OAAA;UAAKK,SAAS,EAAE,6JACdH,cAAc,CAACK,WAAW,GACtB,uIAAuI,GACvI,kLAAkL,EACrL;UAAAD,QAAA,gBAEDN,OAAA;YAAKK,SAAS,EAAE,6DACdH,cAAc,CAACK,WAAW,GACtB,iDAAiD,GACjD,+GAA+G;UAClH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGVlB,OAAA,CAACH,IAAI;YAACQ,SAAS,EAAE,kFACfH,cAAc,CAACK,WAAW,GAAG,+BAA+B,GAAG,4CAA4C;UAC1G;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGNlB,OAAA;YAAKK,SAAS,EAAC;UAAgE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA;QAAKK,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC5DN,OAAA;UAAIK,SAAS,EAAC,8KAA8K;UAAAC,QAAA,EACzLJ,cAAc,CAACK,WAAW,GAAG,YAAY,GAAG;QAAyB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACLlB,OAAA;UAAGK,SAAS,EAAC,+HAA+H;UAAAC,QAAA,EACzIJ,cAAc,CAACK,WAAW,GACvB,wEAAwE,GACxE;QAAwE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlB,OAAA;MAAKK,SAAS,EAAC,wEAAwE;MAAAC,QAAA,EACpF,CAACJ,cAAc,CAACK,WAAW,gBAC1BP,OAAA;QACEmB,OAAO,EAAEA,CAAA,KAAMhB,gBAAgB,CAAC,YAAY,IAAIiB,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAE;QAClFhB,SAAS,EAAC,6QAA6Q;QAAAC,QAAA,gBAGvRN,OAAA;UAAKK,SAAS,EAAC;QAAyM;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/NlB,OAAA;UAAKK,SAAS,EAAC;QAAgE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtFlB,OAAA;UAAKK,SAAS,EAAC;QAAiF;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGvGlB,OAAA;UAAKK,SAAS,EAAC;QAA4K;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGlMlB,OAAA;UAAKK,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDN,OAAA;YAAKK,SAAS,EAAC,6IAA6I;YAAAC,QAAA,eAC1JN,OAAA,CAACJ,IAAI;cAACS,SAAS,EAAC;YAAiE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACNlB,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAGNlB,OAAA;UAAKK,SAAS,EAAC;QAAyL;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzM,CAAC,gBAETlB,OAAA;QACEmB,OAAO,EAAEf,eAAgB;QACzBC,SAAS,EAAC,4QAA4Q;QAAAC,QAAA,gBAGtRN,OAAA;UAAKK,SAAS,EAAC;QAA8L;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpNlB,OAAA;UAAKK,SAAS,EAAC;QAAgE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtFlB,OAAA;UAAKK,SAAS,EAAC;QAAiF;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGvGlB,OAAA;UAAKK,SAAS,EAAC;QAAwK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG9LlB,OAAA;UAAKK,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDN,OAAA;YAAKK,SAAS,EAAC,2IAA2I;YAAAC,QAAA,eACxJN,OAAA,CAACF,MAAM;cAACO,SAAS,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACNlB,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eAGNlB,OAAA;UAAKK,SAAS,EAAC;QAAyL;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzM;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLhB,cAAc,CAACK,WAAW,iBACzBP,OAAA;MAAKK,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC9FN,OAAA;QAAKK,SAAS,EAAC;MAA+C;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrElB,OAAA;QAAMK,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAU;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACI,EAAA,GA5HWrB,cAA6C;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}