{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingPanel.tsx\";\nimport React from 'react';\nimport { Plus, Mic2, Square } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RecordingPanel = ({\n  recordingState,\n  onStartRecording,\n  onStopRecording\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col justify-center items-center space-y-16 p-8 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400/20 rounded-full animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400/30 rounded-full animate-ping\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-indigo-400/25 rounded-full animate-bounce\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative group\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute inset-0 rounded-full transition-all duration-2000 ease-out ${recordingState.isRecording ? 'bg-gradient-to-br from-red-500/15 via-pink-500/10 to-red-600/5 animate-pulse scale-150' : 'bg-gradient-to-br from-blue-500/8 via-indigo-500/6 to-purple-600/4 scale-125 group-hover:scale-150'}`,\n        style: {\n          width: '280px',\n          height: '280px',\n          marginLeft: '-140px',\n          marginTop: '-140px',\n          left: '50%',\n          top: '50%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute inset-0 rounded-full transition-all duration-1500 ease-out ${recordingState.isRecording ? 'bg-gradient-to-br from-red-400/20 via-pink-400/15 to-red-500/10 scale-130' : 'bg-gradient-to-br from-blue-400/12 via-indigo-400/10 to-purple-500/8 scale-115 group-hover:scale-130'}`,\n        style: {\n          width: '220px',\n          height: '220px',\n          marginLeft: '-110px',\n          marginTop: '-110px',\n          left: '50%',\n          top: '50%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `relative w-40 h-40 sm:w-44 sm:h-44 rounded-full flex items-center justify-center transition-all duration-1000 ease-out transform backdrop-blur-3xl border ${recordingState.isRecording ? 'bg-gradient-to-br from-red-500/30 via-pink-500/25 to-red-600/20 border-red-300/40 shadow-2xl shadow-red-500/40 scale-110' : 'bg-gradient-to-br from-slate-800/30 via-slate-700/25 to-slate-600/20 border-slate-300/30 shadow-2xl shadow-blue-500/20 scale-100 hover:scale-110 group-hover:border-blue-300/50'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `absolute inset-3 rounded-full transition-all duration-700 backdrop-blur-xl ${recordingState.isRecording ? 'bg-gradient-to-br from-red-400/25 via-pink-400/20 to-red-500/15' : 'bg-gradient-to-br from-slate-400/15 via-slate-500/12 to-slate-600/10 group-hover:from-blue-400/20 group-hover:to-indigo-500/15'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `absolute inset-6 rounded-full transition-all duration-500 backdrop-blur-lg ${recordingState.isRecording ? 'bg-gradient-to-br from-red-300/20 to-pink-400/15' : 'bg-gradient-to-br from-slate-300/10 to-slate-400/8 group-hover:from-blue-300/15 group-hover:to-indigo-400/12'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `relative w-20 h-20 rounded-full flex items-center justify-center transition-all duration-500 ${recordingState.isRecording ? 'bg-gradient-to-br from-red-200/20 to-red-400/15 shadow-inner' : 'bg-gradient-to-br from-slate-200/15 to-slate-400/12 shadow-inner group-hover:from-blue-200/20 group-hover:to-blue-400/15'}`,\n          children: /*#__PURE__*/_jsxDEV(Mic2, {\n            className: `h-10 w-10 sm:h-12 sm:w-12 transition-all duration-500 ${recordingState.isRecording ? 'text-red-100 animate-pulse drop-shadow-2xl' : 'text-slate-100 group-hover:text-blue-100 group-hover:scale-110 drop-shadow-xl'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-6 left-8 w-8 h-8 bg-white/15 rounded-full blur-md\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-4 left-6 w-4 h-4 bg-white/25 rounded-full blur-sm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-8 right-6 w-6 h-6 bg-white/10 rounded-full blur-lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-6 max-w-2xl mx-auto relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-3xl sm:text-4xl lg:text-5xl font-black transition-all duration-700 leading-tight tracking-tight ${recordingState.isRecording ? 'bg-gradient-to-r from-red-200 via-pink-100 to-red-200 bg-clip-text text-transparent' : 'bg-gradient-to-r from-slate-100 via-blue-50 to-indigo-100 bg-clip-text text-transparent'}`,\n          children: recordingState.isRecording ? 'Įrašoma pokalbis' : 'Pradėkite naują pokalbį'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `h-1 w-24 mx-auto rounded-full transition-all duration-700 ${recordingState.isRecording ? 'bg-gradient-to-r from-red-400 to-pink-400' : 'bg-gradient-to-r from-blue-400 to-indigo-400'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-lg sm:text-xl text-slate-300/90 max-w-lg mx-auto leading-relaxed font-medium tracking-wide\",\n        children: recordingState.isRecording ? 'Jūsų pokalbis įrašomas su pažangia AI transkribavimo technologija' : 'Profesionalus garso įrašymas su automatine transkribavimo technologija'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row gap-6 animate-fade-in-up animation-delay-200\",\n      children: !recordingState.isRecording ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`),\n        className: \"group relative inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-blue-500/30\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-br from-slate-800/90 via-slate-700/85 to-slate-600/80 transition-all duration-500 group-hover:from-blue-600/90 group-hover:via-blue-700/85 group-hover:to-indigo-700/80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-[1px] bg-gradient-to-br from-white/10 to-transparent rounded-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-2xl border-2 border-slate-400/30 transition-all duration-500 group-hover:border-blue-400/50 group-hover:shadow-2xl group-hover:shadow-blue-500/25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 rounded-full bg-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\",\n            children: /*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 transition-transform duration-300 group-hover:rotate-90\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tracking-wide\",\n            children: \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onStopRecording,\n        className: \"group relative inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-red-500/30\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-br from-red-600/90 via-red-700/85 to-red-800/80 transition-all duration-500 group-hover:from-red-700/90 group-hover:via-red-800/85 group-hover:to-red-900/80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-[1px] bg-gradient-to-br from-white/10 to-transparent rounded-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-2xl border-2 border-red-400/40 transition-all duration-500 group-hover:border-red-300/60 group-hover:shadow-2xl group-hover:shadow-red-500/30\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 rounded-sm bg-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\",\n            children: /*#__PURE__*/_jsxDEV(Square, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tracking-wide\",\n            children: \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), recordingState.isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center space-x-4 animate-fade-in-up animation-delay-400\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-red-500/20 via-red-600/15 to-red-700/10 backdrop-blur-xl rounded-full border border-red-400/30 shadow-lg shadow-red-500/20\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-4 h-4 bg-gradient-to-br from-red-400 to-red-600 rounded-full animate-pulse shadow-lg shadow-red-500/50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 w-4 h-4 bg-red-400 rounded-full animate-ping opacity-30\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg font-semibold bg-gradient-to-r from-red-300 to-red-100 bg-clip-text text-transparent tracking-wide\",\n          children: \"\\u012Era\\u0161oma...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = RecordingPanel;\nvar _c;\n$RefreshReg$(_c, \"RecordingPanel\");", "map": {"version": 3, "names": ["React", "Plus", "Mic2", "Square", "jsxDEV", "_jsxDEV", "RecordingPanel", "recordingState", "onStartRecording", "onStopRecording", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isRecording", "style", "width", "height", "marginLeft", "marginTop", "left", "top", "onClick", "Date", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPanel.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { Plus, Mic2, Square, Pause, Play, AlertCircle } from 'lucide-react';\r\nimport { RecordingButton } from './RecordingButton';\r\nimport { RecordingIndicator } from './RecordingIndicator';\r\nimport { Meeting, RecordingState } from '../types/meeting';\r\n\r\ninterface RecordingPanelProps {\r\n  recordingState: RecordingState;\r\n  currentMeeting: Meeting | null;\r\n  onStartRecording: (title: string) => Promise<void>;\r\n  onStopRecording: () => Promise<void>;\r\n  onPauseRecording: () => void;\r\n  onResumeRecording: () => void;\r\n}\r\n\r\nexport const RecordingPanel: React.FC<RecordingPanelProps> = ({\r\n  recordingState,\r\n  onStartRecording,\r\n  onStopRecording,\r\n}) => {\r\n  return (\r\n    <div className=\"flex-1 flex flex-col justify-center items-center space-y-16 p-8 relative overflow-hidden\">\r\n      {/* Floating Particles Background */}\r\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n        <div className=\"absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400/20 rounded-full animate-pulse\"></div>\r\n        <div className=\"absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400/30 rounded-full animate-ping\"></div>\r\n        <div className=\"absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-indigo-400/25 rounded-full animate-bounce\"></div>\r\n      </div>\r\n\r\n      {/* Ultra-Modern Recording Orb */}\r\n      <div className=\"relative group\">\r\n        {/* Outer Glow Rings */}\r\n        <div className={`absolute inset-0 rounded-full transition-all duration-2000 ease-out ${\r\n          recordingState.isRecording\r\n            ? 'bg-gradient-to-br from-red-500/15 via-pink-500/10 to-red-600/5 animate-pulse scale-150'\r\n            : 'bg-gradient-to-br from-blue-500/8 via-indigo-500/6 to-purple-600/4 scale-125 group-hover:scale-150'\r\n        }`} style={{ width: '280px', height: '280px', marginLeft: '-140px', marginTop: '-140px', left: '50%', top: '50%' }}></div>\r\n\r\n        <div className={`absolute inset-0 rounded-full transition-all duration-1500 ease-out ${\r\n          recordingState.isRecording\r\n            ? 'bg-gradient-to-br from-red-400/20 via-pink-400/15 to-red-500/10 scale-130'\r\n            : 'bg-gradient-to-br from-blue-400/12 via-indigo-400/10 to-purple-500/8 scale-115 group-hover:scale-130'\r\n        }`} style={{ width: '220px', height: '220px', marginLeft: '-110px', marginTop: '-110px', left: '50%', top: '50%' }}></div>\r\n\r\n        {/* Main Orb with Advanced Glassmorphism */}\r\n        <div className={`relative w-40 h-40 sm:w-44 sm:h-44 rounded-full flex items-center justify-center transition-all duration-1000 ease-out transform backdrop-blur-3xl border ${\r\n          recordingState.isRecording\r\n            ? 'bg-gradient-to-br from-red-500/30 via-pink-500/25 to-red-600/20 border-red-300/40 shadow-2xl shadow-red-500/40 scale-110'\r\n            : 'bg-gradient-to-br from-slate-800/30 via-slate-700/25 to-slate-600/20 border-slate-300/30 shadow-2xl shadow-blue-500/20 scale-100 hover:scale-110 group-hover:border-blue-300/50'\r\n        }`}>\r\n          {/* Multiple Inner Glass Layers */}\r\n          <div className={`absolute inset-3 rounded-full transition-all duration-700 backdrop-blur-xl ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-br from-red-400/25 via-pink-400/20 to-red-500/15'\r\n              : 'bg-gradient-to-br from-slate-400/15 via-slate-500/12 to-slate-600/10 group-hover:from-blue-400/20 group-hover:to-indigo-500/15'\r\n          }`}></div>\r\n\r\n          <div className={`absolute inset-6 rounded-full transition-all duration-500 backdrop-blur-lg ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-br from-red-300/20 to-pink-400/15'\r\n              : 'bg-gradient-to-br from-slate-300/10 to-slate-400/8 group-hover:from-blue-300/15 group-hover:to-indigo-400/12'\r\n          }`}></div>\r\n\r\n          {/* Neumorphic Microphone Container */}\r\n          <div className={`relative w-20 h-20 rounded-full flex items-center justify-center transition-all duration-500 ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-br from-red-200/20 to-red-400/15 shadow-inner'\r\n              : 'bg-gradient-to-br from-slate-200/15 to-slate-400/12 shadow-inner group-hover:from-blue-200/20 group-hover:to-blue-400/15'\r\n          }`}>\r\n            <Mic2 className={`h-10 w-10 sm:h-12 sm:w-12 transition-all duration-500 ${\r\n              recordingState.isRecording\r\n                ? 'text-red-100 animate-pulse drop-shadow-2xl'\r\n                : 'text-slate-100 group-hover:text-blue-100 group-hover:scale-110 drop-shadow-xl'\r\n            }`} />\r\n          </div>\r\n\r\n          {/* Sophisticated Light Reflections */}\r\n          <div className=\"absolute top-6 left-8 w-8 h-8 bg-white/15 rounded-full blur-md\"></div>\r\n          <div className=\"absolute top-4 left-6 w-4 h-4 bg-white/25 rounded-full blur-sm\"></div>\r\n          <div className=\"absolute bottom-8 right-6 w-6 h-6 bg-white/10 rounded-full blur-lg\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Ultra-Modern Typography */}\r\n      <div className=\"text-center space-y-6 max-w-2xl mx-auto relative z-10\">\r\n        <div className=\"space-y-4\">\r\n          <h1 className={`text-3xl sm:text-4xl lg:text-5xl font-black transition-all duration-700 leading-tight tracking-tight ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-r from-red-200 via-pink-100 to-red-200 bg-clip-text text-transparent'\r\n              : 'bg-gradient-to-r from-slate-100 via-blue-50 to-indigo-100 bg-clip-text text-transparent'\r\n          }`}>\r\n            {recordingState.isRecording ? 'Įrašoma pokalbis' : 'Pradėkite naują pokalbį'}\r\n          </h1>\r\n\r\n          <div className={`h-1 w-24 mx-auto rounded-full transition-all duration-700 ${\r\n            recordingState.isRecording\r\n              ? 'bg-gradient-to-r from-red-400 to-pink-400'\r\n              : 'bg-gradient-to-r from-blue-400 to-indigo-400'\r\n          }`}></div>\r\n        </div>\r\n\r\n        <p className=\"text-lg sm:text-xl text-slate-300/90 max-w-lg mx-auto leading-relaxed font-medium tracking-wide\">\r\n          {recordingState.isRecording\r\n            ? 'Jūsų pokalbis įrašomas su pažangia AI transkribavimo technologija'\r\n            : 'Profesionalus garso įrašymas su automatine transkribavimo technologija'\r\n          }\r\n        </p>\r\n      </div>\r\n\r\n      {/* Sophisticated Recording Controls */}\r\n      <div className=\"flex flex-col sm:flex-row gap-6 animate-fade-in-up animation-delay-200\">\r\n        {!recordingState.isRecording ? (\r\n          <button\r\n            onClick={() => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}\r\n            className=\"group relative inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-blue-500/30\"\r\n          >\r\n            {/* Sophisticated Background Layers */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-slate-800/90 via-slate-700/85 to-slate-600/80 transition-all duration-500 group-hover:from-blue-600/90 group-hover:via-blue-700/85 group-hover:to-indigo-700/80\"></div>\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"></div>\r\n            <div className=\"absolute inset-[1px] bg-gradient-to-br from-white/10 to-transparent rounded-2xl\"></div>\r\n\r\n            {/* Animated Border */}\r\n            <div className=\"absolute inset-0 rounded-2xl border-2 border-slate-400/30 transition-all duration-500 group-hover:border-blue-400/50 group-hover:shadow-2xl group-hover:shadow-blue-500/25\"></div>\r\n\r\n            {/* Content */}\r\n            <div className=\"relative z-10 flex items-center space-x-3\">\r\n              <div className=\"w-6 h-6 rounded-full bg-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\">\r\n                <Plus className=\"h-4 w-4 transition-transform duration-300 group-hover:rotate-90\" />\r\n              </div>\r\n              <span className=\"tracking-wide\">Pradėti įrašymą</span>\r\n            </div>\r\n\r\n            {/* Subtle Shine Effect */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out\"></div>\r\n          </button>\r\n        ) : (\r\n          <button\r\n            onClick={onStopRecording}\r\n            className=\"group relative inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-red-500/30\"\r\n          >\r\n            {/* Sophisticated Background Layers */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-red-600/90 via-red-700/85 to-red-800/80 transition-all duration-500 group-hover:from-red-700/90 group-hover:via-red-800/85 group-hover:to-red-900/80\"></div>\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"></div>\r\n            <div className=\"absolute inset-[1px] bg-gradient-to-br from-white/10 to-transparent rounded-2xl\"></div>\r\n\r\n            {/* Animated Border */}\r\n            <div className=\"absolute inset-0 rounded-2xl border-2 border-red-400/40 transition-all duration-500 group-hover:border-red-300/60 group-hover:shadow-2xl group-hover:shadow-red-500/30\"></div>\r\n\r\n            {/* Content */}\r\n            <div className=\"relative z-10 flex items-center space-x-3\">\r\n              <div className=\"w-6 h-6 rounded-sm bg-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\">\r\n                <Square className=\"h-4 w-4\" />\r\n              </div>\r\n              <span className=\"tracking-wide\">Sustabdyti įrašymą</span>\r\n            </div>\r\n\r\n            {/* Subtle Shine Effect */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-out\"></div>\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Elegant Recording Indicator */}\r\n      {recordingState.isRecording && (\r\n        <div className=\"flex items-center justify-center space-x-4 animate-fade-in-up animation-delay-400\">\r\n          <div className=\"relative flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-red-500/20 via-red-600/15 to-red-700/10 backdrop-blur-xl rounded-full border border-red-400/30 shadow-lg shadow-red-500/20\">\r\n            {/* Sophisticated Recording Dot */}\r\n            <div className=\"relative\">\r\n              <div className=\"w-4 h-4 bg-gradient-to-br from-red-400 to-red-600 rounded-full animate-pulse shadow-lg shadow-red-500/50\"></div>\r\n              <div className=\"absolute inset-0 w-4 h-4 bg-red-400 rounded-full animate-ping opacity-30\"></div>\r\n            </div>\r\n\r\n            {/* Enhanced Text */}\r\n            <span className=\"text-lg font-semibold bg-gradient-to-r from-red-300 to-red-100 bg-clip-text text-transparent tracking-wide\">\r\n              Įrašoma...\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};"], "mappings": ";AAAA,OAAOA,KAAK,MAAiC,OAAO;AACpD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAkC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc5E,OAAO,MAAMC,cAA6C,GAAGA,CAAC;EAC5DC,cAAc;EACdC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EACJ,oBACEJ,OAAA;IAAKK,SAAS,EAAC,0FAA0F;IAAAC,QAAA,gBAEvGN,OAAA;MAAKK,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEN,OAAA;QAAKK,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnGV,OAAA;QAAKK,SAAS,EAAC;MAA+E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrGV,OAAA;QAAKK,SAAS,EAAC;MAAoF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CAAC,eAGNV,OAAA;MAAKK,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE7BN,OAAA;QAAKK,SAAS,EAAE,uEACdH,cAAc,CAACS,WAAW,GACtB,wFAAwF,GACxF,oGAAoG,EACvG;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,MAAM,EAAE,OAAO;UAAEC,UAAU,EAAE,QAAQ;UAAEC,SAAS,EAAE,QAAQ;UAAEC,IAAI,EAAE,KAAK;UAAEC,GAAG,EAAE;QAAM;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE1HV,OAAA;QAAKK,SAAS,EAAE,uEACdH,cAAc,CAACS,WAAW,GACtB,2EAA2E,GAC3E,sGAAsG,EACzG;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,MAAM,EAAE,OAAO;UAAEC,UAAU,EAAE,QAAQ;UAAEC,SAAS,EAAE,QAAQ;UAAEC,IAAI,EAAE,KAAK;UAAEC,GAAG,EAAE;QAAM;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG1HV,OAAA;QAAKK,SAAS,EAAE,6JACdH,cAAc,CAACS,WAAW,GACtB,0HAA0H,GAC1H,iLAAiL,EACpL;QAAAL,QAAA,gBAEDN,OAAA;UAAKK,SAAS,EAAE,8EACdH,cAAc,CAACS,WAAW,GACtB,iEAAiE,GACjE,gIAAgI;QACnI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEVV,OAAA;UAAKK,SAAS,EAAE,8EACdH,cAAc,CAACS,WAAW,GACtB,kDAAkD,GAClD,8GAA8G;QACjH;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGVV,OAAA;UAAKK,SAAS,EAAE,gGACdH,cAAc,CAACS,WAAW,GACtB,8DAA8D,GAC9D,0HAA0H,EAC7H;UAAAL,QAAA,eACDN,OAAA,CAACH,IAAI;YAACQ,SAAS,EAAE,yDACfH,cAAc,CAACS,WAAW,GACtB,4CAA4C,GAC5C,+EAA+E;UAClF;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtFV,OAAA;UAAKK,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtFV,OAAA;UAAKK,SAAS,EAAC;QAAoE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNV,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAAC,QAAA,gBACpEN,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBN,OAAA;UAAIK,SAAS,EAAE,wGACbH,cAAc,CAACS,WAAW,GACtB,qFAAqF,GACrF,yFAAyF,EAC5F;UAAAL,QAAA,EACAJ,cAAc,CAACS,WAAW,GAAG,kBAAkB,GAAG;QAAyB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAELV,OAAA;UAAKK,SAAS,EAAE,6DACdH,cAAc,CAACS,WAAW,GACtB,2CAA2C,GAC3C,8CAA8C;QACjD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAENV,OAAA;QAAGK,SAAS,EAAC,iGAAiG;QAAAC,QAAA,EAC3GJ,cAAc,CAACS,WAAW,GACvB,mEAAmE,GACnE;MAAwE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE3E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNV,OAAA;MAAKK,SAAS,EAAC,wEAAwE;MAAAC,QAAA,EACpF,CAACJ,cAAc,CAACS,WAAW,gBAC1BX,OAAA;QACEmB,OAAO,EAAEA,CAAA,KAAMhB,gBAAgB,CAAC,YAAY,IAAIiB,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAE;QAClFhB,SAAS,EAAC,6QAA6Q;QAAAC,QAAA,gBAGvRN,OAAA;UAAKK,SAAS,EAAC;QAAyM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/NV,OAAA;UAAKK,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtFV,OAAA;UAAKK,SAAS,EAAC;QAAiF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGvGV,OAAA;UAAKK,SAAS,EAAC;QAA4K;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGlMV,OAAA;UAAKK,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDN,OAAA;YAAKK,SAAS,EAAC,6IAA6I;YAAAC,QAAA,eAC1JN,OAAA,CAACJ,IAAI;cAACS,SAAS,EAAC;YAAiE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACNV,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC;QAAyL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzM,CAAC,gBAETV,OAAA;QACEmB,OAAO,EAAEf,eAAgB;QACzBC,SAAS,EAAC,4QAA4Q;QAAAC,QAAA,gBAGtRN,OAAA;UAAKK,SAAS,EAAC;QAA8L;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpNV,OAAA;UAAKK,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtFV,OAAA;UAAKK,SAAS,EAAC;QAAiF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGvGV,OAAA;UAAKK,SAAS,EAAC;QAAwK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG9LV,OAAA;UAAKK,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDN,OAAA;YAAKK,SAAS,EAAC,2IAA2I;YAAAC,QAAA,eACxJN,OAAA,CAACF,MAAM;cAACO,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACNV,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC;QAAyL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzM;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLR,cAAc,CAACS,WAAW,iBACzBX,OAAA;MAAKK,SAAS,EAAC,mFAAmF;MAAAC,QAAA,eAChGN,OAAA;QAAKK,SAAS,EAAC,iMAAiM;QAAAC,QAAA,gBAE9MN,OAAA;UAAKK,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBN,OAAA;YAAKK,SAAS,EAAC;UAA0G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChIV,OAAA;YAAKK,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eAGNV,OAAA;UAAMK,SAAS,EAAC,4GAA4G;UAAAC,QAAA,EAAC;QAE7H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACY,EAAA,GAtKWrB,cAA6C;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}