{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\ResultsPage.tsx\";\nimport React from 'react';\nimport { Headphones, FileText, Download, Share2, Search, Filter } from 'lucide-react';\nimport { ProfessionalTranscriptViewer } from './index';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultsPage = ({\n  meetings,\n  onDeleteMeeting,\n  onGoToTranscription\n}) => {\n  const completedMeetings = meetings.filter(m => m.transcriptionStatus.state === 'completed');\n  const totalWords = completedMeetings.reduce((sum, m) => {\n    var _m$metadata;\n    return sum + (((_m$metadata = m.metadata) === null || _m$metadata === void 0 ? void 0 : _m$metadata.totalWords) || 0);\n  }, 0);\n  const totalDuration = completedMeetings.reduce((sum, m) => sum + (m.duration || 0), 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",\n          children: /*#__PURE__*/_jsxDEV(Headphones, {\n            className: \"h-6 w-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-white\",\n            children: \"Rezultatai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-white/60\",\n            children: \"Per\\u017Ei\\u016Br\\u0117kite ir redaguokite transkribavimo rezultatus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(FileText, {\n                className: \"h-4 w-4 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-semibold\",\n                children: \"Transkriptai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: completedMeetings.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Headphones, {\n                className: \"h-4 w-4 text-green-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-semibold\",\n                children: \"\\u017Dod\\u017Eiai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: totalWords.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(FileText, {\n                className: \"h-4 w-4 text-purple-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-semibold\",\n                children: \"Trukm\\u0117\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: [Math.floor(totalDuration / 60), \"min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Share2, {\n                className: \"h-4 w-4 text-orange-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-semibold\",\n                children: \"Bendrinti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"12 kart\\u0173\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Ie\\u0161koti transkript\\u0173...\",\n            className: \"w-full pl-10 pr-4 py-2 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl transition-all duration-200 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"h-4 w-4 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white text-sm\",\n            children: \"Filtruoti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: /*#__PURE__*/_jsxDEV(ProfessionalTranscriptViewer, {\n        meetings: meetings,\n        onDeleteMeeting: onDeleteMeeting,\n        onGoToTranscription: onGoToTranscription\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-white mb-4\",\n        children: \"Greiti veiksmai\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onGoToTranscription,\n          className: \"bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-400/20 rounded-xl p-4 hover:from-purple-500/30 hover:to-purple-600/30 transition-all duration-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"h-6 w-6 text-purple-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-medium\",\n                children: \"Naujas transkribavimas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"Prad\\u0117ti nauj\\u0105 transkribavim\\u0105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-400/20 rounded-xl p-4 hover:from-blue-500/30 hover:to-blue-600/30 transition-all duration-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-6 w-6 text-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-medium\",\n                children: \"Eksportuoti visus\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"Atsisi\\u0173sti visus transkriptus\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-400/20 rounded-xl p-4 hover:from-green-500/30 hover:to-green-600/30 transition-all duration-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(Share2, {\n              className: \"h-6 w-6 text-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-medium\",\n                children: \"Bendrinti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"Dalintis su komanda\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_c = ResultsPage;\nexport default ResultsPage;\nvar _c;\n$RefreshReg$(_c, \"ResultsPage\");", "map": {"version": 3, "names": ["React", "Headphones", "FileText", "Download", "Share2", "Search", "Filter", "ProfessionalTranscriptViewer", "jsxDEV", "_jsxDEV", "ResultsPage", "meetings", "onDeleteMeeting", "onGoToTranscription", "completedMeetings", "filter", "m", "transcriptionStatus", "state", "totalWords", "reduce", "sum", "_m$metadata", "metadata", "totalDuration", "duration", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "toLocaleString", "Math", "floor", "type", "placeholder", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ResultsPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Headphones, FileText, Download, Share2, Search, Filter } from 'lucide-react';\r\nimport { ProfessionalTranscriptViewer } from './index';\r\nimport { Meeting } from '../types/meeting';\r\n\r\ninterface ResultsPageProps {\r\n  meetings: Meeting[];\r\n  onDeleteMeeting: (meetingId: string) => void;\r\n  onGoToTranscription: () => void;\r\n}\r\n\r\nconst ResultsPage: React.FC<ResultsPageProps> = ({\r\n  meetings,\r\n  onDeleteMeeting,\r\n  onGoToTranscription\r\n}) => {\r\n  const completedMeetings = meetings.filter(m => \r\n    m.transcriptionStatus.state === 'completed'\r\n  );\r\n  \r\n  const totalWords = completedMeetings.reduce((sum, m) => \r\n    sum + (m.metadata?.totalWords || 0), 0\r\n  );\r\n  \r\n  const totalDuration = completedMeetings.reduce((sum, m) => \r\n    sum + (m.duration || 0), 0\r\n  );\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header Section */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center gap-4 mb-4\">\r\n          <div className=\"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\r\n            <Headphones className=\"h-6 w-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-white\">Rezultatai</h2>\r\n            <p className=\"text-sm text-white/60\">Peržiūrėkite ir redaguokite transkribavimo rezultatus</p>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Stats Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n          <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center\">\r\n                <FileText className=\"h-4 w-4 text-blue-400\" />\r\n              </div>\r\n              <div>\r\n                <div className=\"text-white font-semibold\">Transkriptai</div>\r\n                <div className=\"text-white/60 text-sm\">{completedMeetings.length}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center\">\r\n                <Headphones className=\"h-4 w-4 text-green-400\" />\r\n              </div>\r\n              <div>\r\n                <div className=\"text-white font-semibold\">Žodžiai</div>\r\n                <div className=\"text-white/60 text-sm\">{totalWords.toLocaleString()}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center\">\r\n                <FileText className=\"h-4 w-4 text-purple-400\" />\r\n              </div>\r\n              <div>\r\n                <div className=\"text-white font-semibold\">Trukmė</div>\r\n                <div className=\"text-white/60 text-sm\">{Math.floor(totalDuration / 60)}min</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center\">\r\n                <Share2 className=\"h-4 w-4 text-orange-400\" />\r\n              </div>\r\n              <div>\r\n                <div className=\"text-white font-semibold\">Bendrinti</div>\r\n                <div className=\"text-white/60 text-sm\">12 kartų</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Search and Filter */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center gap-4 mb-4\">\r\n          <div className=\"flex-1 relative\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60\" />\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Ieškoti transkriptų...\"\r\n              className=\"w-full pl-10 pr-4 py-2 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n            />\r\n          </div>\r\n          <button className=\"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl transition-all duration-200 flex items-center gap-2\">\r\n            <Filter className=\"h-4 w-4 text-white\" />\r\n            <span className=\"text-white text-sm\">Filtruoti</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Transcript Viewer */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <ProfessionalTranscriptViewer\r\n          meetings={meetings}\r\n          onDeleteMeeting={onDeleteMeeting}\r\n          onGoToTranscription={onGoToTranscription}\r\n        />\r\n      </div>\r\n\r\n      {/* Quick Actions */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Greiti veiksmai</h3>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          <button\r\n            onClick={onGoToTranscription}\r\n            className=\"bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-400/20 rounded-xl p-4 hover:from-purple-500/30 hover:to-purple-600/30 transition-all duration-200\"\r\n          >\r\n            <div className=\"flex items-center gap-3\">\r\n              <FileText className=\"h-6 w-6 text-purple-400\" />\r\n              <div className=\"text-left\">\r\n                <div className=\"text-white font-medium\">Naujas transkribavimas</div>\r\n                <div className=\"text-white/60 text-sm\">Pradėti naują transkribavimą</div>\r\n              </div>\r\n            </div>\r\n          </button>\r\n          \r\n          <button className=\"bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-400/20 rounded-xl p-4 hover:from-blue-500/30 hover:to-blue-600/30 transition-all duration-200\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <Download className=\"h-6 w-6 text-blue-400\" />\r\n              <div className=\"text-left\">\r\n                <div className=\"text-white font-medium\">Eksportuoti visus</div>\r\n                <div className=\"text-white/60 text-sm\">Atsisiųsti visus transkriptus</div>\r\n              </div>\r\n            </div>\r\n          </button>\r\n          \r\n          <button className=\"bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-400/20 rounded-xl p-4 hover:from-green-500/30 hover:to-green-600/30 transition-all duration-200\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <Share2 className=\"h-6 w-6 text-green-400\" />\r\n              <div className=\"text-left\">\r\n                <div className=\"text-white font-medium\">Bendrinti</div>\r\n                <div className=\"text-white/60 text-sm\">Dalintis su komanda</div>\r\n              </div>\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ResultsPage; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAQ,cAAc;AACrF,SAASC,4BAA4B,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASvD,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,QAAQ;EACRC,eAAe;EACfC;AACF,CAAC,KAAK;EACJ,MAAMC,iBAAiB,GAAGH,QAAQ,CAACI,MAAM,CAACC,CAAC,IACzCA,CAAC,CAACC,mBAAmB,CAACC,KAAK,KAAK,WAClC,CAAC;EAED,MAAMC,UAAU,GAAGL,iBAAiB,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC;IAAA,IAAAM,WAAA;IAAA,OACjDD,GAAG,IAAI,EAAAC,WAAA,GAAAN,CAAC,CAACO,QAAQ,cAAAD,WAAA,uBAAVA,WAAA,CAAYH,UAAU,KAAI,CAAC,CAAC;EAAA,GAAE,CACvC,CAAC;EAED,MAAMK,aAAa,GAAGV,iBAAiB,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KACpDK,GAAG,IAAIL,CAAC,CAACS,QAAQ,IAAI,CAAC,CAAC,EAAE,CAC3B,CAAC;EAED,oBACEhB,OAAA;IAAKiB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlB,OAAA;MAAKiB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAClFlB,OAAA;QAAKiB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3ClB,OAAA;UAAKiB,SAAS,EAAC,+JAA+J;UAAAC,QAAA,eAC5KlB,OAAA,CAACR,UAAU;YAACyB,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNtB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAIiB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEtB,OAAA;YAAGiB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAqD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKiB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDlB,OAAA;UAAKiB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC/DlB,OAAA;YAAKiB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClB,OAAA;cAAKiB,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eACjFlB,OAAA,CAACP,QAAQ;gBAACwB,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNtB,OAAA;cAAAkB,QAAA,gBACElB,OAAA;gBAAKiB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5DtB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEb,iBAAiB,CAACkB;cAAM;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC/DlB,OAAA;YAAKiB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClB,OAAA;cAAKiB,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClFlB,OAAA,CAACR,UAAU;gBAACyB,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNtB,OAAA;cAAAkB,QAAA,gBACElB,OAAA;gBAAKiB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDtB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAER,UAAU,CAACc,cAAc,CAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC/DlB,OAAA;YAAKiB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClB,OAAA;cAAKiB,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFlB,OAAA,CAACP,QAAQ;gBAACwB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNtB,OAAA;cAAAkB,QAAA,gBACElB,OAAA;gBAAKiB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtDtB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAEO,IAAI,CAACC,KAAK,CAACX,aAAa,GAAG,EAAE,CAAC,EAAC,KAAG;cAAA;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC/DlB,OAAA;YAAKiB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClB,OAAA;cAAKiB,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFlB,OAAA,CAACL,MAAM;gBAACsB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNtB,OAAA;cAAAkB,QAAA,gBACElB,OAAA;gBAAKiB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzDtB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFlB,OAAA;QAAKiB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3ClB,OAAA;UAAKiB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BlB,OAAA,CAACJ,MAAM;YAACqB,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FtB,OAAA;YACE2B,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kCAAwB;YACpCX,SAAS,EAAC;UAAqL;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA;UAAQiB,SAAS,EAAC,+HAA+H;UAAAC,QAAA,gBAC/IlB,OAAA,CAACH,MAAM;YAACoB,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCtB,OAAA;YAAMiB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFlB,OAAA,CAACF,4BAA4B;QAC3BI,QAAQ,EAAEA,QAAS;QACnBC,eAAe,EAAEA,eAAgB;QACjCC,mBAAmB,EAAEA;MAAoB;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAClFlB,OAAA;QAAIiB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EtB,OAAA;QAAKiB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDlB,OAAA;UACE6B,OAAO,EAAEzB,mBAAoB;UAC7Ba,SAAS,EAAC,6KAA6K;UAAAC,QAAA,eAEvLlB,OAAA;YAAKiB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClB,OAAA,CAACP,QAAQ;cAACwB,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDtB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlB,OAAA;gBAAKiB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpEtB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAETtB,OAAA;UAAQiB,SAAS,EAAC,mKAAmK;UAAAC,QAAA,eACnLlB,OAAA;YAAKiB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClB,OAAA,CAACN,QAAQ;cAACuB,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CtB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlB,OAAA;gBAAKiB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DtB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAETtB,OAAA;UAAQiB,SAAS,EAAC,wKAAwK;UAAAC,QAAA,eACxLlB,OAAA;YAAKiB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClB,OAAA,CAACL,MAAM;cAACsB,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CtB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlB,OAAA;gBAAKiB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDtB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACQ,EAAA,GAtJI7B,WAAuC;AAwJ7C,eAAeA,WAAW;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}