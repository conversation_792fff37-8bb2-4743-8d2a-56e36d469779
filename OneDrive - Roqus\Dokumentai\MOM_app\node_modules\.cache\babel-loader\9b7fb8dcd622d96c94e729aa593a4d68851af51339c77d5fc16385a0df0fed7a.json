{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nexport const useAudioRecorder = () => {\n  _s();\n  const [recordingState, setRecordingState] = useState({\n    isRecording: false,\n    isPaused: false,\n    duration: 0,\n    audioLevel: 0\n  });\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const streamRef = useRef(null);\n  const intervalRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  const startRecording = useCallback(async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          sampleRate: 44100\n        }\n      });\n      streamRef.current = stream;\n      audioChunksRef.current = [];\n\n      // Audio level monitoring\n      const audioContext = new AudioContext();\n      const analyser = audioContext.createAnalyser();\n      const source = audioContext.createMediaStreamSource(stream);\n      analyser.fftSize = 256;\n      source.connect(analyser);\n      audioContextRef.current = audioContext;\n      analyserRef.current = analyser;\n      const mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus'\n      });\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n      mediaRecorder.start(1000); // Collect data every second\n\n      setRecordingState(prev => ({\n        ...prev,\n        isRecording: true,\n        duration: 0\n      }));\n\n      // Start duration timer and real-time audio level monitoring\n      intervalRef.current = setInterval(() => {\n        setRecordingState(prev => {\n          const newDuration = prev.duration + 1;\n\n          // Update audio level\n          if (analyserRef.current) {\n            const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\n            analyserRef.current.getByteFrequencyData(dataArray);\n            const average = dataArray.reduce((a, b) => a + b) / dataArray.length;\n            const audioLevel = Math.min(average / 255, 1);\n            return {\n              ...prev,\n              duration: newDuration,\n              audioLevel\n            };\n          }\n          return {\n            ...prev,\n            duration: newDuration\n          };\n        });\n      }, 100); // Update more frequently for smoother animation\n    } catch (error) {\n      console.error('Klaida pradedant įrašymą:', error);\n      throw error;\n    }\n  }, []);\n  const stopRecording = useCallback(() => {\n    return new Promise((resolve, reject) => {\n      if (!mediaRecorderRef.current || !recordingState.isRecording) {\n        reject(new Error('No active recording to stop'));\n        return;\n      }\n      mediaRecorderRef.current.onstop = () => {\n        const audioBlob = new Blob(audioChunksRef.current, {\n          type: 'audio/webm;codecs=opus'\n        });\n        resolve(audioBlob);\n      };\n      try {\n        mediaRecorderRef.current.stop();\n        if (streamRef.current) {\n          streamRef.current.getTracks().forEach(track => track.stop());\n          streamRef.current = null;\n        }\n        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {\n          audioContextRef.current.close();\n          audioContextRef.current = null;\n        }\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n          intervalRef.current = null;\n        }\n        setRecordingState(prev => ({\n          ...prev,\n          isRecording: false,\n          isPaused: false,\n          audioLevel: 0\n        }));\n        mediaRecorderRef.current = null;\n        analyserRef.current = null;\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }, [recordingState.isRecording]);\n  const pauseRecording = useCallback(() => {\n    if (mediaRecorderRef.current && recordingState.isRecording) {\n      mediaRecorderRef.current.pause();\n      setRecordingState(prev => ({\n        ...prev,\n        isPaused: true\n      }));\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    }\n  }, [recordingState.isRecording]);\n  const resumeRecording = useCallback(() => {\n    if (mediaRecorderRef.current && recordingState.isPaused) {\n      mediaRecorderRef.current.resume();\n      setRecordingState(prev => ({\n        ...prev,\n        isPaused: false\n      }));\n\n      // Resume timer\n      intervalRef.current = setInterval(() => {\n        setRecordingState(prev => ({\n          ...prev,\n          duration: prev.duration + 1\n        }));\n      }, 1000);\n    }\n  }, [recordingState.isPaused]);\n  useEffect(() => {\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n        streamRef.current = null;\n      }\n      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n      }\n      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n        mediaRecorderRef.current = null;\n      }\n      analyserRef.current = null;\n      audioChunksRef.current = [];\n    };\n  }, []);\n  return {\n    recordingState,\n    startRecording,\n    stopRecording,\n    pauseRecording,\n    resumeRecording\n  };\n};\n_s(useAudioRecorder, \"k8Dd5JMTSA7p935fLmz7JsSEXtI=\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "useAudioRecorder", "_s", "recordingState", "setRecordingState", "isRecording", "isPaused", "duration", "audioLevel", "mediaRecorderRef", "audioChunksRef", "streamRef", "intervalRef", "audioContextRef", "analyserRef", "startRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "echoCancellation", "noiseSuppression", "sampleRate", "current", "audioContext", "AudioContext", "analyser", "create<PERSON><PERSON>yser", "source", "createMediaStreamSource", "fftSize", "connect", "mediaRecorder", "MediaRecorder", "mimeType", "ondataavailable", "event", "data", "size", "push", "start", "prev", "setInterval", "newDuration", "dataArray", "Uint8Array", "frequencyBinCount", "getByteFrequencyData", "average", "reduce", "a", "b", "length", "Math", "min", "error", "console", "stopRecording", "Promise", "resolve", "reject", "Error", "onstop", "audioBlob", "Blob", "type", "stop", "getTracks", "for<PERSON>ach", "track", "state", "close", "clearInterval", "pauseRecording", "pause", "resumeRecording", "resume"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/hooks/useAudioRecorder.ts"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\nimport { RecordingState } from '../types/meeting';\n\nexport const useAudioRecorder = () => {\n  const [recordingState, setRecordingState] = useState<RecordingState>({\n    isRecording: false,\n    isPaused: false,\n    duration: 0,\n    audioLevel: 0,\n  });\n\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const audioChunksRef = useRef<Blob[]>([]);\n  const streamRef = useRef<MediaStream | null>(null);\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const analyserRef = useRef<AnalyserNode | null>(null);\n\n  const startRecording = useCallback(async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          sampleRate: 44100,\n        } \n      });\n      \n      streamRef.current = stream;\n      audioChunksRef.current = [];\n\n      // Audio level monitoring\n      const audioContext = new AudioContext();\n      const analyser = audioContext.createAnalyser();      const source = audioContext.createMediaStreamSource(stream);\n      \n      analyser.fftSize = 256;\n      source.connect(analyser);\n      \n      audioContextRef.current = audioContext;\n      analyserRef.current = analyser;\n\n      const mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus'\n      });\n      \n      mediaRecorderRef.current = mediaRecorder;\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      mediaRecorder.start(1000); // Collect data every second\n      \n      setRecordingState(prev => ({\n        ...prev,\n        isRecording: true,\n        duration: 0,\n      }));\n\n      // Start duration timer and real-time audio level monitoring\n      intervalRef.current = setInterval(() => {\n        setRecordingState(prev => {\n          const newDuration = prev.duration + 1;\n\n          // Update audio level\n          if (analyserRef.current) {\n            const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\n            analyserRef.current.getByteFrequencyData(dataArray);\n            const average = dataArray.reduce((a, b) => a + b) / dataArray.length;\n            const audioLevel = Math.min(average / 255, 1);\n\n            return {\n              ...prev,\n              duration: newDuration,\n              audioLevel,\n            };\n          }\n\n          return { ...prev, duration: newDuration };\n        });\n      }, 100); // Update more frequently for smoother animation\n\n    } catch (error) {\n      console.error('Klaida pradedant įrašymą:', error);\n      throw error;\n    }\n  }, []);\n\n  const stopRecording = useCallback((): Promise<Blob> => {\n    return new Promise((resolve, reject) => {\n      if (!mediaRecorderRef.current || !recordingState.isRecording) {\n        reject(new Error('No active recording to stop'));\n        return;\n      }\n\n      mediaRecorderRef.current.onstop = () => {\n        const audioBlob = new Blob(audioChunksRef.current, { \n          type: 'audio/webm;codecs=opus' \n        });\n        resolve(audioBlob);\n      };\n\n      try {\n        mediaRecorderRef.current.stop();\n        \n        if (streamRef.current) {\n          streamRef.current.getTracks().forEach(track => track.stop());\n          streamRef.current = null;\n        }\n        \n        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {\n          audioContextRef.current.close();\n          audioContextRef.current = null;\n        }\n\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n          intervalRef.current = null;\n        }\n\n        setRecordingState(prev => ({\n          ...prev,\n          isRecording: false,\n          isPaused: false,\n          audioLevel: 0,\n        }));\n\n        mediaRecorderRef.current = null;\n        analyserRef.current = null;\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }, [recordingState.isRecording]);\n\n  const pauseRecording = useCallback(() => {\n    if (mediaRecorderRef.current && recordingState.isRecording) {\n      mediaRecorderRef.current.pause();\n      setRecordingState(prev => ({ ...prev, isPaused: true }));\n      \n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    }\n  }, [recordingState.isRecording]);\n\n  const resumeRecording = useCallback(() => {\n    if (mediaRecorderRef.current && recordingState.isPaused) {\n      mediaRecorderRef.current.resume();\n      setRecordingState(prev => ({ ...prev, isPaused: false }));\n      \n      // Resume timer\n      intervalRef.current = setInterval(() => {\n        setRecordingState(prev => ({ ...prev, duration: prev.duration + 1 }));\n      }, 1000);\n    }\n  }, [recordingState.isPaused]);\n\n  useEffect(() => {\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n        streamRef.current = null;\n      }\n      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n      }\n      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n        mediaRecorderRef.current = null;\n      }\n      analyserRef.current = null;\n      audioChunksRef.current = [];\n    };\n  }, []);\n\n  return {\n    recordingState,\n    startRecording,\n    stopRecording,\n    pauseRecording,\n    resumeRecording,\n  };\n};"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAGhE,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGP,QAAQ,CAAiB;IACnEQ,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAGX,MAAM,CAAuB,IAAI,CAAC;EAC3D,MAAMY,cAAc,GAAGZ,MAAM,CAAS,EAAE,CAAC;EACzC,MAAMa,SAAS,GAAGb,MAAM,CAAqB,IAAI,CAAC;EAClD,MAAMc,WAAW,GAAGd,MAAM,CAAwB,IAAI,CAAC;EACvD,MAAMe,eAAe,GAAGf,MAAM,CAAsB,IAAI,CAAC;EACzD,MAAMgB,WAAW,GAAGhB,MAAM,CAAsB,IAAI,CAAC;EAErD,MAAMiB,cAAc,GAAGhB,WAAW,CAAC,YAAY;IAC7C,IAAI;MACF,MAAMiB,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,gBAAgB,EAAE,IAAI;UACtBC,gBAAgB,EAAE,IAAI;UACtBC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;MAEFZ,SAAS,CAACa,OAAO,GAAGR,MAAM;MAC1BN,cAAc,CAACc,OAAO,GAAG,EAAE;;MAE3B;MACA,MAAMC,YAAY,GAAG,IAAIC,YAAY,CAAC,CAAC;MACvC,MAAMC,QAAQ,GAAGF,YAAY,CAACG,cAAc,CAAC,CAAC;MAAO,MAAMC,MAAM,GAAGJ,YAAY,CAACK,uBAAuB,CAACd,MAAM,CAAC;MAEhHW,QAAQ,CAACI,OAAO,GAAG,GAAG;MACtBF,MAAM,CAACG,OAAO,CAACL,QAAQ,CAAC;MAExBd,eAAe,CAACW,OAAO,GAAGC,YAAY;MACtCX,WAAW,CAACU,OAAO,GAAGG,QAAQ;MAE9B,MAAMM,aAAa,GAAG,IAAIC,aAAa,CAAClB,MAAM,EAAE;QAC9CmB,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF1B,gBAAgB,CAACe,OAAO,GAAGS,aAAa;MAExCA,aAAa,CAACG,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvB7B,cAAc,CAACc,OAAO,CAACgB,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzC;MACF,CAAC;MAEDL,aAAa,CAACQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE3BrC,iBAAiB,CAACsC,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPrC,WAAW,EAAE,IAAI;QACjBE,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;;MAEH;MACAK,WAAW,CAACY,OAAO,GAAGmB,WAAW,CAAC,MAAM;QACtCvC,iBAAiB,CAACsC,IAAI,IAAI;UACxB,MAAME,WAAW,GAAGF,IAAI,CAACnC,QAAQ,GAAG,CAAC;;UAErC;UACA,IAAIO,WAAW,CAACU,OAAO,EAAE;YACvB,MAAMqB,SAAS,GAAG,IAAIC,UAAU,CAAChC,WAAW,CAACU,OAAO,CAACuB,iBAAiB,CAAC;YACvEjC,WAAW,CAACU,OAAO,CAACwB,oBAAoB,CAACH,SAAS,CAAC;YACnD,MAAMI,OAAO,GAAGJ,SAAS,CAACK,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAGP,SAAS,CAACQ,MAAM;YACpE,MAAM7C,UAAU,GAAG8C,IAAI,CAACC,GAAG,CAACN,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC;YAE7C,OAAO;cACL,GAAGP,IAAI;cACPnC,QAAQ,EAAEqC,WAAW;cACrBpC;YACF,CAAC;UACH;UAEA,OAAO;YAAE,GAAGkC,IAAI;YAAEnC,QAAQ,EAAEqC;UAAY,CAAC;QAC3C,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAEX,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,aAAa,GAAG3D,WAAW,CAAC,MAAqB;IACrD,OAAO,IAAI4D,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI,CAACpD,gBAAgB,CAACe,OAAO,IAAI,CAACrB,cAAc,CAACE,WAAW,EAAE;QAC5DwD,MAAM,CAAC,IAAIC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAChD;MACF;MAEArD,gBAAgB,CAACe,OAAO,CAACuC,MAAM,GAAG,MAAM;QACtC,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACvD,cAAc,CAACc,OAAO,EAAE;UACjD0C,IAAI,EAAE;QACR,CAAC,CAAC;QACFN,OAAO,CAACI,SAAS,CAAC;MACpB,CAAC;MAED,IAAI;QACFvD,gBAAgB,CAACe,OAAO,CAAC2C,IAAI,CAAC,CAAC;QAE/B,IAAIxD,SAAS,CAACa,OAAO,EAAE;UACrBb,SAAS,CAACa,OAAO,CAAC4C,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACH,IAAI,CAAC,CAAC,CAAC;UAC5DxD,SAAS,CAACa,OAAO,GAAG,IAAI;QAC1B;QAEA,IAAIX,eAAe,CAACW,OAAO,IAAIX,eAAe,CAACW,OAAO,CAAC+C,KAAK,KAAK,QAAQ,EAAE;UACzE1D,eAAe,CAACW,OAAO,CAACgD,KAAK,CAAC,CAAC;UAC/B3D,eAAe,CAACW,OAAO,GAAG,IAAI;QAChC;QAEA,IAAIZ,WAAW,CAACY,OAAO,EAAE;UACvBiD,aAAa,CAAC7D,WAAW,CAACY,OAAO,CAAC;UAClCZ,WAAW,CAACY,OAAO,GAAG,IAAI;QAC5B;QAEApB,iBAAiB,CAACsC,IAAI,KAAK;UACzB,GAAGA,IAAI;UACPrC,WAAW,EAAE,KAAK;UAClBC,QAAQ,EAAE,KAAK;UACfE,UAAU,EAAE;QACd,CAAC,CAAC,CAAC;QAEHC,gBAAgB,CAACe,OAAO,GAAG,IAAI;QAC/BV,WAAW,CAACU,OAAO,GAAG,IAAI;MAC5B,CAAC,CAAC,OAAOgC,KAAK,EAAE;QACdK,MAAM,CAACL,KAAK,CAAC;MACf;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrD,cAAc,CAACE,WAAW,CAAC,CAAC;EAEhC,MAAMqE,cAAc,GAAG3E,WAAW,CAAC,MAAM;IACvC,IAAIU,gBAAgB,CAACe,OAAO,IAAIrB,cAAc,CAACE,WAAW,EAAE;MAC1DI,gBAAgB,CAACe,OAAO,CAACmD,KAAK,CAAC,CAAC;MAChCvE,iBAAiB,CAACsC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEpC,QAAQ,EAAE;MAAK,CAAC,CAAC,CAAC;MAExD,IAAIM,WAAW,CAACY,OAAO,EAAE;QACvBiD,aAAa,CAAC7D,WAAW,CAACY,OAAO,CAAC;MACpC;IACF;EACF,CAAC,EAAE,CAACrB,cAAc,CAACE,WAAW,CAAC,CAAC;EAEhC,MAAMuE,eAAe,GAAG7E,WAAW,CAAC,MAAM;IACxC,IAAIU,gBAAgB,CAACe,OAAO,IAAIrB,cAAc,CAACG,QAAQ,EAAE;MACvDG,gBAAgB,CAACe,OAAO,CAACqD,MAAM,CAAC,CAAC;MACjCzE,iBAAiB,CAACsC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEpC,QAAQ,EAAE;MAAM,CAAC,CAAC,CAAC;;MAEzD;MACAM,WAAW,CAACY,OAAO,GAAGmB,WAAW,CAAC,MAAM;QACtCvC,iBAAiB,CAACsC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEnC,QAAQ,EAAEmC,IAAI,CAACnC,QAAQ,GAAG;QAAE,CAAC,CAAC,CAAC;MACvE,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACJ,cAAc,CAACG,QAAQ,CAAC,CAAC;EAE7BN,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIY,WAAW,CAACY,OAAO,EAAE;QACvBiD,aAAa,CAAC7D,WAAW,CAACY,OAAO,CAAC;QAClCZ,WAAW,CAACY,OAAO,GAAG,IAAI;MAC5B;MACA,IAAIb,SAAS,CAACa,OAAO,EAAE;QACrBb,SAAS,CAACa,OAAO,CAAC4C,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACH,IAAI,CAAC,CAAC,CAAC;QAC5DxD,SAAS,CAACa,OAAO,GAAG,IAAI;MAC1B;MACA,IAAIX,eAAe,CAACW,OAAO,IAAIX,eAAe,CAACW,OAAO,CAAC+C,KAAK,KAAK,QAAQ,EAAE;QACzE1D,eAAe,CAACW,OAAO,CAACgD,KAAK,CAAC,CAAC;QAC/B3D,eAAe,CAACW,OAAO,GAAG,IAAI;MAChC;MACA,IAAIf,gBAAgB,CAACe,OAAO,IAAIf,gBAAgB,CAACe,OAAO,CAAC+C,KAAK,KAAK,UAAU,EAAE;QAC7E9D,gBAAgB,CAACe,OAAO,CAAC2C,IAAI,CAAC,CAAC;QAC/B1D,gBAAgB,CAACe,OAAO,GAAG,IAAI;MACjC;MACAV,WAAW,CAACU,OAAO,GAAG,IAAI;MAC1Bd,cAAc,CAACc,OAAO,GAAG,EAAE;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLrB,cAAc;IACdY,cAAc;IACd2C,aAAa;IACbgB,cAAc;IACdE;EACF,CAAC;AACH,CAAC;AAAC1E,EAAA,CA3LWD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}