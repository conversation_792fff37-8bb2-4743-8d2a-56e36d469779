import React, { useEffect, useState } from 'react';
import { Mic, MicOff, Pause, Circle } from 'lucide-react';
import { RecordingState } from '../types/meeting';

interface RecordingIndicatorProps {
  recordingState: RecordingState;
}

export const RecordingIndicator: React.FC<RecordingIndicatorProps> = ({
  recordingState,
}) => {
  const { isRecording, isPaused, duration, audioLevel } = recordingState;
  const [displayTime, setDisplayTime] = useState('00:00');

  useEffect(() => {
    const formatTime = (seconds: number) => {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    setDisplayTime(formatTime(duration));
  }, [duration]);

  if (!isRecording) {
    return (
      <div className="flex items-center space-x-2 text-gray-500 bg-gradient-to-r from-black/40 to-slate-800/40 px-2.5 py-1.5 rounded-full border border-white/20 ring-1 ring-white/10 shadow-md backdrop-blur-md">
        <div className="flex items-center space-x-1.5">
          <MicOff className="h-3.5 w-3.5 text-white/70" />
          <span className="text-xs font-medium text-white/70">Sustabdyta</span>
        </div>
        <div className="text-xs font-mono text-white/60 ml-1">00:00</div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3 bg-gradient-to-r from-black/50 to-blue-900/40 px-3 py-1.5 rounded-full border border-white/25 ring-1 ring-white/15 shadow-lg backdrop-blur-md transition-all duration-300 hover:shadow-blue-500/10">
      {/* Status Indicator */}
      <div className="flex items-center space-x-1.5">
        {isPaused ? (
          <>
            <div className="flex items-center justify-center w-5 h-5 bg-gradient-to-br from-yellow-500/80 to-amber-600/80 rounded-full border border-white/30 ring-1 ring-white/15 shadow-md">
              <Pause className="h-2.5 w-2.5 text-white" />
            </div>
            <span className="text-xs font-medium text-yellow-300">Pristabdyta</span>
          </>
        ) : (
          <>
            <div className="relative flex items-center justify-center w-5 h-5">
              <Circle className="h-3.5 w-3.5 text-red-500 fill-current animate-pulse" />
              <div className="absolute inset-0 rounded-full bg-red-500/30 animate-ping" />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red-500/40 to-red-600/40 blur-sm" />
            </div>
            <span className="text-xs font-medium text-red-300">Įrašoma</span>
          </>
        )}
      </div>

      {/* Time Display */}
      <div className="px-2.5 py-1 bg-gradient-to-r from-black/60 to-blue-900/50 rounded-full border border-white/20 ring-1 ring-white/10 shadow-md">
        <span className="text-xs font-mono font-medium text-white/90">{displayTime}</span>
      </div>

      {/* Audio Level Indicator */}
      {!isPaused && (
        <div className="flex items-center space-x-1.5">
          <Mic className="h-3.5 w-3.5 text-white/70" />
          <div className="flex items-end space-x-0.5 h-3">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className={`w-0.5 rounded-full transition-all duration-100 ${
                  audioLevel > (i + 1) * 0.2
                    ? 'bg-blue-400 h-full'
                    : 'bg-white/20 h-0.5'
                }`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};