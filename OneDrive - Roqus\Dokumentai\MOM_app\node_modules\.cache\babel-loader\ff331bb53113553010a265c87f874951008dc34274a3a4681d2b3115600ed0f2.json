{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Mic2, Headphones, Settings, Play, Square, BarChart3 } from 'lucide-react';\nimport Button from './Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RecordingPage = ({\n  recordingState,\n  currentMeeting,\n  onStartRecording,\n  onStopRecording,\n  onPauseRecording,\n  onResumeRecording\n}) => {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: '<PERSON><PERSON><PERSON><PERSON><PERSON> pre<PERSON>',\n    completed: false,\n    date: '2024-01-20',\n    priority: 'high',\n    project: 'Aexn CRM analitika',\n    owner: '<PERSON>',\n    type: 'App',\n    location: 'Apps'\n  }, {\n    id: 2,\n    text: 'Susi<PERSON><PERSON><PERSON> su klientu',\n    completed: true,\n    date: '2024-01-18',\n    priority: 'medium',\n    project: 'Aexn | Analitika',\n    owner: 'Petras Petraitis',\n    type: 'Workspace',\n    location: 'Workspaces'\n  }, {\n    id: 3,\n    text: 'Peržiūrėti dokumentus',\n    completed: false,\n    date: '2024-01-22',\n    priority: 'low',\n    project: 'Aexn_analitika',\n    owner: 'Ona Onaitytė',\n    type: 'Report',\n    location: 'Aexn | Analitika'\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false,\n    priority: 'medium',\n    project: 'Aexn_analitika',\n    owner: 'Jonas Jonaitis',\n    type: 'Semantic model',\n    location: 'Aexn | Analitika'\n  }]);\n  const [recordedConversations, setRecordedConversations] = useState([{\n    id: 1,\n    title: 'Susitikimas su komanda',\n    duration: '15:30',\n    date: '2024-01-15'\n  }, {\n    id: 2,\n    title: 'Klientų aptarimas',\n    duration: '22:15',\n    date: '2024-01-14'\n  }, {\n    id: 3,\n    title: 'Projekto planavimas',\n    duration: '18:45',\n    date: '2024-01-13'\n  }]);\n\n  // TODO editing states\n  const [editingTodo, setEditingTodo] = useState(null);\n  const [editingText, setEditingText] = useState('');\n  const [editingProject, setEditingProject] = useState('');\n  const [editingOwner, setEditingOwner] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState('medium');\n  const [newTodoProject, setNewTodoProject] = useState('');\n  const [newTodoOwner, setNewTodoOwner] = useState('');\n  const [sortType, setSortType] = useState('date_desc');\n  const handleStartRecording = async () => {\n    setIsRecording(true);\n    setRecordingTime(0);\n    // Simulate recording time\n    const interval = setInterval(() => {\n      setRecordingTime(prev => prev + 1);\n    }, 1000);\n\n    // Store interval for cleanup\n    window.recordingInterval = interval;\n  };\n  const handleStopRecording = () => {\n    setIsRecording(false);\n    if (window.recordingInterval) {\n      clearInterval(window.recordingInterval);\n    }\n    // Add new recording to list\n    const newRecording = {\n      id: Date.now(),\n      title: `Pokalbis ${new Date().toLocaleString('lt-LT')}`,\n      duration: `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`,\n      date: new Date().toISOString().split('T')[0]\n    };\n    setRecordedConversations(prev => [newRecording, ...prev]);\n    setRecordingTime(0);\n  };\n  const toggleTodo = id => {\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const deleteTodo = id => {\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n  const startEditingTodo = todo => {\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n    setEditingProject(todo.project || '');\n    setEditingOwner(todo.owner || '');\n  };\n  const saveEditedTodo = () => {\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => todo.id === editingTodo ? {\n        ...todo,\n        text: editingText.trim(),\n        project: editingProject,\n        owner: editingOwner\n      } : todo));\n      setEditingTodo(null);\n      setEditingText('');\n      setEditingProject('');\n      setEditingOwner('');\n    }\n  };\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n    setEditingProject('');\n    setEditingOwner('');\n  };\n  const addNewTodo = () => {\n    if (newTodoText.trim()) {\n      const newTodo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority,\n        project: newTodoProject,\n        owner: newTodoOwner\n      };\n      setTodos(prev => [...prev, newTodo]);\n      setNewTodoText('');\n      setNewTodoDate('');\n      setNewTodoPriority('medium');\n      setNewTodoProject('');\n      setNewTodoOwner('');\n      setShowNewTodoForm(false);\n    }\n  };\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n    setNewTodoProject('');\n    setNewTodoOwner('');\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-400';\n      case 'medium':\n        return 'text-yellow-400';\n      case 'low':\n        return 'text-green-400';\n      default:\n        return 'text-white/60';\n    }\n  };\n  const getPriorityBg = priority => {\n    switch (priority) {\n      case 'high':\n        return 'bg-red-500/20 border-red-500/30';\n      case 'medium':\n        return 'bg-yellow-500/20 border-yellow-500/30';\n      case 'low':\n        return 'bg-green-500/20 border-green-500/30';\n      default:\n        return 'bg-white/5 border-white/10';\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Rūšiavimo funkcija\n  const sortedTodos = [...todos].sort((a, b) => {\n    if (sortType === 'date_desc') {\n      if (!a.date) return 1;\n      if (!b.date) return -1;\n      return b.date.localeCompare(a.date);\n    }\n    if (sortType === 'date_asc') {\n      if (!a.date) return 1;\n      if (!b.date) return -1;\n      return a.date.localeCompare(b.date);\n    }\n    if (sortType === 'alpha_asc') {\n      return a.text.localeCompare(b.text, 'lt');\n    }\n    if (sortType === 'alpha_desc') {\n      return b.text.localeCompare(a.text, 'lt');\n    }\n    return 0;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",\n          children: /*#__PURE__*/_jsxDEV(Mic2, {\n            className: \"h-6 w-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-white\",\n            children: \"Pokalbi\\u0173 \\u012Fra\\u0161ymas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-white/60\",\n            children: \"Profesionalus garso \\u012Fra\\u0161ymas su automatine transkribavimo technologija\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Mic2, {\n            className: \"h-5 w-5 text-blue-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"Pokalbi\\u0173 \\u012Fra\\u0161ymas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-medium\",\n                children: \"\\u012Era\\u0161ymo statusas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-white mb-2\",\n              children: formatTime(recordingTime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/60 text-sm\",\n              children: isRecording ? 'Įrašoma...' : 'Pasiruošta įrašymui'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: !isRecording ? /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleStartRecording,\n              icon: /*#__PURE__*/_jsxDEV(Play, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 25\n              }, this),\n              variant: \"primary\",\n              fullWidth: true,\n              children: \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleStopRecording,\n              icon: /*#__PURE__*/_jsxDEV(Square, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 25\n              }, this),\n              variant: \"primary\",\n              fullWidth: true,\n              style: {\n                background: 'linear-gradient(to right, #ef4444, #ec4899)'\n              },\n              children: \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), currentMeeting && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-medium mb-2\",\n              children: \"Dabartinis pokalbis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/80 text-sm\",\n              children: currentMeeting.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/60 text-xs mt-1\",\n              children: [\"Prad\\u0117tas: \", currentMeeting.date.toLocaleTimeString('lt-LT')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Headphones, {\n            className: \"h-5 w-5 text-purple-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"\\u012Era\\u0161yti pokalbiai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recordedConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-medium text-sm truncate\",\n                children: conversation.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-xs\",\n                children: conversation.duration\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-xs\",\n                children: conversation.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)]\n          }, conversation.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: [\"I\\u0161 viso: \", recordedConversations.length, \" pokalbi\\u0173\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"h-5 w-5 text-orange-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"Analitika\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 rounded-xl p-4 border border-white/10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-white\",\n                children: recordedConversations.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"\\u012Era\\u0161yti pokalbiai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 rounded-xl p-4 border border-white/10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-white\",\n                children: recordedConversations.reduce((total, conv) => {\n                  const [mins, secs] = conv.duration.split(':').map(Number);\n                  return total + mins * 60 + secs;\n                }, 0) / 60 | 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"Minut\\u0117s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"U\\u017Eduo\\u010Di\\u0173 progresas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: [Math.round(todos.filter(t => t.completed).length / todos.length * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${todos.filter(t => t.completed).length / todos.length * 100}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"\\u012Era\\u0161ymo kokyb\\u0117\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: \"95%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-500 h-2 rounded-full\",\n                  style: {\n                    width: '95%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"Transkribavimo tikslumas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: \"98%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-500 h-2 rounded-full\",\n                  style: {\n                    width: '98%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 5\n  }, this);\n};\n_s(RecordingPage, \"CBR8JJl2GBjqYtmPJDLeORvo3qk=\");\n_c = RecordingPage;\nexport default RecordingPage;\nvar _c;\n$RefreshReg$(_c, \"RecordingPage\");", "map": {"version": 3, "names": ["React", "useState", "Mic2", "Headphones", "Settings", "Play", "Square", "BarChart3", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "RecordingPage", "recordingState", "currentMeeting", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "_s", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "date", "priority", "project", "owner", "type", "location", "recordedConversations", "setRecordedConversations", "title", "duration", "editingTodo", "setEditingTodo", "editingText", "setEditingText", "editingProject", "setEditingProject", "<PERSON><PERSON><PERSON><PERSON>", "setEditingOwner", "newTodoText", "setNewTodoText", "showNewTodoForm", "setShowNewTodoForm", "newTodoDate", "setNewTodoDate", "newTodoPriority", "setNewTodoPriority", "newTodoProject", "setNewTodoProject", "newTodoOwner", "setNewTodoOwner", "sortType", "setSortType", "handleStartRecording", "interval", "setInterval", "prev", "window", "recordingInterval", "handleStopRecording", "clearInterval", "newRecording", "Date", "now", "toLocaleString", "Math", "floor", "toString", "padStart", "toISOString", "split", "toggleTodo", "map", "todo", "deleteTodo", "filter", "startEditingTodo", "saveEditedTodo", "trim", "cancelEditing", "addNewTodo", "newTodo", "undefined", "cancelNewTodo", "getPriorityColor", "getPriorityBg", "formatTime", "seconds", "mins", "secs", "sortedTodos", "sort", "a", "b", "localeCompare", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "icon", "variant", "fullWidth", "style", "background", "toLocaleTimeString", "conversation", "length", "reduce", "total", "conv", "Number", "round", "t", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Mic2, Zap, Headphones, Settings, Play, Pause, Square, Plus, List, BarChart3, Edit, Trash2, Calendar, X, Check } from 'lucide-react';\nimport { RecordingPanel } from './index';\nimport { RecordingState, Meeting } from '../types/meeting';\nimport Button from './Button';\n\ninterface Todo {\n  id: number;\n  text: string;\n  completed: boolean;\n  date?: string;\n  priority: 'low' | 'medium' | 'high';\n  project?: string;\n  owner?: string;\n  type?: string;\n  location?: string;\n}\n\ninterface RecordingPageProps {\n  recordingState: RecordingState;\n  currentMeeting: Meeting | null;\n  onStartRecording: (title: string) => Promise<void>;\n  onStopRecording: () => Promise<void>;\n  onPauseRecording: () => void;\n  onResumeRecording: () => void;\n}\n\nconst RecordingPage: React.FC<RecordingPageProps> = ({\n  recordingState,\n  currentMeeting,\n  onStartRecording,\n  onStopRecording,\n  onPauseRecording,\n  onResumeRecording\n}) => {\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [todos, setTodos] = useState<Todo[]>([\n    { id: 1, text: 'Pasiruošti prezentacijai', completed: false, date: '2024-01-20', priority: 'high', project: 'Aexn CRM analitika', owner: 'Jonas Jonaitis', type: 'App', location: 'Apps' },\n    { id: 2, text: 'Susitikimas su klientu', completed: true, date: '2024-01-18', priority: 'medium', project: 'Aexn | Analitika', owner: 'Petras Petraitis', type: 'Workspace', location: 'Workspaces' },\n    { id: 3, text: 'Peržiūrėti dokumentus', completed: false, date: '2024-01-22', priority: 'low', project: 'Aexn_analitika', owner: 'Ona Onaitytė', type: 'Report', location: 'Aexn | Analitika' },\n    { id: 4, text: 'Planuoti kitą savaitę', completed: false, priority: 'medium', project: 'Aexn_analitika', owner: 'Jonas Jonaitis', type: 'Semantic model', location: 'Aexn | Analitika' }\n  ]);\n  const [recordedConversations, setRecordedConversations] = useState([\n    { id: 1, title: 'Susitikimas su komanda', duration: '15:30', date: '2024-01-15' },\n    { id: 2, title: 'Klientų aptarimas', duration: '22:15', date: '2024-01-14' },\n    { id: 3, title: 'Projekto planavimas', duration: '18:45', date: '2024-01-13' }\n  ]);\n\n  // TODO editing states\n  const [editingTodo, setEditingTodo] = useState<number | null>(null);\n  const [editingText, setEditingText] = useState('');\n  const [editingProject, setEditingProject] = useState('');\n  const [editingOwner, setEditingOwner] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');\n  const [newTodoProject, setNewTodoProject] = useState('');\n  const [newTodoOwner, setNewTodoOwner] = useState('');\n  const [sortType, setSortType] = useState<'date_desc' | 'date_asc' | 'alpha_asc' | 'alpha_desc'>('date_desc');\n\n  const handleStartRecording = async () => {\n    setIsRecording(true);\n    setRecordingTime(0);\n    // Simulate recording time\n    const interval = setInterval(() => {\n      setRecordingTime(prev => prev + 1);\n    }, 1000);\n    \n    // Store interval for cleanup\n    (window as any).recordingInterval = interval;\n  };\n\n  const handleStopRecording = () => {\n    setIsRecording(false);\n    if ((window as any).recordingInterval) {\n      clearInterval((window as any).recordingInterval);\n    }\n    // Add new recording to list\n    const newRecording = {\n      id: Date.now(),\n      title: `Pokalbis ${new Date().toLocaleString('lt-LT')}`,\n      duration: `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`,\n      date: new Date().toISOString().split('T')[0]\n    };\n    setRecordedConversations(prev => [newRecording, ...prev]);\n    setRecordingTime(0);\n  };\n\n  const toggleTodo = (id: number) => {\n    setTodos(prev => prev.map(todo => \n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\n    ));\n  };\n\n  const deleteTodo = (id: number) => {\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n\n  const startEditingTodo = (todo: Todo) => {\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n    setEditingProject(todo.project || '');\n    setEditingOwner(todo.owner || '');\n  };\n\n  const saveEditedTodo = () => {\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => \n        todo.id === editingTodo ? { ...todo, text: editingText.trim(), project: editingProject, owner: editingOwner } : todo\n      ));\n      setEditingTodo(null);\n      setEditingText('');\n      setEditingProject('');\n      setEditingOwner('');\n    }\n  };\n\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n    setEditingProject('');\n    setEditingOwner('');\n  };\n\n  const addNewTodo = () => {\n    if (newTodoText.trim()) {\n      const newTodo: Todo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority,\n        project: newTodoProject,\n        owner: newTodoOwner\n      };\n      setTodos(prev => [...prev, newTodo]);\n      setNewTodoText('');\n      setNewTodoDate('');\n      setNewTodoPriority('medium');\n      setNewTodoProject('');\n      setNewTodoOwner('');\n      setShowNewTodoForm(false);\n    }\n  };\n\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n    setNewTodoProject('');\n    setNewTodoOwner('');\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high': return 'text-red-400';\n      case 'medium': return 'text-yellow-400';\n      case 'low': return 'text-green-400';\n      default: return 'text-white/60';\n    }\n  };\n\n  const getPriorityBg = (priority: string) => {\n    switch (priority) {\n      case 'high': return 'bg-red-500/20 border-red-500/30';\n      case 'medium': return 'bg-yellow-500/20 border-yellow-500/30';\n      case 'low': return 'bg-green-500/20 border-green-500/30';\n      default: return 'bg-white/5 border-white/10';\n    }\n  };\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Rūšiavimo funkcija\n  const sortedTodos = [...todos].sort((a, b) => {\n    if (sortType === 'date_desc') {\n      if (!a.date) return 1;\n      if (!b.date) return -1;\n      return b.date.localeCompare(a.date);\n    }\n    if (sortType === 'date_asc') {\n      if (!a.date) return 1;\n      if (!b.date) return -1;\n      return a.date.localeCompare(b.date);\n    }\n    if (sortType === 'alpha_asc') {\n      return a.text.localeCompare(b.text, 'lt');\n    }\n    if (sortType === 'alpha_desc') {\n      return b.text.localeCompare(a.text, 'lt');\n    }\n    return 0;\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\n        <div className=\"flex items-center gap-4\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\n            <Mic2 className=\"h-6 w-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold text-white\">Pokalbių įrašymas</h2>\n            <p className=\"text-sm text-white/60\">Profesionalus garso įrašymas su automatine transkribavimo technologija</p>\n          </div>\n        </div>\n      </div>\n\n      {/* 2x2 Grid Layout */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        \n        {/* Top-Left: Pokalbių įrašymo komponentas */}\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <Mic2 className=\"h-5 w-5 text-blue-400\" />\n            <h3 className=\"text-lg font-semibold text-white\">Pokalbių įrašymas</h3>\n          </div>\n          \n          <div className=\"space-y-4\">\n            {/* Recording Status */}\n            <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <span className=\"text-white font-medium\">Įrašymo statusas</span>\n                <div className={`w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>\n              </div>\n              <div className=\"text-2xl font-bold text-white mb-2\">\n                {formatTime(recordingTime)}\n              </div>\n              <div className=\"text-white/60 text-sm\">\n                {isRecording ? 'Įrašoma...' : 'Pasiruošta įrašymui'}\n              </div>\n            </div>\n\n            {/* Recording Controls */}\n            <div className=\"flex gap-3\">\n              {!isRecording ? (\n                <Button\n                  onClick={handleStartRecording}\n                  icon={<Play className=\"h-4 w-4\" />}\n                  variant=\"primary\"\n                  fullWidth\n                >\n                  Pradėti įrašymą\n                </Button>\n              ) : (\n                <Button\n                  onClick={handleStopRecording}\n                  icon={<Square className=\"h-4 w-4\" />}\n                  variant=\"primary\"\n                  fullWidth\n                  style={{ background: 'linear-gradient(to right, #ef4444, #ec4899)' }}\n                >\n                  Sustabdyti įrašymą\n                </Button>\n              )}\n            </div>\n\n            {/* Current Meeting Info */}\n            {currentMeeting && (\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\n                <div className=\"text-white font-medium mb-2\">Dabartinis pokalbis</div>\n                <div className=\"text-white/80 text-sm\">{currentMeeting.title}</div>\n                <div className=\"text-white/60 text-xs mt-1\">\n                  Pradėtas: {currentMeeting.date.toLocaleTimeString('lt-LT')}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom-Left: Įrašyti pokalbiai komponentas */}\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <Headphones className=\"h-5 w-5 text-purple-400\" />\n            <h3 className=\"text-lg font-semibold text-white\">Įrašyti pokalbiai</h3>\n          </div>\n          \n          <div className=\"space-y-3\">\n            {recordedConversations.map((conversation) => (\n              <div\n                key={conversation.id}\n                className=\"p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\"\n              >\n                <div className=\"flex items-center justify-between mb-2\">\n                  <div className=\"text-white font-medium text-sm truncate\">\n                    {conversation.title}\n                  </div>\n                  <div className=\"text-white/60 text-xs\">\n                    {conversation.duration}\n                  </div>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-white/60 text-xs\">\n                    {conversation.date}\n                  </div>\n                  <div className=\"flex gap-2\">\n                    <button className=\"p-1 text-white/60 hover:text-white transition-colors\">\n                      <Play className=\"h-3 w-3\" />\n                    </button>\n                    <button className=\"p-1 text-white/60 hover:text-white transition-colors\">\n                      <Settings className=\"h-3 w-3\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n          \n          <div className=\"mt-4 pt-4 border-t border-white/10\">\n            <div className=\"text-white/60 text-sm\">\n              Iš viso: {recordedConversations.length} pokalbių\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom-Right: Analitikos komponentas */}\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <BarChart3 className=\"h-5 w-5 text-orange-400\" />\n            <h3 className=\"text-lg font-semibold text-white\">Analitika</h3>\n          </div>\n          \n          <div className=\"space-y-4\">\n            {/* Recording Stats */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10 text-center\">\n                <div className=\"text-2xl font-bold text-white\">{recordedConversations.length}</div>\n                <div className=\"text-white/60 text-sm\">Įrašyti pokalbiai</div>\n              </div>\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10 text-center\">\n                <div className=\"text-2xl font-bold text-white\">\n                  {recordedConversations.reduce((total, conv) => {\n                    const [mins, secs] = conv.duration.split(':').map(Number);\n                    return total + mins * 60 + secs;\n                  }, 0) / 60 | 0}\n                </div>\n                <div className=\"text-white/60 text-sm\">Minutės</div>\n              </div>\n            </div>\n\n            {/* Progress Bars */}\n            <div className=\"space-y-3\">\n              <div>\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-white/80 text-sm\">Užduočių progresas</span>\n                  <span className=\"text-white/60 text-sm\">\n                    {Math.round((todos.filter(t => t.completed).length / todos.length) * 100)}%\n                  </span>\n                </div>\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\n                  <div \n                    className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${(todos.filter(t => t.completed).length / todos.length) * 100}%` }}\n                  ></div>\n                </div>\n              </div>\n              \n              <div>\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-white/80 text-sm\">Įrašymo kokybė</span>\n                  <span className=\"text-white/60 text-sm\">95%</span>\n                </div>\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{ width: '95%' }}></div>\n                </div>\n              </div>\n              \n              <div>\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-white/80 text-sm\">Transkribavimo tikslumas</span>\n                  <span className=\"text-white/60 text-sm\">98%</span>\n                </div>\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\n                  <div className=\"bg-purple-500 h-2 rounded-full\" style={{ width: '98%' }}></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RecordingPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAOC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAASC,MAAM,EAAcC,SAAS,QAA0C,cAAc;AAG5I,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuB9B,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,cAAc;EACdC,cAAc;EACdC,gBAAgB;EAChBC,eAAe;EACfC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAS,CACzC;IAAEwB,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,MAAM;IAAEC,OAAO,EAAE,oBAAoB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,KAAK;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAC1L;IAAER,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE,WAAW;IAAEC,QAAQ,EAAE;EAAa,CAAC,EACrM;IAAER,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,KAAK;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAmB,CAAC,EAC/L;IAAER,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEE,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,QAAQ,EAAE;EAAmB,CAAC,CACzL,CAAC;EACF,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlC,QAAQ,CAAC,CACjE;IAAEwB,EAAE,EAAE,CAAC;IAAEW,KAAK,EAAE,wBAAwB;IAAEC,QAAQ,EAAE,OAAO;IAAET,IAAI,EAAE;EAAa,CAAC,EACjF;IAAEH,EAAE,EAAE,CAAC;IAAEW,KAAK,EAAE,mBAAmB;IAAEC,QAAQ,EAAE,OAAO;IAAET,IAAI,EAAE;EAAa,CAAC,EAC5E;IAAEH,EAAE,EAAE,CAAC;IAAEW,KAAK,EAAE,qBAAqB;IAAEC,QAAQ,EAAE,OAAO;IAAET,IAAI,EAAE;EAAa,CAAC,CAC/E,CAAC;;EAEF;EACA,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAA4B,QAAQ,CAAC;EAC3F,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAwD,WAAW,CAAC;EAE5G,MAAM2D,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvCxC,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,CAAC,CAAC;IACnB;IACA,MAAMuC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCxC,gBAAgB,CAACyC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC;;IAER;IACCC,MAAM,CAASC,iBAAiB,GAAGJ,QAAQ;EAC9C,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChC9C,cAAc,CAAC,KAAK,CAAC;IACrB,IAAK4C,MAAM,CAASC,iBAAiB,EAAE;MACrCE,aAAa,CAAEH,MAAM,CAASC,iBAAiB,CAAC;IAClD;IACA;IACA,MAAMG,YAAY,GAAG;MACnB3C,EAAE,EAAE4C,IAAI,CAACC,GAAG,CAAC,CAAC;MACdlC,KAAK,EAAE,YAAY,IAAIiC,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC,EAAE;MACvDlC,QAAQ,EAAE,GAAGmC,IAAI,CAACC,KAAK,CAACpD,aAAa,GAAG,EAAE,CAAC,IAAI,CAACA,aAAa,GAAG,EAAE,EAAEqD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACjG/C,IAAI,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD1C,wBAAwB,CAAC4B,IAAI,IAAI,CAACK,YAAY,EAAE,GAAGL,IAAI,CAAC,CAAC;IACzDzC,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAMwD,UAAU,GAAIrD,EAAU,IAAK;IACjCD,QAAQ,CAACuC,IAAI,IAAIA,IAAI,CAACgB,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACvD,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGuD,IAAI;MAAErD,SAAS,EAAE,CAACqD,IAAI,CAACrD;IAAU,CAAC,GAAGqD,IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIxD,EAAU,IAAK;IACjCD,QAAQ,CAACuC,IAAI,IAAIA,IAAI,CAACmB,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACvD,EAAE,KAAKA,EAAE,CAAC,CAAC;EACvD,CAAC;EAED,MAAM0D,gBAAgB,GAAIH,IAAU,IAAK;IACvCzC,cAAc,CAACyC,IAAI,CAACvD,EAAE,CAAC;IACvBgB,cAAc,CAACuC,IAAI,CAACtD,IAAI,CAAC;IACzBiB,iBAAiB,CAACqC,IAAI,CAAClD,OAAO,IAAI,EAAE,CAAC;IACrCe,eAAe,CAACmC,IAAI,CAACjD,KAAK,IAAI,EAAE,CAAC;EACnC,CAAC;EAED,MAAMqD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9C,WAAW,IAAIE,WAAW,CAAC6C,IAAI,CAAC,CAAC,EAAE;MACrC7D,QAAQ,CAACuC,IAAI,IAAIA,IAAI,CAACgB,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACvD,EAAE,KAAKa,WAAW,GAAG;QAAE,GAAG0C,IAAI;QAAEtD,IAAI,EAAEc,WAAW,CAAC6C,IAAI,CAAC,CAAC;QAAEvD,OAAO,EAAEY,cAAc;QAAEX,KAAK,EAAEa;MAAa,CAAC,GAAGoC,IAClH,CAAC,CAAC;MACFzC,cAAc,CAAC,IAAI,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC;EAED,MAAMyC,aAAa,GAAGA,CAAA,KAAM;IAC1B/C,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;IAClBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAM0C,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIzC,WAAW,CAACuC,IAAI,CAAC,CAAC,EAAE;MACtB,MAAMG,OAAa,GAAG;QACpB/D,EAAE,EAAE4C,IAAI,CAACC,GAAG,CAAC,CAAC;QACd5C,IAAI,EAAEoB,WAAW,CAACuC,IAAI,CAAC,CAAC;QACxB1D,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAEsB,WAAW,IAAIuC,SAAS;QAC9B5D,QAAQ,EAAEuB,eAAe;QACzBtB,OAAO,EAAEwB,cAAc;QACvBvB,KAAK,EAAEyB;MACT,CAAC;MACDhC,QAAQ,CAACuC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEyB,OAAO,CAAC,CAAC;MACpCzC,cAAc,CAAC,EAAE,CAAC;MAClBI,cAAc,CAAC,EAAE,CAAC;MAClBE,kBAAkB,CAAC,QAAQ,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;MACnBR,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMyC,aAAa,GAAGA,CAAA,KAAM;IAC1BzC,kBAAkB,CAAC,KAAK,CAAC;IACzBF,cAAc,CAAC,EAAE,CAAC;IAClBI,cAAc,CAAC,EAAE,CAAC;IAClBE,kBAAkB,CAAC,QAAQ,CAAC;IAC5BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAMkC,gBAAgB,GAAI9D,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAM+D,aAAa,GAAI/D,QAAgB,IAAK;IAC1C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,iCAAiC;MACrD,KAAK,QAAQ;QAAE,OAAO,uCAAuC;MAC7D,KAAK,KAAK;QAAE,OAAO,qCAAqC;MACxD;QAAS,OAAO,4BAA4B;IAC9C;EACF,CAAC;EAED,MAAMgE,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAGvB,IAAI,CAACC,KAAK,CAACqB,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACtB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;;EAED;EACA,MAAMsB,WAAW,GAAG,CAAC,GAAG1E,KAAK,CAAC,CAAC2E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC5C,IAAI1C,QAAQ,KAAK,WAAW,EAAE;MAC5B,IAAI,CAACyC,CAAC,CAACvE,IAAI,EAAE,OAAO,CAAC;MACrB,IAAI,CAACwE,CAAC,CAACxE,IAAI,EAAE,OAAO,CAAC,CAAC;MACtB,OAAOwE,CAAC,CAACxE,IAAI,CAACyE,aAAa,CAACF,CAAC,CAACvE,IAAI,CAAC;IACrC;IACA,IAAI8B,QAAQ,KAAK,UAAU,EAAE;MAC3B,IAAI,CAACyC,CAAC,CAACvE,IAAI,EAAE,OAAO,CAAC;MACrB,IAAI,CAACwE,CAAC,CAACxE,IAAI,EAAE,OAAO,CAAC,CAAC;MACtB,OAAOuE,CAAC,CAACvE,IAAI,CAACyE,aAAa,CAACD,CAAC,CAACxE,IAAI,CAAC;IACrC;IACA,IAAI8B,QAAQ,KAAK,WAAW,EAAE;MAC5B,OAAOyC,CAAC,CAACzE,IAAI,CAAC2E,aAAa,CAACD,CAAC,CAAC1E,IAAI,EAAE,IAAI,CAAC;IAC3C;IACA,IAAIgC,QAAQ,KAAK,YAAY,EAAE;MAC7B,OAAO0C,CAAC,CAAC1E,IAAI,CAAC2E,aAAa,CAACF,CAAC,CAACzE,IAAI,EAAE,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC;EACV,CAAC,CAAC;EAEF,oBACEhB,OAAA;IAAK4F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB7F,OAAA;MAAK4F,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClF7F,OAAA;QAAK4F,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC7F,OAAA;UAAK4F,SAAS,EAAC,6JAA6J;UAAAC,QAAA,eAC1K7F,OAAA,CAACR,IAAI;YAACoG,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNjG,OAAA;UAAA6F,QAAA,gBACE7F,OAAA;YAAI4F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEjG,OAAA;YAAG4F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjG,OAAA;MAAK4F,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAGpD7F,OAAA;QAAK4F,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF7F,OAAA;UAAK4F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C7F,OAAA,CAACR,IAAI;YAACoG,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CjG,OAAA;YAAI4F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENjG,OAAA;UAAK4F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB7F,OAAA;YAAK4F,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/D7F,OAAA;cAAK4F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD7F,OAAA;gBAAM4F,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChEjG,OAAA;gBAAK4F,SAAS,EAAE,wBAAwBnF,WAAW,GAAG,0BAA0B,GAAG,aAAa;cAAG;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC,eACNjG,OAAA;cAAK4F,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDV,UAAU,CAACxE,aAAa;YAAC;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNjG,OAAA;cAAK4F,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCpF,WAAW,GAAG,YAAY,GAAG;YAAqB;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjG,OAAA;YAAK4F,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxB,CAACpF,WAAW,gBACXT,OAAA,CAACF,MAAM;cACLoG,OAAO,EAAEhD,oBAAqB;cAC9BiD,IAAI,eAAEnG,OAAA,CAACL,IAAI;gBAACiG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnCG,OAAO,EAAC,SAAS;cACjBC,SAAS;cAAAR,QAAA,EACV;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETjG,OAAA,CAACF,MAAM;cACLoG,OAAO,EAAE1C,mBAAoB;cAC7B2C,IAAI,eAAEnG,OAAA,CAACJ,MAAM;gBAACgG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrCG,OAAO,EAAC,SAAS;cACjBC,SAAS;cACTC,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAA8C,CAAE;cAAAV,QAAA,EACtE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL9F,cAAc,iBACbH,OAAA;YAAK4F,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/D7F,OAAA;cAAK4F,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtEjG,OAAA;cAAK4F,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE1F,cAAc,CAACuB;YAAK;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnEjG,OAAA;cAAK4F,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,iBAChC,EAAC1F,cAAc,CAACe,IAAI,CAACsF,kBAAkB,CAAC,OAAO,CAAC;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAK4F,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF7F,OAAA;UAAK4F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C7F,OAAA,CAACP,UAAU;YAACmG,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDjG,OAAA;YAAI4F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENjG,OAAA;UAAK4F,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBrE,qBAAqB,CAAC6C,GAAG,CAAEoC,YAAY,iBACtCzG,OAAA;YAEE4F,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBAEzH7F,OAAA;cAAK4F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD7F,OAAA;gBAAK4F,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACrDY,YAAY,CAAC/E;cAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNjG,OAAA;gBAAK4F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCY,YAAY,CAAC9E;cAAQ;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjG,OAAA;cAAK4F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD7F,OAAA;gBAAK4F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCY,YAAY,CAACvF;cAAI;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNjG,OAAA;gBAAK4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7F,OAAA;kBAAQ4F,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACtE7F,OAAA,CAACL,IAAI;oBAACiG,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACTjG,OAAA;kBAAQ4F,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACtE7F,OAAA,CAACN,QAAQ;oBAACkG,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAvBDQ,YAAY,CAAC1F,EAAE;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBjB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjG,OAAA;UAAK4F,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD7F,OAAA;YAAK4F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,gBAC5B,EAACrE,qBAAqB,CAACkF,MAAM,EAAC,gBACzC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAK4F,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF7F,OAAA;UAAK4F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C7F,OAAA,CAACH,SAAS;YAAC+F,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDjG,OAAA;YAAI4F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENjG,OAAA;UAAK4F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB7F,OAAA;YAAK4F,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC7F,OAAA;cAAK4F,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3E7F,OAAA;gBAAK4F,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAErE,qBAAqB,CAACkF;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnFjG,OAAA;gBAAK4F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNjG,OAAA;cAAK4F,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3E7F,OAAA;gBAAK4F,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC3CrE,qBAAqB,CAACmF,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAK;kBAC7C,MAAM,CAACxB,IAAI,EAAEC,IAAI,CAAC,GAAGuB,IAAI,CAAClF,QAAQ,CAACwC,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACyC,MAAM,CAAC;kBACzD,OAAOF,KAAK,GAAGvB,IAAI,GAAG,EAAE,GAAGC,IAAI;gBACjC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACNjG,OAAA;gBAAK4F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjG,OAAA;YAAK4F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7F,OAAA;cAAA6F,QAAA,gBACE7F,OAAA;gBAAK4F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD7F,OAAA;kBAAM4F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjEjG,OAAA;kBAAM4F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACpC/B,IAAI,CAACiD,KAAK,CAAElG,KAAK,CAAC2D,MAAM,CAACwC,CAAC,IAAIA,CAAC,CAAC/F,SAAS,CAAC,CAACyF,MAAM,GAAG7F,KAAK,CAAC6F,MAAM,GAAI,GAAG,CAAC,EAAC,GAC5E;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjG,OAAA;gBAAK4F,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD7F,OAAA;kBACE4F,SAAS,EAAC,2DAA2D;kBACrEU,KAAK,EAAE;oBAAEW,KAAK,EAAE,GAAIpG,KAAK,CAAC2D,MAAM,CAACwC,CAAC,IAAIA,CAAC,CAAC/F,SAAS,CAAC,CAACyF,MAAM,GAAG7F,KAAK,CAAC6F,MAAM,GAAI,GAAG;kBAAI;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjG,OAAA;cAAA6F,QAAA,gBACE7F,OAAA;gBAAK4F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD7F,OAAA;kBAAM4F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7DjG,OAAA;kBAAM4F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNjG,OAAA;gBAAK4F,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD7F,OAAA;kBAAK4F,SAAS,EAAC,8BAA8B;kBAACU,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAM;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjG,OAAA;cAAA6F,QAAA,gBACE7F,OAAA;gBAAK4F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD7F,OAAA;kBAAM4F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEjG,OAAA;kBAAM4F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNjG,OAAA;gBAAK4F,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD7F,OAAA;kBAAK4F,SAAS,EAAC,gCAAgC;kBAACU,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAM;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzF,EAAA,CA3WIP,aAA2C;AAAAiH,EAAA,GAA3CjH,aAA2C;AA6WjD,eAAeA,aAAa;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}