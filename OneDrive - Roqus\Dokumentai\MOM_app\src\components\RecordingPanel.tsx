import React, { useState, useCallback } from 'react';
import { Plus, Mic2, Square, Pause, Play, AlertCircle } from 'lucide-react';
import { RecordingButton } from './RecordingButton';
import { RecordingIndicator } from './RecordingIndicator';
import { Meeting, RecordingState } from '../types/meeting';

interface RecordingPanelProps {
  recordingState: RecordingState;
  currentMeeting: Meeting | null;
  onStartRecording: (title: string) => Promise<void>;
  onStopRecording: () => Promise<void>;
  onPauseRecording: () => void;
  onResumeRecording: () => void;
}

export const RecordingPanel: React.FC<RecordingPanelProps> = ({
  recordingState,
  onStartRecording,
  onStopRecording,
}) => {
  return (
    <div className="flex-1 flex flex-col justify-center items-center space-y-8 p-6 relative overflow-hidden">
      {/* Subtle Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-blue-400/15 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-0.5 h-0.5 bg-purple-400/20 rounded-full animate-ping"></div>
        <div className="absolute top-1/2 left-3/4 w-1 h-1 bg-indigo-400/15 rounded-full animate-bounce"></div>
      </div>

      {/* Compact Modern Recording Orb */}
      <div className="relative group">
        {/* Subtle Outer Glow Rings */}
        <div className={`absolute inset-0 rounded-full transition-all duration-2000 ease-out ${
          recordingState.isRecording
            ? 'bg-gradient-to-br from-red-500/10 via-pink-500/8 to-red-600/5 animate-pulse scale-125'
            : 'bg-gradient-to-br from-blue-500/6 via-indigo-500/4 to-purple-600/3 scale-110 group-hover:scale-125'
        }`} style={{ width: '140px', height: '140px', marginLeft: '-70px', marginTop: '-70px', left: '50%', top: '50%' }}></div>

        <div className={`absolute inset-0 rounded-full transition-all duration-1500 ease-out ${
          recordingState.isRecording
            ? 'bg-gradient-to-br from-red-400/15 via-pink-400/12 to-red-500/8 scale-115'
            : 'bg-gradient-to-br from-blue-400/10 via-indigo-400/8 to-purple-500/6 scale-105 group-hover:scale-115'
        }`} style={{ width: '110px', height: '110px', marginLeft: '-55px', marginTop: '-55px', left: '50%', top: '50%' }}></div>

        {/* Compact Main Orb */}
        <div className={`relative w-20 h-20 sm:w-24 sm:h-24 rounded-full flex items-center justify-center transition-all duration-1000 ease-out transform backdrop-blur-2xl border ${
          recordingState.isRecording
            ? 'bg-gradient-to-br from-red-500/25 via-pink-500/20 to-red-600/15 border-red-300/30 shadow-xl shadow-red-500/30 scale-105'
            : 'bg-gradient-to-br from-slate-800/25 via-slate-700/20 to-slate-600/15 border-slate-300/25 shadow-xl shadow-blue-500/15 scale-100 hover:scale-105 group-hover:border-blue-300/40'
        }`}>
          {/* Compact Inner Glass Layers */}
          <div className={`absolute inset-2 rounded-full transition-all duration-700 backdrop-blur-lg ${
            recordingState.isRecording
              ? 'bg-gradient-to-br from-red-400/20 via-pink-400/15 to-red-500/12'
              : 'bg-gradient-to-br from-slate-400/12 via-slate-500/10 to-slate-600/8 group-hover:from-blue-400/15 group-hover:to-indigo-500/12'
          }`}></div>

          {/* Compact Microphone Container */}
          <div className={`relative w-10 h-10 rounded-full flex items-center justify-center transition-all duration-500 ${
            recordingState.isRecording
              ? 'bg-gradient-to-br from-red-200/15 to-red-400/12 shadow-inner'
              : 'bg-gradient-to-br from-slate-200/12 to-slate-400/10 shadow-inner group-hover:from-blue-200/15 group-hover:to-blue-400/12'
          }`}>
            <Mic2 className={`h-5 w-5 sm:h-6 sm:w-6 transition-all duration-500 ${
              recordingState.isRecording
                ? 'text-red-100 animate-pulse drop-shadow-lg'
                : 'text-slate-100 group-hover:text-blue-100 group-hover:scale-110 drop-shadow-md'
            }`} />
          </div>

          {/* Subtle Light Reflections */}
          <div className="absolute top-2 left-3 w-3 h-3 bg-white/12 rounded-full blur-sm"></div>
          <div className="absolute top-1 left-2 w-2 h-2 bg-white/20 rounded-full blur-xs"></div>
        </div>
      </div>

      {/* Compact Modern Typography */}
      <div className="text-center space-y-4 max-w-lg mx-auto relative z-10">
        <div className="space-y-2">
          <h1 className={`text-lg sm:text-xl font-bold transition-all duration-700 leading-tight tracking-tight ${
            recordingState.isRecording
              ? 'bg-gradient-to-r from-red-200 via-pink-100 to-red-200 bg-clip-text text-transparent'
              : 'bg-gradient-to-r from-slate-100 via-blue-50 to-indigo-100 bg-clip-text text-transparent'
          }`}>
            {recordingState.isRecording ? 'Įrašoma pokalbis' : 'Pradėkite naują pokalbį'}
          </h1>

          <div className={`h-0.5 w-16 mx-auto rounded-full transition-all duration-700 ${
            recordingState.isRecording
              ? 'bg-gradient-to-r from-red-400 to-pink-400'
              : 'bg-gradient-to-r from-blue-400 to-indigo-400'
          }`}></div>
        </div>

        <p className="text-sm text-slate-300/80 max-w-md mx-auto leading-relaxed font-medium">
          {recordingState.isRecording
            ? 'Pokalbis įrašomas su AI transkribavimo technologija'
            : 'Profesionalus garso įrašymas su automatine transkribavimo technologija'
          }
        </p>
      </div>

      {/* Compact Modern Action Button */}
      <div className="relative z-10">
        {!recordingState.isRecording ? (
          <button
            onClick={() => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}
            className="group relative inline-flex items-center justify-center space-x-3 px-6 py-3 text-sm font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-102 active:scale-98 focus:outline-none"
          >
            {/* Compact Glassmorphic Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/15 via-indigo-600/12 to-purple-700/8 backdrop-blur-xl"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/8"></div>
            <div className="absolute inset-[1px] bg-gradient-to-br from-white/15 via-white/8 to-transparent rounded-2xl"></div>

            {/* Subtle Border with Glow */}
            <div className="absolute inset-0 rounded-2xl border border-blue-300/25 transition-all duration-500 group-hover:border-blue-200/40 group-hover:shadow-lg group-hover:shadow-blue-400/20"></div>

            {/* Compact Icon Container */}
            <div className="relative z-10 flex items-center space-x-3">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-blue-400/25 to-indigo-500/15 backdrop-blur-lg flex items-center justify-center transition-all duration-300 group-hover:scale-105 border border-blue-300/15">
                <Plus className="h-4 w-4 text-blue-100 transition-all duration-300 group-hover:rotate-90 group-hover:text-white drop-shadow-sm" />
              </div>
              <span className="text-sm font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 bg-clip-text text-transparent">
                Pradėti įrašymą
              </span>
            </div>

            {/* Subtle Shine Animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out"></div>
          </button>
        ) : (
          <button
            onClick={onStopRecording}
            className="group relative inline-flex items-center justify-center space-x-3 px-6 py-3 text-sm font-semibold text-white overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-102 active:scale-98 focus:outline-none"
          >
            {/* Compact Glassmorphic Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/15 via-pink-600/12 to-red-700/8 backdrop-blur-xl"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/8"></div>
            <div className="absolute inset-[1px] bg-gradient-to-br from-white/15 via-white/8 to-transparent rounded-2xl"></div>

            {/* Subtle Border with Glow */}
            <div className="absolute inset-0 rounded-2xl border border-red-300/30 transition-all duration-500 group-hover:border-red-200/50 group-hover:shadow-lg group-hover:shadow-red-400/25 animate-pulse"></div>

            {/* Compact Icon Container */}
            <div className="relative z-10 flex items-center space-x-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-red-400/25 to-pink-500/15 backdrop-blur-lg flex items-center justify-center transition-all duration-300 group-hover:scale-105 border border-red-300/15">
                <Square className="h-4 w-4 text-red-100 transition-all duration-300 group-hover:text-white drop-shadow-sm" />
              </div>
              <span className="text-sm font-semibold bg-gradient-to-r from-red-100 to-pink-100 bg-clip-text text-transparent">
                Sustabdyti įrašymą
              </span>
            </div>

            {/* Subtle Shine Animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out"></div>
          </button>
        )}
      </div>

      {/* Compact Recording Status Badge */}
      {recordingState.isRecording && (
        <div className="relative">
          {/* Compact Status Container */}
          <div className="relative flex items-center space-x-3 px-4 py-2 bg-gradient-to-r from-red-500/12 via-pink-500/10 to-red-600/8 backdrop-blur-xl rounded-xl border border-red-300/20 shadow-lg shadow-red-500/15">
            {/* Compact Recording Indicator */}
            <div className="relative flex items-center space-x-2">
              {/* Simple Recording Dot */}
              <div className="relative">
                <div className="w-3 h-3 bg-gradient-to-br from-red-400 to-red-500 rounded-full shadow-md shadow-red-500/50 animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-red-400/50 rounded-full animate-ping"></div>
                <div className="absolute top-0.5 left-0.5 w-1 h-1 bg-white/60 rounded-full"></div>
              </div>

              {/* Compact Typography */}
              <span className="text-sm font-bold bg-gradient-to-r from-red-200 to-pink-100 bg-clip-text text-transparent tracking-wide">
                ĮRAŠOMA
              </span>
            </div>

            {/* Compact Waveform Visualization */}
            <div className="flex items-center space-x-0.5">
              <div className="w-0.5 bg-red-300/60 rounded-full animate-pulse" style={{ height: '8px', animationDelay: '0ms' }}></div>
              <div className="w-0.5 bg-red-300/80 rounded-full animate-pulse" style={{ height: '12px', animationDelay: '150ms' }}></div>
              <div className="w-0.5 bg-red-300/60 rounded-full animate-pulse" style={{ height: '10px', animationDelay: '300ms' }}></div>
              <div className="w-0.5 bg-red-300/90 rounded-full animate-pulse" style={{ height: '14px', animationDelay: '450ms' }}></div>
              <div className="w-0.5 bg-red-300/70 rounded-full animate-pulse" style={{ height: '9px', animationDelay: '600ms' }}></div>
            </div>
          </div>

          {/* Subtle Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/8 to-pink-500/8 rounded-xl blur-lg animate-pulse"></div>
        </div>
      )}
    </div>
  );
};