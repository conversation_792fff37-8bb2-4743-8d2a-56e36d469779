import React, { useState, useCallback } from 'react';
import { Plus, Mic2, Square, Pause, Play, AlertCircle } from 'lucide-react';
import { RecordingButton } from './RecordingButton';
import { RecordingIndicator } from './RecordingIndicator';
import { Meeting, RecordingState } from '../types/meeting';

interface RecordingPanelProps {
  recordingState: RecordingState;
  currentMeeting: Meeting | null;
  onStartRecording: (title: string) => Promise<void>;
  onStopRecording: () => Promise<void>;
  onPauseRecording: () => void;
  onResumeRecording: () => void;
}

export const RecordingPanel: React.FC<RecordingPanelProps> = ({
  recordingState,
  onStartRecording,
  onStopRecording,
}) => {
  return (
    <div className="flex-1 flex flex-col justify-center items-center space-y-16 p-8 relative overflow-hidden">
      {/* Floating Particles Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400/20 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400/30 rounded-full animate-ping"></div>
        <div className="absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-indigo-400/25 rounded-full animate-bounce"></div>
      </div>

      {/* Ultra-Modern Recording Orb */}
      <div className="relative group">
        {/* Outer Glow Rings */}
        <div className={`absolute inset-0 rounded-full transition-all duration-2000 ease-out ${
          recordingState.isRecording
            ? 'bg-gradient-to-br from-red-500/15 via-pink-500/10 to-red-600/5 animate-pulse scale-150'
            : 'bg-gradient-to-br from-blue-500/8 via-indigo-500/6 to-purple-600/4 scale-125 group-hover:scale-150'
        }`} style={{ width: '280px', height: '280px', marginLeft: '-140px', marginTop: '-140px', left: '50%', top: '50%' }}></div>

        <div className={`absolute inset-0 rounded-full transition-all duration-1500 ease-out ${
          recordingState.isRecording
            ? 'bg-gradient-to-br from-red-400/20 via-pink-400/15 to-red-500/10 scale-130'
            : 'bg-gradient-to-br from-blue-400/12 via-indigo-400/10 to-purple-500/8 scale-115 group-hover:scale-130'
        }`} style={{ width: '220px', height: '220px', marginLeft: '-110px', marginTop: '-110px', left: '50%', top: '50%' }}></div>

        {/* Main Orb with Advanced Glassmorphism */}
        <div className={`relative w-40 h-40 sm:w-44 sm:h-44 rounded-full flex items-center justify-center transition-all duration-1000 ease-out transform backdrop-blur-3xl border ${
          recordingState.isRecording
            ? 'bg-gradient-to-br from-red-500/30 via-pink-500/25 to-red-600/20 border-red-300/40 shadow-2xl shadow-red-500/40 scale-110'
            : 'bg-gradient-to-br from-slate-800/30 via-slate-700/25 to-slate-600/20 border-slate-300/30 shadow-2xl shadow-blue-500/20 scale-100 hover:scale-110 group-hover:border-blue-300/50'
        }`}>
          {/* Multiple Inner Glass Layers */}
          <div className={`absolute inset-3 rounded-full transition-all duration-700 backdrop-blur-xl ${
            recordingState.isRecording
              ? 'bg-gradient-to-br from-red-400/25 via-pink-400/20 to-red-500/15'
              : 'bg-gradient-to-br from-slate-400/15 via-slate-500/12 to-slate-600/10 group-hover:from-blue-400/20 group-hover:to-indigo-500/15'
          }`}></div>

          <div className={`absolute inset-6 rounded-full transition-all duration-500 backdrop-blur-lg ${
            recordingState.isRecording
              ? 'bg-gradient-to-br from-red-300/20 to-pink-400/15'
              : 'bg-gradient-to-br from-slate-300/10 to-slate-400/8 group-hover:from-blue-300/15 group-hover:to-indigo-400/12'
          }`}></div>

          {/* Neumorphic Microphone Container */}
          <div className={`relative w-20 h-20 rounded-full flex items-center justify-center transition-all duration-500 ${
            recordingState.isRecording
              ? 'bg-gradient-to-br from-red-200/20 to-red-400/15 shadow-inner'
              : 'bg-gradient-to-br from-slate-200/15 to-slate-400/12 shadow-inner group-hover:from-blue-200/20 group-hover:to-blue-400/15'
          }`}>
            <Mic2 className={`h-10 w-10 sm:h-12 sm:w-12 transition-all duration-500 ${
              recordingState.isRecording
                ? 'text-red-100 animate-pulse drop-shadow-2xl'
                : 'text-slate-100 group-hover:text-blue-100 group-hover:scale-110 drop-shadow-xl'
            }`} />
          </div>

          {/* Sophisticated Light Reflections */}
          <div className="absolute top-6 left-8 w-8 h-8 bg-white/15 rounded-full blur-md"></div>
          <div className="absolute top-4 left-6 w-4 h-4 bg-white/25 rounded-full blur-sm"></div>
          <div className="absolute bottom-8 right-6 w-6 h-6 bg-white/10 rounded-full blur-lg"></div>
        </div>
      </div>

      {/* Ultra-Modern Typography */}
      <div className="text-center space-y-6 max-w-2xl mx-auto relative z-10">
        <div className="space-y-4">
          <h1 className={`text-3xl sm:text-4xl lg:text-5xl font-black transition-all duration-700 leading-tight tracking-tight ${
            recordingState.isRecording
              ? 'bg-gradient-to-r from-red-200 via-pink-100 to-red-200 bg-clip-text text-transparent'
              : 'bg-gradient-to-r from-slate-100 via-blue-50 to-indigo-100 bg-clip-text text-transparent'
          }`}>
            {recordingState.isRecording ? 'Įrašoma pokalbis' : 'Pradėkite naują pokalbį'}
          </h1>

          <div className={`h-1 w-24 mx-auto rounded-full transition-all duration-700 ${
            recordingState.isRecording
              ? 'bg-gradient-to-r from-red-400 to-pink-400'
              : 'bg-gradient-to-r from-blue-400 to-indigo-400'
          }`}></div>
        </div>

        <p className="text-lg sm:text-xl text-slate-300/90 max-w-lg mx-auto leading-relaxed font-medium tracking-wide">
          {recordingState.isRecording
            ? 'Jūsų pokalbis įrašomas su pažangia AI transkribavimo technologija'
            : 'Profesionalus garso įrašymas su automatine transkribavimo technologija'
          }
        </p>
      </div>

      {/* Ultra-Modern Floating Action Button */}
      <div className="relative z-10">
        {!recordingState.isRecording ? (
          <button
            onClick={() => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}
            className="group relative inline-flex items-center justify-center space-x-4 px-12 py-5 text-xl font-bold text-white overflow-hidden rounded-3xl transition-all duration-700 transform hover:scale-105 active:scale-95 focus:outline-none"
          >
            {/* Advanced Glassmorphic Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-indigo-600/15 to-purple-700/10 backdrop-blur-2xl"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-white/10"></div>
            <div className="absolute inset-[1px] bg-gradient-to-br from-white/20 via-white/10 to-transparent rounded-3xl"></div>

            {/* Neumorphic Border with Glow */}
            <div className="absolute inset-0 rounded-3xl border-2 border-blue-300/30 transition-all duration-700 group-hover:border-blue-200/50 group-hover:shadow-2xl group-hover:shadow-blue-400/30"></div>

            {/* Floating Icon Container */}
            <div className="relative z-10 flex items-center space-x-4">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-blue-400/30 to-indigo-500/20 backdrop-blur-xl flex items-center justify-center transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 border border-blue-300/20">
                <Plus className="h-6 w-6 text-blue-100 transition-all duration-500 group-hover:rotate-90 group-hover:text-white drop-shadow-lg" />
              </div>
              <div className="flex flex-col items-start">
                <span className="text-xl font-black tracking-wide bg-gradient-to-r from-blue-100 to-indigo-100 bg-clip-text text-transparent">
                  Pradėti įrašymą
                </span>
                <span className="text-sm text-blue-200/80 font-medium">
                  Naujas pokalbis
                </span>
              </div>
            </div>

            {/* Advanced Shine Animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1500 ease-out"></div>

            {/* Floating Particles */}
            <div className="absolute top-2 right-4 w-1 h-1 bg-blue-300/60 rounded-full animate-ping"></div>
            <div className="absolute bottom-3 left-6 w-0.5 h-0.5 bg-indigo-300/40 rounded-full animate-pulse"></div>
          </button>
        ) : (
          <button
            onClick={onStopRecording}
            className="group relative inline-flex items-center justify-center space-x-4 px-12 py-5 text-xl font-bold text-white overflow-hidden rounded-3xl transition-all duration-700 transform hover:scale-105 active:scale-95 focus:outline-none"
          >
            {/* Advanced Glassmorphic Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 via-pink-600/15 to-red-700/10 backdrop-blur-2xl"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-white/10"></div>
            <div className="absolute inset-[1px] bg-gradient-to-br from-white/20 via-white/10 to-transparent rounded-3xl"></div>

            {/* Neumorphic Border with Glow */}
            <div className="absolute inset-0 rounded-3xl border-2 border-red-300/40 transition-all duration-700 group-hover:border-red-200/60 group-hover:shadow-2xl group-hover:shadow-red-400/40 animate-pulse"></div>

            {/* Floating Icon Container */}
            <div className="relative z-10 flex items-center space-x-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-red-400/30 to-pink-500/20 backdrop-blur-xl flex items-center justify-center transition-all duration-500 group-hover:scale-110 border border-red-300/20">
                <Square className="h-6 w-6 text-red-100 transition-all duration-500 group-hover:text-white drop-shadow-lg" />
              </div>
              <div className="flex flex-col items-start">
                <span className="text-xl font-black tracking-wide bg-gradient-to-r from-red-100 to-pink-100 bg-clip-text text-transparent">
                  Sustabdyti įrašymą
                </span>
                <span className="text-sm text-red-200/80 font-medium">
                  Baigti pokalbį
                </span>
              </div>
            </div>

            {/* Advanced Shine Animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1500 ease-out"></div>

            {/* Floating Particles */}
            <div className="absolute top-2 right-4 w-1 h-1 bg-red-300/60 rounded-full animate-ping"></div>
            <div className="absolute bottom-3 left-6 w-0.5 h-0.5 bg-pink-300/40 rounded-full animate-pulse"></div>
          </button>
        )}
      </div>

      {/* Ultra-Modern Recording Status Badge */}
      {recordingState.isRecording && (
        <div className="relative">
          {/* Floating Status Container */}
          <div className="relative flex items-center space-x-4 px-8 py-4 bg-gradient-to-r from-red-500/15 via-pink-500/12 to-red-600/10 backdrop-blur-3xl rounded-2xl border border-red-300/25 shadow-2xl shadow-red-500/20">
            {/* Advanced Recording Indicator */}
            <div className="relative flex items-center space-x-3">
              {/* Multi-layered Recording Dot */}
              <div className="relative">
                <div className="w-5 h-5 bg-gradient-to-br from-red-400 via-pink-400 to-red-500 rounded-full shadow-lg shadow-red-500/60 animate-pulse"></div>
                <div className="absolute inset-0 w-5 h-5 bg-red-400/60 rounded-full animate-ping"></div>
                <div className="absolute inset-1 w-3 h-3 bg-gradient-to-br from-red-200 to-pink-200 rounded-full"></div>
                <div className="absolute top-1 left-1 w-1 h-1 bg-white/80 rounded-full"></div>
              </div>

              {/* Sophisticated Typography */}
              <div className="flex flex-col">
                <span className="text-xl font-black bg-gradient-to-r from-red-200 via-pink-100 to-red-200 bg-clip-text text-transparent tracking-wide">
                  ĮRAŠOMA
                </span>
                <span className="text-sm text-red-200/70 font-medium tracking-wider">
                  LIVE RECORDING
                </span>
              </div>
            </div>

            {/* Animated Waveform Visualization */}
            <div className="flex items-center space-x-1 ml-4">
              <div className="w-1 bg-red-300/60 rounded-full animate-pulse" style={{ height: '12px', animationDelay: '0ms' }}></div>
              <div className="w-1 bg-red-300/80 rounded-full animate-pulse" style={{ height: '20px', animationDelay: '150ms' }}></div>
              <div className="w-1 bg-red-300/60 rounded-full animate-pulse" style={{ height: '16px', animationDelay: '300ms' }}></div>
              <div className="w-1 bg-red-300/90 rounded-full animate-pulse" style={{ height: '24px', animationDelay: '450ms' }}></div>
              <div className="w-1 bg-red-300/70 rounded-full animate-pulse" style={{ height: '14px', animationDelay: '600ms' }}></div>
            </div>

            {/* Floating Light Particles */}
            <div className="absolute top-1 right-3 w-1 h-1 bg-red-300/60 rounded-full animate-ping"></div>
            <div className="absolute bottom-2 left-4 w-0.5 h-0.5 bg-pink-300/40 rounded-full animate-pulse"></div>
          </div>

          {/* Subtle Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-pink-500/10 rounded-2xl blur-xl animate-pulse"></div>
        </div>
      )}
    </div>
  );
};