{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingIndicator.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Mic, MicOff, Pause, Circle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const RecordingIndicator = ({\n  recordingState\n}) => {\n  _s();\n  const {\n    isRecording,\n    isPaused,\n    duration,\n    audioLevel\n  } = recordingState;\n  const [displayTime, setDisplayTime] = useState('00:00');\n  useEffect(() => {\n    const formatTime = seconds => {\n      const mins = Math.floor(seconds / 60);\n      const secs = Math.floor(seconds % 60);\n      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    };\n    setDisplayTime(formatTime(duration));\n  }, [duration]);\n  if (!isRecording) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 text-gray-500 bg-gradient-to-r from-black/40 to-slate-800/40 px-2.5 py-1.5 rounded-full border border-white/20 ring-1 ring-white/10 shadow-md backdrop-blur-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-1.5\",\n        children: [/*#__PURE__*/_jsxDEV(MicOff, {\n          className: \"h-3.5 w-3.5 text-white/70\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-medium text-white/70\",\n          children: \"Sustabdyta\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs font-mono text-white/60 ml-1\",\n        children: \"00:00\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center space-x-3 bg-gradient-to-r from-black/50 to-blue-900/40 px-3 py-1.5 rounded-full border border-white/25 ring-1 ring-white/15 shadow-lg backdrop-blur-md transition-all duration-300 hover:shadow-blue-500/10\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-1.5\",\n      children: isPaused ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center w-5 h-5 bg-gradient-to-br from-yellow-500/80 to-amber-600/80 rounded-full border border-white/30 ring-1 ring-white/15 shadow-md\",\n          children: /*#__PURE__*/_jsxDEV(Pause, {\n            className: \"h-2.5 w-2.5 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-medium text-yellow-300\",\n          children: \"Pristabdyta\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex items-center justify-center w-5 h-5\",\n          children: [/*#__PURE__*/_jsxDEV(Circle, {\n            className: \"h-3.5 w-3.5 text-red-500 fill-current animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 rounded-full bg-red-500/30 animate-ping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 rounded-full bg-gradient-to-r from-red-500/40 to-red-600/40 blur-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs font-medium text-red-300\",\n          children: \"\\u012Era\\u0161oma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-2.5 py-1 bg-gradient-to-r from-black/60 to-blue-900/50 rounded-full border border-white/10 shadow-md\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-xs font-mono font-medium text-white/90\",\n        children: displayTime\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), !isPaused && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-1.5\",\n      children: [/*#__PURE__*/_jsxDEV(Mic, {\n        className: \"h-3.5 w-3.5 text-white/70\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-end space-x-0.5 h-3\",\n        children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-0.5 rounded-full transition-all duration-100 ${audioLevel > (i + 1) * 0.2 ? 'bg-blue-400 h-full' : 'bg-white/20 h-0.5'}`\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(RecordingIndicator, \"YPLZE1MiXiTdBYax6Fia79Mdwys=\");\n_c = RecordingIndicator;\nvar _c;\n$RefreshReg$(_c, \"RecordingIndicator\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Mic", "<PERSON><PERSON><PERSON><PERSON>", "Pause", "Circle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RecordingIndicator", "recordingState", "_s", "isRecording", "isPaused", "duration", "audioLevel", "displayTime", "setDisplayTime", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingIndicator.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Mic, MicOff, Pause, Circle } from 'lucide-react';\nimport { RecordingState } from '../types/meeting';\n\ninterface RecordingIndicatorProps {\n  recordingState: RecordingState;\n}\n\nexport const RecordingIndicator: React.FC<RecordingIndicatorProps> = ({\n  recordingState,\n}) => {\n  const { isRecording, isPaused, duration, audioLevel } = recordingState;\n  const [displayTime, setDisplayTime] = useState('00:00');\n\n  useEffect(() => {\n    const formatTime = (seconds: number) => {\n      const mins = Math.floor(seconds / 60);\n      const secs = Math.floor(seconds % 60);\n      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    };\n\n    setDisplayTime(formatTime(duration));\n  }, [duration]);\n\n  if (!isRecording) {\n    return (\n      <div className=\"flex items-center space-x-2 text-gray-500 bg-gradient-to-r from-black/40 to-slate-800/40 px-2.5 py-1.5 rounded-full border border-white/20 ring-1 ring-white/10 shadow-md backdrop-blur-md\">\n        <div className=\"flex items-center space-x-1.5\">\n          <MicOff className=\"h-3.5 w-3.5 text-white/70\" />\n          <span className=\"text-xs font-medium text-white/70\">Sustabdyta</span>\n        </div>\n        <div className=\"text-xs font-mono text-white/60 ml-1\">00:00</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center space-x-3 bg-gradient-to-r from-black/50 to-blue-900/40 px-3 py-1.5 rounded-full border border-white/25 ring-1 ring-white/15 shadow-lg backdrop-blur-md transition-all duration-300 hover:shadow-blue-500/10\">\n      {/* Status Indicator */}\n      <div className=\"flex items-center space-x-1.5\">\n        {isPaused ? (\n          <>\n            <div className=\"flex items-center justify-center w-5 h-5 bg-gradient-to-br from-yellow-500/80 to-amber-600/80 rounded-full border border-white/30 ring-1 ring-white/15 shadow-md\">\n              <Pause className=\"h-2.5 w-2.5 text-white\" />\n            </div>\n            <span className=\"text-xs font-medium text-yellow-300\">Pristabdyta</span>\n          </>\n        ) : (\n          <>\n            <div className=\"relative flex items-center justify-center w-5 h-5\">\n              <Circle className=\"h-3.5 w-3.5 text-red-500 fill-current animate-pulse\" />\n              <div className=\"absolute inset-0 rounded-full bg-red-500/30 animate-ping\" />\n              <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-red-500/40 to-red-600/40 blur-sm\" />\n            </div>\n            <span className=\"text-xs font-medium text-red-300\">Įrašoma</span>\n          </>\n        )}\n      </div>\n\n      {/* Time Display */}\n      <div className=\"px-2.5 py-1 bg-gradient-to-r from-black/60 to-blue-900/50 rounded-full border border-white/10 shadow-md\">\n        <span className=\"text-xs font-mono font-medium text-white/90\">{displayTime}</span>\n      </div>\n\n      {/* Audio Level Indicator */}\n      {!isPaused && (\n        <div className=\"flex items-center space-x-1.5\">\n          <Mic className=\"h-3.5 w-3.5 text-white/70\" />\n          <div className=\"flex items-end space-x-0.5 h-3\">\n            {[...Array(5)].map((_, i) => (\n              <div\n                key={i}\n                className={`w-0.5 rounded-full transition-all duration-100 ${\n                  audioLevel > (i + 1) * 0.2\n                    ? 'bg-blue-400 h-full'\n                    : 'bg-white/20 h-0.5'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAO1D,OAAO,MAAMC,kBAAqD,GAAGA,CAAC;EACpEC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC;EAAW,CAAC,GAAGL,cAAc;EACtE,MAAM,CAACM,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,OAAO,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACd,MAAMmB,UAAU,GAAIC,OAAe,IAAK;MACtC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;MACrC,MAAMI,IAAI,GAAGF,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;MACrC,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAClF,CAAC;IAEDR,cAAc,CAACC,UAAU,CAACJ,QAAQ,CAAC,CAAC;EACtC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,IAAI,CAACF,WAAW,EAAE;IAChB,oBACEN,OAAA;MAAKoB,SAAS,EAAC,4LAA4L;MAAAC,QAAA,gBACzMrB,OAAA;QAAKoB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CrB,OAAA,CAACJ,MAAM;UAACwB,SAAS,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDzB,OAAA;UAAMoB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC;EAEV;EAEA,oBACEzB,OAAA;IAAKoB,SAAS,EAAC,gOAAgO;IAAAC,QAAA,gBAE7OrB,OAAA;MAAKoB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,EAC3Cd,QAAQ,gBACPP,OAAA,CAAAE,SAAA;QAAAmB,QAAA,gBACErB,OAAA;UAAKoB,SAAS,EAAC,kKAAkK;UAAAC,QAAA,eAC/KrB,OAAA,CAACH,KAAK;YAACuB,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNzB,OAAA;UAAMoB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eACxE,CAAC,gBAEHzB,OAAA,CAAAE,SAAA;QAAAmB,QAAA,gBACErB,OAAA;UAAKoB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChErB,OAAA,CAACF,MAAM;YAACsB,SAAS,EAAC;UAAqD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1EzB,OAAA;YAAKoB,SAAS,EAAC;UAA0D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5EzB,OAAA;YAAKoB,SAAS,EAAC;UAAsF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG,CAAC,eACNzB,OAAA;UAAMoB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eACjE;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,yGAAyG;MAAAC,QAAA,eACtHrB,OAAA;QAAMoB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,EAAEX;MAAW;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,EAGL,CAAClB,QAAQ,iBACRP,OAAA;MAAKoB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5CrB,OAAA,CAACL,GAAG;QAACyB,SAAS,EAAC;MAA2B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CzB,OAAA;QAAKoB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAC5C,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB7B,OAAA;UAEEoB,SAAS,EAAE,kDACTX,UAAU,GAAG,CAACoB,CAAC,GAAG,CAAC,IAAI,GAAG,GACtB,oBAAoB,GACpB,mBAAmB;QACtB,GALEA,CAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpB,EAAA,CA5EWF,kBAAqD;AAAA2B,EAAA,GAArD3B,kBAAqD;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}