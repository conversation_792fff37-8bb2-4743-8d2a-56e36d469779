{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\MeetingsList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Clock, FileText, Trash2, Mic, Square, Loader2, List, CheckCircle } from 'lucide-react';\nimport Button from './Button';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const MeetingsList = ({\n  meetings,\n  onSelectMeeting,\n  onDeleteMeeting,\n  activeView,\n  onViewTranscription,\n  onViewResults\n}) => {\n  _s();\n  const [expandedMeeting, setExpandedMeeting] = useState(null);\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'recording':\n        return /*#__PURE__*/_jsxDEV(Mic, {\n          className: \"h-3 w-3 text-red-500 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 16\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(Loader2, {\n          className: \"h-3 w-3 text-blue-500 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"h-3 w-3 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(Square, {\n          className: \"h-3 w-3 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"h-3 w-3 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'recording':\n        return 'Įrašoma';\n      case 'processing':\n        return 'Apdorojama';\n      case 'completed':\n        return 'Baigta';\n      case 'error':\n        return 'Klaida';\n      default:\n        return 'Nežinoma';\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'recording':\n        return 'text-red-500';\n      case 'processing':\n        return 'text-blue-500';\n      case 'completed':\n        return 'text-green-500';\n      case 'error':\n        return 'text-red-500';\n      default:\n        return 'text-gray-400';\n    }\n  };\n  if (meetings.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl p-6 h-full transition-smooth hover:shadow-primary float-effect\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Pokalbiai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center h-64 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mb-3 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft\",\n          children: /*#__PURE__*/_jsxDEV(FileText, {\n            className: \"h-6 w-6 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"N\\u0117ra pokalbi\\u0173\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500\",\n          children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col animate-fade-in\",\n    children: meetings.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 overflow-y-auto\",\n      children: meetings.map((meeting, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4 hover:bg-white/10 transition-all duration-300 cursor-pointer transform hover:scale-[1.01] animate-fade-in-up shadow-lg hover:shadow-blue-500/10\",\n        style: {\n          animationDelay: `${index * 50}ms`\n        },\n        onClick: () => onSelectMeeting(meeting),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-base font-semibold text-white truncate transition-colors duration-200 mb-1.5 leading-tight\",\n              children: meeting.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-white/60 mb-2.5 transition-colors duration-200 font-medium\",\n              children: meeting.date.toLocaleString('lt-LT')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 mb-2.5 text-xs\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1.5 text-white/50\",\n                children: [/*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"h-3.5 w-3.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-mono\",\n                  children: [Math.floor(meeting.duration / 60), \":\", String(meeting.duration % 60).padStart(2, '0')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1.5\",\n                children: [getStatusIcon(meeting.transcriptionStatus.state), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `transition-colors duration-200 text-xs font-medium ${getStatusColor(meeting.transcriptionStatus.state)}`,\n                  children: getStatusText(meeting.transcriptionStatus.state)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 19\n            }, this), meeting.transcript && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2.5 text-xs text-green-400/80 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(FileText, {\n                className: \"h-3.5 w-3.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [meeting.transcript.length, \" segment\\u0173\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 23\n              }, this), meeting.participants && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/30\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: [meeting.participants.length, \" dalyvi\\u0173\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => onViewTranscription(meeting),\n              icon: /*#__PURE__*/_jsxDEV(FileText, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 27\n              }, this),\n              variant: \"secondary\",\n              size: \"sm\",\n              children: \"Transkriptas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => onViewResults(meeting),\n              icon: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 27\n              }, this),\n              variant: \"primary\",\n              size: \"sm\",\n              children: \"Rezultatai\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              onDeleteMeeting(meeting.id);\n            },\n            className: \"p-2 text-white/30 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 ml-3 transform hover:scale-110 flex-shrink-0\",\n            title: \"I\\u0161trinti pokalb\\u012F\",\n            children: /*#__PURE__*/_jsxDEV(Trash2, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 15\n        }, this)\n      }, meeting.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-20 h-20 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full flex items-center justify-center animate-pulse\",\n        children: /*#__PURE__*/_jsxDEV(List, {\n          className: \"h-10 w-10 text-indigo-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl sm:text-2xl font-semibold text-white\",\n          children: \"N\\u0117ra pokalbi\\u0173\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg text-white/70\",\n          children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(MeetingsList, \"xl+1qxd3RRxKYfNdGPK+7eGKlQc=\");\n_c = MeetingsList;\nvar _c;\n$RefreshReg$(_c, \"MeetingsList\");", "map": {"version": 3, "names": ["React", "useState", "Clock", "FileText", "Trash2", "Mic", "Square", "Loader2", "List", "CheckCircle", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MeetingsList", "meetings", "onSelectMeeting", "onDeleteMeeting", "activeView", "onViewTranscription", "onViewResults", "_s", "expandedMeeting", "setExpandedMeeting", "formatDuration", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "getStatusIcon", "status", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusText", "getStatusColor", "length", "children", "map", "meeting", "index", "style", "animationDelay", "onClick", "title", "date", "toLocaleString", "duration", "String", "transcriptionStatus", "state", "transcript", "participants", "icon", "variant", "size", "e", "stopPropagation", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/MeetingsList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Calendar, Clock, FileText, Trash2, Download, Mic, Square, Loader2, Volume2, List, CheckCircle } from 'lucide-react';\nimport { Meeting } from '../types/meeting';\nimport { AudioPlayer } from './AudioPlayer';\nimport But<PERSON> from './Button';\n\ninterface MeetingsListProps {\n  meetings: Meeting[];\n  currentMeeting: Meeting | null;\n  onSelectMeeting: (meeting: Meeting) => void;\n  onDeleteMeeting: (meetingId: string) => void;\n  onExportMeeting: (meeting: Meeting) => void;\n  isRecording?: boolean;\n  activeView: 'list' | 'grid';\n  onViewTranscription: (meeting: Meeting) => void;\n  onViewResults: (meeting: Meeting) => void;\n}\n\nexport const MeetingsList: React.FC<MeetingsListProps> = ({\n  meetings,\n  onSelectMeeting,\n  onDeleteMeeting,\n  activeView,\n  onViewTranscription,\n  onViewResults,\n}) => {\n  const [expandedMeeting, setExpandedMeeting] = useState<string | null>(null);\n  const formatDuration = (seconds: number): string => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'recording':\n        return <Mic className=\"h-3 w-3 text-red-500 animate-pulse\" />;\n      case 'processing':\n        return <Loader2 className=\"h-3 w-3 text-blue-500 animate-spin\" />;\n      case 'completed':\n        return <FileText className=\"h-3 w-3 text-green-500\" />;\n      case 'error':\n        return <Square className=\"h-3 w-3 text-red-500\" />;\n      default:\n        return <FileText className=\"h-3 w-3 text-gray-400\" />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'recording':\n        return 'Įrašoma';\n      case 'processing':\n        return 'Apdorojama';\n      case 'completed':\n        return 'Baigta';\n      case 'error':\n        return 'Klaida';\n      default:\n        return 'Nežinoma';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'recording':\n        return 'text-red-500';\n      case 'processing':\n        return 'text-blue-500';\n      case 'completed':\n        return 'text-green-500';\n      case 'error':\n        return 'text-red-500';\n      default:\n        return 'text-gray-400';\n    }\n  };\n\n  if (meetings.length === 0) {\n    return (\n      <div className=\"gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl p-6 h-full transition-smooth hover:shadow-primary float-effect\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Pokalbiai</h2>\n        </div>\n        <div className=\"flex flex-col items-center justify-center h-64 text-center\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mb-3 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft\">\n            <FileText className=\"h-6 w-6 text-gray-400\" />\n          </div>\n          <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Nėra pokalbių</h3>\n          <p className=\"text-xs text-gray-500\">Pradėkite naują pokalbį, kad pamatytumėte jį čia</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col animate-fade-in\">\n      {meetings.length > 0 ? (\n        <div className=\"space-y-4 overflow-y-auto\">\n          {meetings.map((meeting, index) => (\n            <div\n              key={meeting.id}\n              className=\"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4 hover:bg-white/10 transition-all duration-300 cursor-pointer transform hover:scale-[1.01] animate-fade-in-up shadow-lg hover:shadow-blue-500/10\"\n              style={{ animationDelay: `${index * 50}ms` }}\n              onClick={() => onSelectMeeting(meeting)}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <h4 className=\"text-base font-semibold text-white truncate transition-colors duration-200 mb-1.5 leading-tight\">{meeting.title}</h4>\n                  <p className=\"text-sm text-white/60 mb-2.5 transition-colors duration-200 font-medium\">\n                    {meeting.date.toLocaleString('lt-LT')}\n                  </p>\n                  <div className=\"flex items-center space-x-3 mb-2.5 text-xs\">\n                    <div className=\"flex items-center space-x-1.5 text-white/50\">\n                      <Clock className=\"h-3.5 w-3.5\" />\n                      <span className=\"font-mono\">\n                        {Math.floor(meeting.duration / 60)}:{String(meeting.duration % 60).padStart(2, '0')}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center space-x-1.5\">\n                      {getStatusIcon(meeting.transcriptionStatus.state)}\n                      <span className={`transition-colors duration-200 text-xs font-medium ${getStatusColor(meeting.transcriptionStatus.state)}`}>\n                        {getStatusText(meeting.transcriptionStatus.state)}\n                      </span>\n                    </div>\n                  </div>\n                  {meeting.transcript && (\n                    <div className=\"flex items-center space-x-2.5 text-xs text-green-400/80 transition-all duration-200\">\n                      <FileText className=\"h-3.5 w-3.5\" />\n                      <span className=\"font-medium\">{meeting.transcript.length} segmentų</span>\n                      {meeting.participants && (\n                        <>\n                          <span className=\"text-white/30\">•</span>\n                          <span className=\"font-medium\">{meeting.participants.length} dalyvių</span>\n                        </>\n                      )}\n                    </div>\n                  )}\n                </div>\n                {/* Veiksmų mygtukai */}\n                <div className=\"flex gap-2\">\n                  <Button\n                    onClick={() => onViewTranscription(meeting)}\n                    icon={<FileText className=\"h-4 w-4\" />}\n                    variant=\"secondary\"\n                    size=\"sm\"\n                  >\n                    Transkriptas\n                  </Button>\n                  <Button\n                    onClick={() => onViewResults(meeting)}\n                    icon={<CheckCircle className=\"h-4 w-4\" />}\n                    variant=\"primary\"\n                    size=\"sm\"\n                  >\n                    Rezultatai\n                  </Button>\n                </div>\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    onDeleteMeeting(meeting.id);\n                  }}\n                  className=\"p-2 text-white/30 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 ml-3 transform hover:scale-110 flex-shrink-0\"\n                  title=\"Ištrinti pokalbį\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up\">\n          <div className=\"w-20 h-20 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full flex items-center justify-center animate-pulse\">\n            <List className=\"h-10 w-10 text-indigo-400\" />\n          </div>\n          <div>\n            <h3 className=\"text-xl sm:text-2xl font-semibold text-white\">Nėra pokalbių</h3>\n            <p className=\"text-base sm:text-lg text-white/70\">\n              Pradėkite naują pokalbį, kad pamatytumėte jį čia\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAAmBC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAYC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAWC,IAAI,EAAEC,WAAW,QAAQ,cAAc;AAG5H,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAc9B,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EACxDC,QAAQ;EACRC,eAAe;EACfC,eAAe;EACfC,UAAU;EACVC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAMwB,cAAc,GAAIC,OAAe,IAAa;IAClD,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,MAAMC,aAAa,GAAIC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,oBAAOtB,OAAA,CAACP,GAAG;UAAC8B,SAAS,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D,KAAK,YAAY;QACf,oBAAO3B,OAAA,CAACL,OAAO;UAAC4B,SAAS,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE,KAAK,WAAW;QACd,oBAAO3B,OAAA,CAACT,QAAQ;UAACgC,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD,KAAK,OAAO;QACV,oBAAO3B,OAAA,CAACN,MAAM;UAAC6B,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD;QACE,oBAAO3B,OAAA,CAACT,QAAQ;UAACgC,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,aAAa,GAAIN,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,YAAY;MACrB,KAAK,WAAW;QACd,OAAO,QAAQ;MACjB,KAAK,OAAO;QACV,OAAO,QAAQ;MACjB;QACE,OAAO,UAAU;IACrB;EACF,CAAC;EAED,MAAMO,cAAc,GAAIP,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,cAAc;MACvB,KAAK,YAAY;QACf,OAAO,eAAe;MACxB,KAAK,WAAW;QACd,OAAO,gBAAgB;MACzB,KAAK,OAAO;QACV,OAAO,cAAc;MACvB;QACE,OAAO,eAAe;IAC1B;EACF,CAAC;EAED,IAAIlB,QAAQ,CAAC0B,MAAM,KAAK,CAAC,EAAE;IACzB,oBACE9B,OAAA;MAAKuB,SAAS,EAAC,uJAAuJ;MAAAQ,QAAA,gBACpK/B,OAAA;QAAKuB,SAAS,EAAC,wCAAwC;QAAAQ,QAAA,eACrD/B,OAAA;UAAIuB,SAAS,EAAC,qCAAqC;UAAAQ,QAAA,EAAC;QAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACN3B,OAAA;QAAKuB,SAAS,EAAC,4DAA4D;QAAAQ,QAAA,gBACzE/B,OAAA;UAAKuB,SAAS,EAAC,gNAAgN;UAAAQ,QAAA,eAC7N/B,OAAA,CAACT,QAAQ;YAACgC,SAAS,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACN3B,OAAA;UAAIuB,SAAS,EAAC,wCAAwC;UAAAQ,QAAA,EAAC;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE3B,OAAA;UAAGuB,SAAS,EAAC,uBAAuB;UAAAQ,QAAA,EAAC;QAAgD;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3B,OAAA;IAAKuB,SAAS,EAAC,sCAAsC;IAAAQ,QAAA,EAClD3B,QAAQ,CAAC0B,MAAM,GAAG,CAAC,gBAClB9B,OAAA;MAAKuB,SAAS,EAAC,2BAA2B;MAAAQ,QAAA,EACvC3B,QAAQ,CAAC4B,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BlC,OAAA;QAEEuB,SAAS,EAAC,mNAAmN;QAC7NY,KAAK,EAAE;UAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,EAAE;QAAK,CAAE;QAC7CG,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC4B,OAAO,CAAE;QAAAF,QAAA,eAExC/B,OAAA;UAAKuB,SAAS,EAAC,kCAAkC;UAAAQ,QAAA,gBAC/C/B,OAAA;YAAKuB,SAAS,EAAC,gBAAgB;YAAAQ,QAAA,gBAC7B/B,OAAA;cAAIuB,SAAS,EAAC,iGAAiG;cAAAQ,QAAA,EAAEE,OAAO,CAACK;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpI3B,OAAA;cAAGuB,SAAS,EAAC,yEAAyE;cAAAQ,QAAA,EACnFE,OAAO,CAACM,IAAI,CAACC,cAAc,CAAC,OAAO;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACJ3B,OAAA;cAAKuB,SAAS,EAAC,4CAA4C;cAAAQ,QAAA,gBACzD/B,OAAA;gBAAKuB,SAAS,EAAC,6CAA6C;gBAAAQ,QAAA,gBAC1D/B,OAAA,CAACV,KAAK;kBAACiC,SAAS,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC3B,OAAA;kBAAMuB,SAAS,EAAC,WAAW;kBAAAQ,QAAA,GACxBf,IAAI,CAACC,KAAK,CAACgB,OAAO,CAACQ,QAAQ,GAAG,EAAE,CAAC,EAAC,GAAC,EAACC,MAAM,CAACT,OAAO,CAACQ,QAAQ,GAAG,EAAE,CAAC,CAACrB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN3B,OAAA;gBAAKuB,SAAS,EAAC,+BAA+B;gBAAAQ,QAAA,GAC3CV,aAAa,CAACY,OAAO,CAACU,mBAAmB,CAACC,KAAK,CAAC,eACjD5C,OAAA;kBAAMuB,SAAS,EAAE,sDAAsDM,cAAc,CAACI,OAAO,CAACU,mBAAmB,CAACC,KAAK,CAAC,EAAG;kBAAAb,QAAA,EACxHH,aAAa,CAACK,OAAO,CAACU,mBAAmB,CAACC,KAAK;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLM,OAAO,CAACY,UAAU,iBACjB7C,OAAA;cAAKuB,SAAS,EAAC,qFAAqF;cAAAQ,QAAA,gBAClG/B,OAAA,CAACT,QAAQ;gBAACgC,SAAS,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpC3B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAQ,QAAA,GAAEE,OAAO,CAACY,UAAU,CAACf,MAAM,EAAC,gBAAS;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACxEM,OAAO,CAACa,YAAY,iBACnB9C,OAAA,CAAAE,SAAA;gBAAA6B,QAAA,gBACE/B,OAAA;kBAAMuB,SAAS,EAAC,eAAe;kBAAAQ,QAAA,EAAC;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC3B,OAAA;kBAAMuB,SAAS,EAAC,aAAa;kBAAAQ,QAAA,GAAEE,OAAO,CAACa,YAAY,CAAChB,MAAM,EAAC,eAAQ;gBAAA;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eAC1E,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN3B,OAAA;YAAKuB,SAAS,EAAC,YAAY;YAAAQ,QAAA,gBACzB/B,OAAA,CAACF,MAAM;cACLuC,OAAO,EAAEA,CAAA,KAAM7B,mBAAmB,CAACyB,OAAO,CAAE;cAC5Cc,IAAI,eAAE/C,OAAA,CAACT,QAAQ;gBAACgC,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvCqB,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,IAAI;cAAAlB,QAAA,EACV;YAED;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3B,OAAA,CAACF,MAAM;cACLuC,OAAO,EAAEA,CAAA,KAAM5B,aAAa,CAACwB,OAAO,CAAE;cACtCc,IAAI,eAAE/C,OAAA,CAACH,WAAW;gBAAC0B,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1CqB,OAAO,EAAC,SAAS;cACjBC,IAAI,EAAC,IAAI;cAAAlB,QAAA,EACV;YAED;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN3B,OAAA;YACEqC,OAAO,EAAGa,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB7C,eAAe,CAAC2B,OAAO,CAACmB,EAAE,CAAC;YAC7B,CAAE;YACF7B,SAAS,EAAC,8IAA8I;YACxJe,KAAK,EAAC,4BAAkB;YAAAP,QAAA,eAExB/B,OAAA,CAACR,MAAM;cAAC+B,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC,GAnEDM,OAAO,CAACmB,EAAE;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoEZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAEN3B,OAAA;MAAKuB,SAAS,EAAC,2FAA2F;MAAAQ,QAAA,gBACxG/B,OAAA;QAAKuB,SAAS,EAAC,6HAA6H;QAAAQ,QAAA,eAC1I/B,OAAA,CAACJ,IAAI;UAAC2B,SAAS,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACN3B,OAAA;QAAA+B,QAAA,gBACE/B,OAAA;UAAIuB,SAAS,EAAC,8CAA8C;UAAAQ,QAAA,EAAC;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/E3B,OAAA;UAAGuB,SAAS,EAAC,oCAAoC;UAAAQ,QAAA,EAAC;QAElD;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjB,EAAA,CAzKWP,YAAyC;AAAAkD,EAAA,GAAzClD,YAAyC;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}