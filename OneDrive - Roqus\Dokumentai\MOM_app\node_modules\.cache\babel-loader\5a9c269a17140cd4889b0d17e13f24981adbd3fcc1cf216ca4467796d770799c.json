{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\Button.tsx\";\nimport React from 'react';\nimport clsx from 'clsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  icon,\n  loading = false,\n  fullWidth = false,\n  className,\n  disabled,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  const variantClasses = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 shadow-sm hover:shadow-md focus:ring-blue-500',\n    secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300 hover:border-gray-400'\n  };\n  const sizeClasses = {\n    sm: 'px-3 py-2 text-sm gap-1.5',\n    md: 'px-4 py-3 text-sm gap-2',\n    lg: 'px-4 py-3 text-base gap-2.5'\n  };\n  const widthClasses = fullWidth ? 'w-full' : '';\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: clsx(baseClasses, variantClasses[variant], sizeClasses[size], widthClasses, className),\n    disabled: disabled || loading,\n    ...props,\n    children: [loading && /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"animate-spin h-4 w-4\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        className: \"opacity-25\",\n        cx: \"12\",\n        cy: \"12\",\n        r: \"10\",\n        stroke: \"currentColor\",\n        strokeWidth: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        className: \"opacity-75\",\n        fill: \"currentColor\",\n        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this), !loading && icon && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"flex-shrink-0\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 28\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "clsx", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "variant", "size", "icon", "loading", "fullWidth", "className", "disabled", "props", "baseClasses", "variantClasses", "primary", "secondary", "sizeClasses", "sm", "md", "lg", "widthClasses", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Button.tsx"], "sourcesContent": ["import React from 'react';\r\nimport clsx from 'clsx';\r\n\r\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  children: React.ReactNode;\r\n  variant?: 'primary' | 'secondary';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  icon?: React.ReactNode;\r\n  loading?: boolean;\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  icon,\r\n  loading = false,\r\n  fullWidth = false,\r\n  className,\r\n  disabled,\r\n  ...props\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 shadow-sm hover:shadow-md focus:ring-blue-500',\r\n    secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300 hover:border-gray-400'\r\n  };\r\n\r\n  const sizeClasses = {\r\n    sm: 'px-3 py-2 text-sm gap-1.5',\r\n    md: 'px-4 py-3 text-sm gap-2',\r\n    lg: 'px-4 py-3 text-base gap-2.5'\r\n  };\r\n\r\n  const widthClasses = fullWidth ? 'w-full' : '';\r\n\r\n  return (\r\n    <button\r\n      className={clsx(\r\n        baseClasses,\r\n        variantClasses[variant],\r\n        sizeClasses[size],\r\n        widthClasses,\r\n        className\r\n      )}\r\n      disabled={disabled || loading}\r\n      {...props}\r\n    >\r\n      {loading && (\r\n        <svg className=\"animate-spin h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n        </svg>\r\n      )}\r\n      {!loading && icon && <span className=\"flex-shrink-0\">{icon}</span>}\r\n      <span>{children}</span>\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default Button; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWxB,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,QAAQ;EACRC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,IAAI;EACJC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG,KAAK;EACjBC,SAAS;EACTC,QAAQ;EACR,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG,gMAAgM;EAEpN,MAAMC,cAAc,GAAG;IACrBC,OAAO,EAAE,+IAA+I;IACxJC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,2BAA2B;IAC/BC,EAAE,EAAE,yBAAyB;IAC7BC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,YAAY,GAAGZ,SAAS,GAAG,QAAQ,GAAG,EAAE;EAE9C,oBACEP,OAAA;IACEQ,SAAS,EAAEV,IAAI,CACba,WAAW,EACXC,cAAc,CAACT,OAAO,CAAC,EACvBY,WAAW,CAACX,IAAI,CAAC,EACjBe,YAAY,EACZX,SACF,CAAE;IACFC,QAAQ,EAAEA,QAAQ,IAAIH,OAAQ;IAAA,GAC1BI,KAAK;IAAAR,QAAA,GAERI,OAAO,iBACNN,OAAA;MAAKQ,SAAS,EAAC,sBAAsB;MAACY,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAAAnB,QAAA,gBACnEF,OAAA;QAAQQ,SAAS,EAAC,YAAY;QAACc,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACrG9B,OAAA;QAAMQ,SAAS,EAAC,YAAY;QAACY,IAAI,EAAC,cAAc;QAACW,CAAC,EAAC;MAAiH;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzK,CACN,EACA,CAACxB,OAAO,IAAID,IAAI,iBAAIL,OAAA;MAAMQ,SAAS,EAAC,eAAe;MAAAN,QAAA,EAAEG;IAAI;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAClE9B,OAAA;MAAAE,QAAA,EAAOA;IAAQ;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEb,CAAC;AAACE,EAAA,GAhDI/B,MAA6B;AAkDnC,eAAeA,MAAM;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}