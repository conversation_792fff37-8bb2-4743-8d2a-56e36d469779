import React, { useState, useMemo } from 'react';
import { Edit3, Download, Copy, Clock, Users, BarChart3, FileText, Loader2, CheckCircle, Calendar, Mic, Trash2, Headphones, Zap } from 'lucide-react';
import { Meeting, TranscriptSegment, Speaker, MeetingMetadata } from '../types/meeting';
import { AudioPlayer } from './AudioPlayer';

interface ProfessionalTranscriptViewerProps {
  meetings: Meeting[];
  onDeleteMeeting: (meetingId: string) => void;
  onGoToTranscription?: () => void;
}

const SPEAKER_COLORS = [
  'bg-blue-100 text-blue-800 border-blue-200',
  'bg-green-100 text-green-800 border-green-200',
  'bg-purple-100 text-purple-800 border-purple-200',
  'bg-orange-100 text-orange-800 border-orange-200',
  'bg-pink-100 text-pink-800 border-pink-200',
  'bg-indigo-100 text-indigo-800 border-indigo-200',
];

export const ProfessionalTranscriptViewer: React.FC<ProfessionalTranscriptViewerProps> = ({
  meetings,
  onDeleteMeeting,
  onGoToTranscription,
}) => {
  const completedMeetings = meetings.filter(m => 
    m.transcriptionStatus.state === 'completed' && m.transcript
  );

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const hrs = Math.floor(mins / 60);
    const remainingMins = mins % 60;
    
    if (hrs > 0) {
      return `${hrs}:${remainingMins.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    }
    return `${mins}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  return (
    <div className="flex-1 flex flex-col animate-fade-in">
      {completedMeetings.length > 0 ? (
        <div className="space-y-8 overflow-y-auto">
          {completedMeetings.map((meeting, meetingIndex) => (
            <div
              key={meeting.id}
              className="bg-white/5 backdrop-blur-md border border-white/20 rounded-xl p-5 animate-fade-in-up shadow-lg hover:shadow-blue-500/10 transition-all duration-300"
              style={{ animationDelay: `${meetingIndex * 100}ms` }}
            >
              <div className="flex items-center justify-between mb-5">
                <div>
                  <h3 className="text-lg sm:text-xl font-bold text-white transition-colors duration-200 mb-1.5 leading-tight">{meeting.title}</h3>
                  <p className="text-sm sm:text-base text-white/60 transition-colors duration-200 font-medium">
                    {meeting.date.toLocaleString('lt-LT')} • {formatDuration(meeting.duration)}s
                  </p>
                </div>
                <button
                  onClick={() => onDeleteMeeting(meeting.id)}
                  className="p-2 text-white/30 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110 flex-shrink-0"
                  title="Ištrinti pokalbį"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
              
              {meeting.transcript && (
                <div className="space-y-3">
                  {meeting.transcript.map((segment, index) => (
                    <div
                      key={index}
                      className="bg-white/5 rounded-lg p-3.5 hover:bg-white/8 transition-all duration-300 transform hover:scale-[1.005] animate-fade-in-up border border-white/10"
                      style={{ animationDelay: `${(meetingIndex * 100) + (index * 20)}ms` }}
                    >
                      <div className="flex items-start space-x-3">
                        {meeting.participants && segment.speaker && (
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500/80 to-indigo-600/80 rounded-full flex items-center justify-center text-white text-xs font-medium transition-all duration-200 hover:scale-110 border border-white/20">
                              {segment.speaker}
                            </div>
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2.5 mb-1.5">
                            {meeting.participants && segment.speaker && (
                              <span className="text-sm font-semibold text-blue-400 transition-colors duration-200">
                                {segment.speaker}
                              </span>
                            )}
                            <span className="text-xs text-white/40 transition-colors duration-200 font-mono">
                              {formatTime(segment.timestamp)} - {formatTime(segment.endTimestamp || segment.timestamp)}
                            </span>
                          </div>
                          <p className="text-sm text-white/75 leading-relaxed transition-colors duration-200">{segment.text}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up">
          <div className="w-20 h-20 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full flex items-center justify-center animate-pulse">
            <Headphones className="h-10 w-10 text-green-400" />
          </div>
          <div>
            <h3 className="text-xl sm:text-2xl font-semibold text-white">Nėra transkribuotų pokalbių</h3>
            <p className="text-base sm:text-lg text-white/70 mb-6">
              Transkribuokite pokalbį, kad pamatytumėte rezultatus čia
            </p>
            {onGoToTranscription && (
              <button
                onClick={onGoToTranscription}
                className="inline-flex items-center space-x-2 px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-purple-500/80 via-purple-600/70 to-pink-600/80 hover:from-purple-500/90 hover:via-purple-600/80 hover:to-pink-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-white/30 ring-1 ring-white/15 hover:border-purple-300/50 transform hover:scale-105"
              >
                <Zap className="h-5 w-5" />
                <span>Eiti į transkribavimą</span>
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}; 