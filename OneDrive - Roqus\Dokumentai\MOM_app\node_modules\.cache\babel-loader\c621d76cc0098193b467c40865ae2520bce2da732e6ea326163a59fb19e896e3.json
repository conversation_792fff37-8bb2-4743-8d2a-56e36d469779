{"ast": null, "code": "import React from'react';import clsx from'clsx';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Button=_ref=>{let{children,variant='primary',size='md',icon,loading=false,fullWidth=false,className,disabled,...props}=_ref;const baseClasses='inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';const variantClasses={primary:'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md',secondary:'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300 hover:border-gray-400'};const sizeClasses={sm:'px-2.5 py-1.5 text-sm gap-1.5',md:'px-3 py-2 text-sm gap-2',lg:'px-4 py-2.5 text-base gap-2.5'};const widthClasses=fullWidth?'w-full':'';return/*#__PURE__*/_jsxs(\"button\",{className:clsx(baseClasses,variantClasses[variant],sizeClasses[size],widthClasses,className),disabled:disabled||loading,...props,children:[loading&&/*#__PURE__*/_jsxs(\"svg\",{className:\"animate-spin h-4 w-4\",fill:\"none\",viewBox:\"0 0 24 24\",children:[/*#__PURE__*/_jsx(\"circle\",{className:\"opacity-25\",cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"4\"}),/*#__PURE__*/_jsx(\"path\",{className:\"opacity-75\",fill:\"currentColor\",d:\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"})]}),!loading&&icon&&/*#__PURE__*/_jsx(\"span\",{className:\"flex-shrink-0\",children:icon}),/*#__PURE__*/_jsx(\"span\",{children:children})]});};export default Button;", "map": {"version": 3, "names": ["React", "clsx", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "_ref", "children", "variant", "size", "icon", "loading", "fullWidth", "className", "disabled", "props", "baseClasses", "variantClasses", "primary", "secondary", "sizeClasses", "sm", "md", "lg", "widthClasses", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Button.tsx"], "sourcesContent": ["import React from 'react';\r\nimport clsx from 'clsx';\r\n\r\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  children: React.ReactNode;\r\n  variant?: 'primary' | 'secondary';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  icon?: React.ReactNode;\r\n  loading?: boolean;\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  icon,\r\n  loading = false,\r\n  fullWidth = false,\r\n  className,\r\n  disabled,\r\n  ...props\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md',\r\n    secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300 hover:border-gray-400'\r\n  };\r\n\r\n  const sizeClasses = {\r\n    sm: 'px-2.5 py-1.5 text-sm gap-1.5',\r\n    md: 'px-3 py-2 text-sm gap-2',\r\n    lg: 'px-4 py-2.5 text-base gap-2.5'\r\n  };\r\n\r\n  const widthClasses = fullWidth ? 'w-full' : '';\r\n\r\n  return (\r\n    <button\r\n      className={clsx(\r\n        baseClasses,\r\n        variantClasses[variant],\r\n        sizeClasses[size],\r\n        widthClasses,\r\n        className\r\n      )}\r\n      disabled={disabled || loading}\r\n      {...props}\r\n    >\r\n      {loading && (\r\n        <svg className=\"animate-spin h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n        </svg>\r\n      )}\r\n      {!loading && icon && <span className=\"flex-shrink-0\">{icon}</span>}\r\n      <span>{children}</span>\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default Button; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,IAAI,KAAM,MAAM,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWxB,KAAM,CAAAC,MAA6B,CAAGC,IAAA,EAUhC,IAViC,CACrCC,QAAQ,CACRC,OAAO,CAAG,SAAS,CACnBC,IAAI,CAAG,IAAI,CACXC,IAAI,CACJC,OAAO,CAAG,KAAK,CACfC,SAAS,CAAG,KAAK,CACjBC,SAAS,CACTC,QAAQ,CACR,GAAGC,KACL,CAAC,CAAAT,IAAA,CACC,KAAM,CAAAU,WAAW,CAAG,gMAAgM,CAEpN,KAAM,CAAAC,cAAc,CAAG,CACrBC,OAAO,CAAE,wFAAwF,CACjGC,SAAS,CAAE,8GACb,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,CAClBC,EAAE,CAAE,+BAA+B,CACnCC,EAAE,CAAE,yBAAyB,CAC7BC,EAAE,CAAE,+BACN,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGZ,SAAS,CAAG,QAAQ,CAAG,EAAE,CAE9C,mBACER,KAAA,WACES,SAAS,CAAEb,IAAI,CACbgB,WAAW,CACXC,cAAc,CAACT,OAAO,CAAC,CACvBY,WAAW,CAACX,IAAI,CAAC,CACjBe,YAAY,CACZX,SACF,CAAE,CACFC,QAAQ,CAAEA,QAAQ,EAAIH,OAAQ,IAC1BI,KAAK,CAAAR,QAAA,EAERI,OAAO,eACNP,KAAA,QAAKS,SAAS,CAAC,sBAAsB,CAACY,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAAAnB,QAAA,eACnEL,IAAA,WAAQW,SAAS,CAAC,YAAY,CAACc,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAS,CAAC,cACrG7B,IAAA,SAAMW,SAAS,CAAC,YAAY,CAACY,IAAI,CAAC,cAAc,CAACO,CAAC,CAAC,iHAAiH,CAAO,CAAC,EACzK,CACN,CACA,CAACrB,OAAO,EAAID,IAAI,eAAIR,IAAA,SAAMW,SAAS,CAAC,eAAe,CAAAN,QAAA,CAAEG,IAAI,CAAO,CAAC,cAClER,IAAA,SAAAK,QAAA,CAAOA,QAAQ,CAAO,CAAC,EACjB,CAAC,CAEb,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}