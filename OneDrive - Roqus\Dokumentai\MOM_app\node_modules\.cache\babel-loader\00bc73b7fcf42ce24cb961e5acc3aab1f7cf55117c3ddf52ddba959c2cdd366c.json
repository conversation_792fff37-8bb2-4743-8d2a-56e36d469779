{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Mic2, Headphones, Settings, Play, Square, Plus, List, BarChart3, Edit, Trash2, Calendar, X, Check } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RecordingPage = ({\n  recordingState,\n  currentMeeting,\n  onStartRecording,\n  onStopRecording,\n  onPauseRecording,\n  onResumeRecording\n}) => {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: '<PERSON>siruo<PERSON><PERSON> prezentacijai',\n    completed: false,\n    date: '2024-01-20',\n    priority: 'high'\n  }, {\n    id: 2,\n    text: 'Susitikimas su klientu',\n    completed: true,\n    date: '2024-01-18',\n    priority: 'medium'\n  }, {\n    id: 3,\n    text: 'Peržiūrėti dokumentus',\n    completed: false,\n    date: '2024-01-22',\n    priority: 'low'\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false,\n    priority: 'medium'\n  }]);\n  const [recordedConversations, setRecordedConversations] = useState([{\n    id: 1,\n    title: 'Susitikimas su komanda',\n    duration: '15:30',\n    date: '2024-01-15'\n  }, {\n    id: 2,\n    title: 'Klientų aptarimas',\n    duration: '22:15',\n    date: '2024-01-14'\n  }, {\n    id: 3,\n    title: 'Projekto planavimas',\n    duration: '18:45',\n    date: '2024-01-13'\n  }]);\n\n  // TODO editing states\n  const [editingTodo, setEditingTodo] = useState(null);\n  const [editingText, setEditingText] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState('medium');\n  const handleStartRecording = async () => {\n    setIsRecording(true);\n    setRecordingTime(0);\n    // Simulate recording time\n    const interval = setInterval(() => {\n      setRecordingTime(prev => prev + 1);\n    }, 1000);\n\n    // Store interval for cleanup\n    window.recordingInterval = interval;\n  };\n  const handleStopRecording = () => {\n    setIsRecording(false);\n    if (window.recordingInterval) {\n      clearInterval(window.recordingInterval);\n    }\n    // Add new recording to list\n    const newRecording = {\n      id: Date.now(),\n      title: `Pokalbis ${new Date().toLocaleString('lt-LT')}`,\n      duration: `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`,\n      date: new Date().toISOString().split('T')[0]\n    };\n    setRecordedConversations(prev => [newRecording, ...prev]);\n    setRecordingTime(0);\n  };\n  const toggleTodo = id => {\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const deleteTodo = id => {\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n  const startEditingTodo = todo => {\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n  };\n  const saveEditedTodo = () => {\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => todo.id === editingTodo ? {\n        ...todo,\n        text: editingText.trim()\n      } : todo));\n      setEditingTodo(null);\n      setEditingText('');\n    }\n  };\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n  };\n  const addNewTodo = () => {\n    if (newTodoText.trim()) {\n      const newTodo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority\n      };\n      setTodos(prev => [...prev, newTodo]);\n      setNewTodoText('');\n      setNewTodoDate('');\n      setNewTodoPriority('medium');\n      setShowNewTodoForm(false);\n    }\n  };\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-400';\n      case 'medium':\n        return 'text-yellow-400';\n      case 'low':\n        return 'text-green-400';\n      default:\n        return 'text-white/60';\n    }\n  };\n  const getPriorityBg = priority => {\n    switch (priority) {\n      case 'high':\n        return 'bg-red-500/20 border-red-500/30';\n      case 'medium':\n        return 'bg-yellow-500/20 border-yellow-500/30';\n      case 'low':\n        return 'bg-green-500/20 border-green-500/30';\n      default:\n        return 'bg-white/5 border-white/10';\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",\n          children: /*#__PURE__*/_jsxDEV(Mic2, {\n            className: \"h-6 w-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-white\",\n            children: \"Pokalbi\\u0173 \\u012Fra\\u0161ymas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-white/60\",\n            children: \"Profesionalus garso \\u012Fra\\u0161ymas su automatine transkribavimo technologija\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Mic2, {\n            className: \"h-5 w-5 text-blue-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"Pokalbi\\u0173 \\u012Fra\\u0161ymas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-medium\",\n                children: \"\\u012Era\\u0161ymo statusas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-white mb-2\",\n              children: formatTime(recordingTime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/60 text-sm\",\n              children: isRecording ? 'Įrašoma...' : 'Pasiruošta įrašymui'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: !isRecording ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStartRecording,\n              className: \"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStopRecording,\n              className: \"flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-red-600 hover:to-pink-600 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(Square, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this), \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), currentMeeting && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-medium mb-2\",\n              children: \"Dabartinis pokalbis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/80 text-sm\",\n              children: currentMeeting.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/60 text-xs mt-1\",\n              children: [\"Prad\\u0117tas: \", currentMeeting.date.toLocaleTimeString('lt-LT')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(List, {\n              className: \"h-5 w-5 text-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-white\",\n              children: \"Susitikimo u\\u017Eduotys\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNewTodoForm(true),\n            className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\",\n            children: /*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), showNewTodoForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-4 bg-white/5 rounded-xl border border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoText,\n              onChange: e => setNewTodoText(e.target.value),\n              placeholder: \"\\u012Eveskite u\\u017Eduot\\u012F...\",\n              className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\",\n              onKeyPress: e => e.key === 'Enter' && addNewTodo()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: newTodoDate,\n                onChange: e => setNewTodoDate(e.target.value),\n                className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newTodoPriority,\n                onChange: e => setNewTodoPriority(e.target.value),\n                className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"low\",\n                  children: \"\\u017Demas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"medium\",\n                  children: \"Vidutinis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high\",\n                  children: \"Auk\\u0161tas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: addNewTodo,\n                className: \"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(Check, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this), \"Prid\\u0117ti\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: cancelNewTodo,\n                className: \"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(X, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), \"At\\u0161aukti\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: todos.map(todo => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-3 rounded-xl border transition-all duration-200 ${getPriorityBg(todo.priority)}`,\n            children: editingTodo === todo.id ?\n            /*#__PURE__*/\n            // Editing mode\n            _jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: editingText,\n                onChange: e => setEditingText(e.target.value),\n                className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\",\n                onKeyPress: e => e.key === 'Enter' && saveEditedTodo()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: saveEditedTodo,\n                  className: \"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(Check, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 25\n                  }, this), \"I\\u0161saugoti\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: cancelEditing,\n                  className: \"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(X, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 25\n                  }, this), \"At\\u0161aukti\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            // View mode\n            _jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-5 h-5 rounded border-2 flex items-center justify-center cursor-pointer ${todo.completed ? 'bg-green-500 border-green-500' : 'border-white/30'}`,\n                onClick: () => toggleTodo(todo.id),\n                children: todo.completed && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-white rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-white ${todo.completed ? 'line-through text-white/50' : ''}`,\n                  children: todo.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this), todo.date && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                    className: \"h-3 w-3 text-white/40\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white/40 text-xs\",\n                    children: todo.date\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`,\n                  children: todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => startEditingTodo(todo),\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Edit, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => deleteTodo(todo.id),\n                  className: \"p-1 text-white/60 hover:text-red-400 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 19\n            }, this)\n          }, todo.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: [\"U\\u017Ebaigta: \", todos.filter(t => t.completed).length, \" i\\u0161 \", todos.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Headphones, {\n            className: \"h-5 w-5 text-purple-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"\\u012Era\\u0161yti pokalbiai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recordedConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-medium text-sm truncate\",\n                children: conversation.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-xs\",\n                children: conversation.duration\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-xs\",\n                children: conversation.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this)]\n          }, conversation.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: [\"I\\u0161 viso: \", recordedConversations.length, \" pokalbi\\u0173\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"h-5 w-5 text-orange-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"Analitika\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 rounded-xl p-4 border border-white/10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-white\",\n                children: recordedConversations.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"\\u012Era\\u0161yti pokalbiai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 rounded-xl p-4 border border-white/10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-white\",\n                children: recordedConversations.reduce((total, conv) => {\n                  const [mins, secs] = conv.duration.split(':').map(Number);\n                  return total + mins * 60 + secs;\n                }, 0) / 60 | 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"Minut\\u0117s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"U\\u017Eduo\\u010Di\\u0173 progresas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: [Math.round(todos.filter(t => t.completed).length / todos.length * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${todos.filter(t => t.completed).length / todos.length * 100}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"\\u012Era\\u0161ymo kokyb\\u0117\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: \"95%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-500 h-2 rounded-full\",\n                  style: {\n                    width: '95%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"Transkribavimo tikslumas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: \"98%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-500 h-2 rounded-full\",\n                  style: {\n                    width: '98%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n};\n_s(RecordingPage, \"pekb8g387+FytGAEjADzEt4q9EA=\");\n_c = RecordingPage;\nexport default RecordingPage;\nvar _c;\n$RefreshReg$(_c, \"RecordingPage\");", "map": {"version": 3, "names": ["React", "useState", "Mic2", "Headphones", "Settings", "Play", "Square", "Plus", "List", "BarChart3", "Edit", "Trash2", "Calendar", "X", "Check", "jsxDEV", "_jsxDEV", "RecordingPage", "recordingState", "currentMeeting", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "_s", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "date", "priority", "recordedConversations", "setRecordedConversations", "title", "duration", "editingTodo", "setEditingTodo", "editingText", "setEditingText", "newTodoText", "setNewTodoText", "showNewTodoForm", "setShowNewTodoForm", "newTodoDate", "setNewTodoDate", "newTodoPriority", "setNewTodoPriority", "handleStartRecording", "interval", "setInterval", "prev", "window", "recordingInterval", "handleStopRecording", "clearInterval", "newRecording", "Date", "now", "toLocaleString", "Math", "floor", "toString", "padStart", "toISOString", "split", "toggleTodo", "map", "todo", "deleteTodo", "filter", "startEditingTodo", "saveEditedTodo", "trim", "cancelEditing", "addNewTodo", "newTodo", "undefined", "cancelNewTodo", "getPriorityColor", "getPriorityBg", "formatTime", "seconds", "mins", "secs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "toLocaleTimeString", "type", "value", "onChange", "e", "target", "placeholder", "onKeyPress", "key", "t", "length", "conversation", "reduce", "total", "conv", "Number", "round", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Mic2, Zap, Headphones, Settings, Play, Pause, Square, Plus, List, BarChart3, Edit, Trash2, Calendar, X, Check } from 'lucide-react';\r\nimport { RecordingPanel } from './index';\r\nimport { RecordingState, Meeting } from '../types/meeting';\r\n\r\ninterface Todo {\r\n  id: number;\r\n  text: string;\r\n  completed: boolean;\r\n  date?: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n}\r\n\r\ninterface RecordingPageProps {\r\n  recordingState: RecordingState;\r\n  currentMeeting: Meeting | null;\r\n  onStartRecording: (title: string) => Promise<void>;\r\n  onStopRecording: () => Promise<void>;\r\n  onPauseRecording: () => void;\r\n  onResumeRecording: () => void;\r\n}\r\n\r\nconst RecordingPage: React.FC<RecordingPageProps> = ({\r\n  recordingState,\r\n  currentMeeting,\r\n  onStartRecording,\r\n  onStopRecording,\r\n  onPauseRecording,\r\n  onResumeRecording\r\n}) => {\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [todos, setTodos] = useState<Todo[]>([\r\n    { id: 1, text: '<PERSON><PERSON><PERSON><PERSON><PERSON> prezentacijai', completed: false, date: '2024-01-20', priority: 'high' },\r\n    { id: 2, text: 'Susitikimas su klientu', completed: true, date: '2024-01-18', priority: 'medium' },\r\n    { id: 3, text: 'Peržiūrėti dokumentus', completed: false, date: '2024-01-22', priority: 'low' },\r\n    { id: 4, text: 'Planuoti kitą savaitę', completed: false, priority: 'medium' }\r\n  ]);\r\n  const [recordedConversations, setRecordedConversations] = useState([\r\n    { id: 1, title: 'Susitikimas su komanda', duration: '15:30', date: '2024-01-15' },\r\n    { id: 2, title: 'Klientų aptarimas', duration: '22:15', date: '2024-01-14' },\r\n    { id: 3, title: 'Projekto planavimas', duration: '18:45', date: '2024-01-13' }\r\n  ]);\r\n\r\n  // TODO editing states\r\n  const [editingTodo, setEditingTodo] = useState<number | null>(null);\r\n  const [editingText, setEditingText] = useState('');\r\n  const [newTodoText, setNewTodoText] = useState('');\r\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\r\n  const [newTodoDate, setNewTodoDate] = useState('');\r\n  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');\r\n\r\n  const handleStartRecording = async () => {\r\n    setIsRecording(true);\r\n    setRecordingTime(0);\r\n    // Simulate recording time\r\n    const interval = setInterval(() => {\r\n      setRecordingTime(prev => prev + 1);\r\n    }, 1000);\r\n    \r\n    // Store interval for cleanup\r\n    (window as any).recordingInterval = interval;\r\n  };\r\n\r\n  const handleStopRecording = () => {\r\n    setIsRecording(false);\r\n    if ((window as any).recordingInterval) {\r\n      clearInterval((window as any).recordingInterval);\r\n    }\r\n    // Add new recording to list\r\n    const newRecording = {\r\n      id: Date.now(),\r\n      title: `Pokalbis ${new Date().toLocaleString('lt-LT')}`,\r\n      duration: `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`,\r\n      date: new Date().toISOString().split('T')[0]\r\n    };\r\n    setRecordedConversations(prev => [newRecording, ...prev]);\r\n    setRecordingTime(0);\r\n  };\r\n\r\n  const toggleTodo = (id: number) => {\r\n    setTodos(prev => prev.map(todo => \r\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\r\n    ));\r\n  };\r\n\r\n  const deleteTodo = (id: number) => {\r\n    setTodos(prev => prev.filter(todo => todo.id !== id));\r\n  };\r\n\r\n  const startEditingTodo = (todo: Todo) => {\r\n    setEditingTodo(todo.id);\r\n    setEditingText(todo.text);\r\n  };\r\n\r\n  const saveEditedTodo = () => {\r\n    if (editingTodo && editingText.trim()) {\r\n      setTodos(prev => prev.map(todo => \r\n        todo.id === editingTodo ? { ...todo, text: editingText.trim() } : todo\r\n      ));\r\n      setEditingTodo(null);\r\n      setEditingText('');\r\n    }\r\n  };\r\n\r\n  const cancelEditing = () => {\r\n    setEditingTodo(null);\r\n    setEditingText('');\r\n  };\r\n\r\n  const addNewTodo = () => {\r\n    if (newTodoText.trim()) {\r\n      const newTodo: Todo = {\r\n        id: Date.now(),\r\n        text: newTodoText.trim(),\r\n        completed: false,\r\n        date: newTodoDate || undefined,\r\n        priority: newTodoPriority\r\n      };\r\n      setTodos(prev => [...prev, newTodo]);\r\n      setNewTodoText('');\r\n      setNewTodoDate('');\r\n      setNewTodoPriority('medium');\r\n      setShowNewTodoForm(false);\r\n    }\r\n  };\r\n\r\n  const cancelNewTodo = () => {\r\n    setShowNewTodoForm(false);\r\n    setNewTodoText('');\r\n    setNewTodoDate('');\r\n    setNewTodoPriority('medium');\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'text-red-400';\r\n      case 'medium': return 'text-yellow-400';\r\n      case 'low': return 'text-green-400';\r\n      default: return 'text-white/60';\r\n    }\r\n  };\r\n\r\n  const getPriorityBg = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'bg-red-500/20 border-red-500/30';\r\n      case 'medium': return 'bg-yellow-500/20 border-yellow-500/30';\r\n      case 'low': return 'bg-green-500/20 border-green-500/30';\r\n      default: return 'bg-white/5 border-white/10';\r\n    }\r\n  };\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\r\n            <Mic2 className=\"h-6 w-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-white\">Pokalbių įrašymas</h2>\r\n            <p className=\"text-sm text-white/60\">Profesionalus garso įrašymas su automatine transkribavimo technologija</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 2x2 Grid Layout */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        \r\n        {/* Top-Left: Pokalbių įrašymo komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <Mic2 className=\"h-5 w-5 text-blue-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Pokalbių įrašymas</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-4\">\r\n            {/* Recording Status */}\r\n            <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <span className=\"text-white font-medium\">Įrašymo statusas</span>\r\n                <div className={`w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>\r\n              </div>\r\n              <div className=\"text-2xl font-bold text-white mb-2\">\r\n                {formatTime(recordingTime)}\r\n              </div>\r\n              <div className=\"text-white/60 text-sm\">\r\n                {isRecording ? 'Įrašoma...' : 'Pasiruošta įrašymui'}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Recording Controls */}\r\n            <div className=\"flex gap-3\">\r\n              {!isRecording ? (\r\n                <button\r\n                  onClick={handleStartRecording}\r\n                  className=\"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200\"\r\n                >\r\n                  <Play className=\"h-4 w-4\" />\r\n                  Pradėti įrašymą\r\n                </button>\r\n              ) : (\r\n                <button\r\n                  onClick={handleStopRecording}\r\n                  className=\"flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-red-600 hover:to-pink-600 transition-all duration-200\"\r\n                >\r\n                  <Square className=\"h-4 w-4\" />\r\n                  Sustabdyti įrašymą\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Current Meeting Info */}\r\n            {currentMeeting && (\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n                <div className=\"text-white font-medium mb-2\">Dabartinis pokalbis</div>\r\n                <div className=\"text-white/80 text-sm\">{currentMeeting.title}</div>\r\n                <div className=\"text-white/60 text-xs mt-1\">\r\n                  Pradėtas: {currentMeeting.date.toLocaleTimeString('lt-LT')}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Top-Right: Susitikimo užduotys - TODOS list komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <List className=\"h-5 w-5 text-green-400\" />\r\n              <h3 className=\"text-lg font-semibold text-white\">Susitikimo užduotys</h3>\r\n            </div>\r\n            <button\r\n              onClick={() => setShowNewTodoForm(true)}\r\n              className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\"\r\n            >\r\n              <Plus className=\"h-4 w-4 text-white\" />\r\n            </button>\r\n          </div>\r\n          \r\n          {/* New Todo Form */}\r\n          {showNewTodoForm && (\r\n            <div className=\"mb-4 p-4 bg-white/5 rounded-xl border border-white/10\">\r\n              <div className=\"space-y-3\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoText}\r\n                  onChange={(e) => setNewTodoText(e.target.value)}\r\n                  placeholder=\"Įveskite užduotį...\"\r\n                  className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                  onKeyPress={(e) => e.key === 'Enter' && addNewTodo()}\r\n                />\r\n                <div className=\"flex gap-2\">\r\n                  <input\r\n                    type=\"date\"\r\n                    value={newTodoDate}\r\n                    onChange={(e) => setNewTodoDate(e.target.value)}\r\n                    className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                  />\r\n                  <select\r\n                    value={newTodoPriority}\r\n                    onChange={(e) => setNewTodoPriority(e.target.value as 'low' | 'medium' | 'high')}\r\n                    className=\"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                  >\r\n                    <option value=\"low\">Žemas</option>\r\n                    <option value=\"medium\">Vidutinis</option>\r\n                    <option value=\"high\">Aukštas</option>\r\n                  </select>\r\n                </div>\r\n                <div className=\"flex gap-2\">\r\n                  <button\r\n                    onClick={addNewTodo}\r\n                    className=\"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                  >\r\n                    <Check className=\"h-4 w-4\" />\r\n                    Pridėti\r\n                  </button>\r\n                  <button\r\n                    onClick={cancelNewTodo}\r\n                    className=\"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                  >\r\n                    <X className=\"h-4 w-4\" />\r\n                    Atšaukti\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          \r\n          <div className=\"space-y-3\">\r\n            {todos.map((todo) => (\r\n              <div\r\n                key={todo.id}\r\n                className={`p-3 rounded-xl border transition-all duration-200 ${getPriorityBg(todo.priority)}`}\r\n              >\r\n                {editingTodo === todo.id ? (\r\n                  // Editing mode\r\n                  <div className=\"space-y-3\">\r\n                    <input\r\n                      type=\"text\"\r\n                      value={editingText}\r\n                      onChange={(e) => setEditingText(e.target.value)}\r\n                      className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                      onKeyPress={(e) => e.key === 'Enter' && saveEditedTodo()}\r\n                    />\r\n                    <div className=\"flex gap-2\">\r\n                      <button\r\n                        onClick={saveEditedTodo}\r\n                        className=\"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                      >\r\n                        <Check className=\"h-4 w-4\" />\r\n                        Išsaugoti\r\n                      </button>\r\n                      <button\r\n                        onClick={cancelEditing}\r\n                        className=\"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                      >\r\n                        <X className=\"h-4 w-4\" />\r\n                        Atšaukti\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  // View mode\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <div \r\n                      className={`w-5 h-5 rounded border-2 flex items-center justify-center cursor-pointer ${\r\n                        todo.completed \r\n                          ? 'bg-green-500 border-green-500' \r\n                          : 'border-white/30'\r\n                      }`}\r\n                      onClick={() => toggleTodo(todo.id)}\r\n                    >\r\n                      {todo.completed && <div className=\"w-2 h-2 bg-white rounded-full\"></div>}\r\n                    </div>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <span className={`text-white ${todo.completed ? 'line-through text-white/50' : ''}`}>\r\n                        {todo.text}\r\n                      </span>\r\n                      {todo.date && (\r\n                        <div className=\"flex items-center gap-1 mt-1\">\r\n                          <Calendar className=\"h-3 w-3 text-white/40\" />\r\n                          <span className=\"text-white/40 text-xs\">{todo.date}</span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex items-center gap-1\">\r\n                      <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`}>\r\n                        {todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'}\r\n                      </span>\r\n                      <button\r\n                        onClick={() => startEditingTodo(todo)}\r\n                        className=\"p-1 text-white/60 hover:text-white transition-colors\"\r\n                      >\r\n                        <Edit className=\"h-3 w-3\" />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => deleteTodo(todo.id)}\r\n                        className=\"p-1 text-white/60 hover:text-red-400 transition-colors\"\r\n                      >\r\n                        <Trash2 className=\"h-3 w-3\" />\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ))}\r\n          </div>\r\n          \r\n          <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n            <div className=\"text-white/60 text-sm\">\r\n              Užbaigta: {todos.filter(t => t.completed).length} iš {todos.length}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom-Left: Įrašyti pokalbiai komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <Headphones className=\"h-5 w-5 text-purple-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Įrašyti pokalbiai</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-3\">\r\n            {recordedConversations.map((conversation) => (\r\n              <div\r\n                key={conversation.id}\r\n                className=\"p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\"\r\n              >\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <div className=\"text-white font-medium text-sm truncate\">\r\n                    {conversation.title}\r\n                  </div>\r\n                  <div className=\"text-white/60 text-xs\">\r\n                    {conversation.duration}\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"text-white/60 text-xs\">\r\n                    {conversation.date}\r\n                  </div>\r\n                  <div className=\"flex gap-2\">\r\n                    <button className=\"p-1 text-white/60 hover:text-white transition-colors\">\r\n                      <Play className=\"h-3 w-3\" />\r\n                    </button>\r\n                    <button className=\"p-1 text-white/60 hover:text-white transition-colors\">\r\n                      <Settings className=\"h-3 w-3\" />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          \r\n          <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n            <div className=\"text-white/60 text-sm\">\r\n              Iš viso: {recordedConversations.length} pokalbių\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom-Right: Analitikos komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <BarChart3 className=\"h-5 w-5 text-orange-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Analitika</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-4\">\r\n            {/* Recording Stats */}\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10 text-center\">\r\n                <div className=\"text-2xl font-bold text-white\">{recordedConversations.length}</div>\r\n                <div className=\"text-white/60 text-sm\">Įrašyti pokalbiai</div>\r\n              </div>\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10 text-center\">\r\n                <div className=\"text-2xl font-bold text-white\">\r\n                  {recordedConversations.reduce((total, conv) => {\r\n                    const [mins, secs] = conv.duration.split(':').map(Number);\r\n                    return total + mins * 60 + secs;\r\n                  }, 0) / 60 | 0}\r\n                </div>\r\n                <div className=\"text-white/60 text-sm\">Minutės</div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Progress Bars */}\r\n            <div className=\"space-y-3\">\r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Užduočių progresas</span>\r\n                  <span className=\"text-white/60 text-sm\">\r\n                    {Math.round((todos.filter(t => t.completed).length / todos.length) * 100)}%\r\n                  </span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div \r\n                    className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\r\n                    style={{ width: `${(todos.filter(t => t.completed).length / todos.length) * 100}%` }}\r\n                  ></div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Įrašymo kokybė</span>\r\n                  <span className=\"text-white/60 text-sm\">95%</span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{ width: '95%' }}></div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Transkribavimo tikslumas</span>\r\n                  <span className=\"text-white/60 text-sm\">98%</span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div className=\"bg-purple-500 h-2 rounded-full\" style={{ width: '98%' }}></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RecordingPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAOC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,CAAC,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAqB7I,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,cAAc;EACdC,cAAc;EACdC,gBAAgB;EAChBC,eAAe;EACfC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAS,CACzC;IAAE8B,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE;EAAO,CAAC,EACnG;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE;EAAS,CAAC,EAClG;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE;EAAM,CAAC,EAC/F;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEE,QAAQ,EAAE;EAAS,CAAC,CAC/E,CAAC;EACF,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpC,QAAQ,CAAC,CACjE;IAAE8B,EAAE,EAAE,CAAC;IAAEO,KAAK,EAAE,wBAAwB;IAAEC,QAAQ,EAAE,OAAO;IAAEL,IAAI,EAAE;EAAa,CAAC,EACjF;IAAEH,EAAE,EAAE,CAAC;IAAEO,KAAK,EAAE,mBAAmB;IAAEC,QAAQ,EAAE,OAAO;IAAEL,IAAI,EAAE;EAAa,CAAC,EAC5E;IAAEH,EAAE,EAAE,CAAC;IAAEO,KAAK,EAAE,qBAAqB;IAAEC,QAAQ,EAAE,OAAO;IAAEL,IAAI,EAAE;EAAa,CAAC,CAC/E,CAAC;;EAEF;EACA,MAAM,CAACM,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAA4B,QAAQ,CAAC;EAE3F,MAAMmD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC1B,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,CAAC,CAAC;IACnB;IACA,MAAMyB,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC1B,gBAAgB,CAAC2B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC;;IAER;IACCC,MAAM,CAASC,iBAAiB,GAAGJ,QAAQ;EAC9C,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChChC,cAAc,CAAC,KAAK,CAAC;IACrB,IAAK8B,MAAM,CAASC,iBAAiB,EAAE;MACrCE,aAAa,CAAEH,MAAM,CAASC,iBAAiB,CAAC;IAClD;IACA;IACA,MAAMG,YAAY,GAAG;MACnB7B,EAAE,EAAE8B,IAAI,CAACC,GAAG,CAAC,CAAC;MACdxB,KAAK,EAAE,YAAY,IAAIuB,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC,EAAE;MACvDxB,QAAQ,EAAE,GAAGyB,IAAI,CAACC,KAAK,CAACtC,aAAa,GAAG,EAAE,CAAC,IAAI,CAACA,aAAa,GAAG,EAAE,EAAEuC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACjGjC,IAAI,EAAE,IAAI2B,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IACDhC,wBAAwB,CAACkB,IAAI,IAAI,CAACK,YAAY,EAAE,GAAGL,IAAI,CAAC,CAAC;IACzD3B,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM0C,UAAU,GAAIvC,EAAU,IAAK;IACjCD,QAAQ,CAACyB,IAAI,IAAIA,IAAI,CAACgB,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACzC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGyC,IAAI;MAAEvC,SAAS,EAAE,CAACuC,IAAI,CAACvC;IAAU,CAAC,GAAGuC,IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAI1C,EAAU,IAAK;IACjCD,QAAQ,CAACyB,IAAI,IAAIA,IAAI,CAACmB,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACzC,EAAE,KAAKA,EAAE,CAAC,CAAC;EACvD,CAAC;EAED,MAAM4C,gBAAgB,GAAIH,IAAU,IAAK;IACvC/B,cAAc,CAAC+B,IAAI,CAACzC,EAAE,CAAC;IACvBY,cAAc,CAAC6B,IAAI,CAACxC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM4C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIpC,WAAW,IAAIE,WAAW,CAACmC,IAAI,CAAC,CAAC,EAAE;MACrC/C,QAAQ,CAACyB,IAAI,IAAIA,IAAI,CAACgB,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACzC,EAAE,KAAKS,WAAW,GAAG;QAAE,GAAGgC,IAAI;QAAExC,IAAI,EAAEU,WAAW,CAACmC,IAAI,CAAC;MAAE,CAAC,GAAGL,IACpE,CAAC,CAAC;MACF/B,cAAc,CAAC,IAAI,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC;EAED,MAAMmC,aAAa,GAAGA,CAAA,KAAM;IAC1BrC,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMoC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAInC,WAAW,CAACiC,IAAI,CAAC,CAAC,EAAE;MACtB,MAAMG,OAAa,GAAG;QACpBjD,EAAE,EAAE8B,IAAI,CAACC,GAAG,CAAC,CAAC;QACd9B,IAAI,EAAEY,WAAW,CAACiC,IAAI,CAAC,CAAC;QACxB5C,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAEc,WAAW,IAAIiC,SAAS;QAC9B9C,QAAQ,EAAEe;MACZ,CAAC;MACDpB,QAAQ,CAACyB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEyB,OAAO,CAAC,CAAC;MACpCnC,cAAc,CAAC,EAAE,CAAC;MAClBI,cAAc,CAAC,EAAE,CAAC;MAClBE,kBAAkB,CAAC,QAAQ,CAAC;MAC5BJ,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMmC,aAAa,GAAGA,CAAA,KAAM;IAC1BnC,kBAAkB,CAAC,KAAK,CAAC;IACzBF,cAAc,CAAC,EAAE,CAAC;IAClBI,cAAc,CAAC,EAAE,CAAC;IAClBE,kBAAkB,CAAC,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMgC,gBAAgB,GAAIhD,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAMiD,aAAa,GAAIjD,QAAgB,IAAK;IAC1C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,iCAAiC;MACrD,KAAK,QAAQ;QAAE,OAAO,uCAAuC;MAC7D,KAAK,KAAK;QAAE,OAAO,qCAAqC;MACxD;QAAS,OAAO,4BAA4B;IAC9C;EACF,CAAC;EAED,MAAMkD,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAGvB,IAAI,CAACC,KAAK,CAACqB,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACtB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,oBACEnD,OAAA;IAAKyE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB1E,OAAA;MAAKyE,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClF1E,OAAA;QAAKyE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC1E,OAAA;UAAKyE,SAAS,EAAC,6JAA6J;UAAAC,QAAA,eAC1K1E,OAAA,CAACd,IAAI;YAACuF,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACN9E,OAAA;UAAA0E,QAAA,gBACE1E,OAAA;YAAIyE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvE9E,OAAA;YAAGyE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9E,OAAA;MAAKyE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAGpD1E,OAAA;QAAKyE,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF1E,OAAA;UAAKyE,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C1E,OAAA,CAACd,IAAI;YAACuF,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C9E,OAAA;YAAIyE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB1E,OAAA;YAAKyE,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/D1E,OAAA;cAAKyE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD1E,OAAA;gBAAMyE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChE9E,OAAA;gBAAKyE,SAAS,EAAE,wBAAwBhE,WAAW,GAAG,0BAA0B,GAAG,aAAa;cAAG;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC,eACN9E,OAAA;cAAKyE,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDL,UAAU,CAAC1D,aAAa;YAAC;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACN9E,OAAA;cAAKyE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCjE,WAAW,GAAG,YAAY,GAAG;YAAqB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxB,CAACjE,WAAW,gBACXT,OAAA;cACE+E,OAAO,EAAE3C,oBAAqB;cAC9BqC,SAAS,EAAC,4MAA4M;cAAAC,QAAA,gBAEtN1E,OAAA,CAACX,IAAI;gBAACoF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uCAE9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAET9E,OAAA;cACE+E,OAAO,EAAErC,mBAAoB;cAC7B+B,SAAS,EAAC,sMAAsM;cAAAC,QAAA,gBAEhN1E,OAAA,CAACV,MAAM;gBAACmF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qCAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL3E,cAAc,iBACbH,OAAA;YAAKyE,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/D1E,OAAA;cAAKyE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtE9E,OAAA;cAAKyE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEvE,cAAc,CAACmB;YAAK;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnE9E,OAAA;cAAKyE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,iBAChC,EAACvE,cAAc,CAACe,IAAI,CAAC8D,kBAAkB,CAAC,OAAO,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9E,OAAA;QAAKyE,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF1E,OAAA;UAAKyE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD1E,OAAA;YAAKyE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC1E,OAAA,CAACR,IAAI;cAACiF,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C9E,OAAA;cAAIyE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACN9E,OAAA;YACE+E,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAAC,IAAI,CAAE;YACxC0C,SAAS,EAAC,0EAA0E;YAAAC,QAAA,eAEpF1E,OAAA,CAACT,IAAI;cAACkF,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLhD,eAAe,iBACd9B,OAAA;UAAKyE,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpE1E,OAAA;YAAKyE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB1E,OAAA;cACEiF,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEtD,WAAY;cACnBuD,QAAQ,EAAGC,CAAC,IAAKvD,cAAc,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDI,WAAW,EAAC,oCAAqB;cACjCb,SAAS,EAAC,yIAAyI;cACnJc,UAAU,EAAGH,CAAC,IAAKA,CAAC,CAACI,GAAG,KAAK,OAAO,IAAIzB,UAAU,CAAC;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACF9E,OAAA;cAAKyE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB1E,OAAA;gBACEiF,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAElD,WAAY;gBACnBmD,QAAQ,EAAGC,CAAC,IAAKnD,cAAc,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDT,SAAS,EAAC;cAAoH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H,CAAC,eACF9E,OAAA;gBACEkF,KAAK,EAAEhD,eAAgB;gBACvBiD,QAAQ,EAAGC,CAAC,IAAKjD,kBAAkB,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAkC,CAAE;gBACjFT,SAAS,EAAC,6GAA6G;gBAAAC,QAAA,gBAEvH1E,OAAA;kBAAQkF,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC9E,OAAA;kBAAQkF,KAAK,EAAC,QAAQ;kBAAAR,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC9E,OAAA;kBAAQkF,KAAK,EAAC,MAAM;kBAAAR,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN9E,OAAA;cAAKyE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB1E,OAAA;gBACE+E,OAAO,EAAEhB,UAAW;gBACpBU,SAAS,EAAC,iIAAiI;gBAAAC,QAAA,gBAE3I1E,OAAA,CAACF,KAAK;kBAAC2E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9E,OAAA;gBACE+E,OAAO,EAAEb,aAAc;gBACvBO,SAAS,EAAC,6HAA6H;gBAAAC,QAAA,gBAEvI1E,OAAA,CAACH,CAAC;kBAAC4E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAE3B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED9E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7D,KAAK,CAAC0C,GAAG,CAAEC,IAAI,iBACdxD,OAAA;YAEEyE,SAAS,EAAE,qDAAqDL,aAAa,CAACZ,IAAI,CAACrC,QAAQ,CAAC,EAAG;YAAAuD,QAAA,EAE9FlD,WAAW,KAAKgC,IAAI,CAACzC,EAAE;YAAA;YACtB;YACAf,OAAA;cAAKyE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1E,OAAA;gBACEiF,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAExD,WAAY;gBACnByD,QAAQ,EAAGC,CAAC,IAAKzD,cAAc,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDT,SAAS,EAAC,oHAAoH;gBAC9Hc,UAAU,EAAGH,CAAC,IAAKA,CAAC,CAACI,GAAG,KAAK,OAAO,IAAI5B,cAAc,CAAC;cAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACF9E,OAAA;gBAAKyE,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB1E,OAAA;kBACE+E,OAAO,EAAEnB,cAAe;kBACxBa,SAAS,EAAC,iIAAiI;kBAAAC,QAAA,gBAE3I1E,OAAA,CAACF,KAAK;oBAAC2E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kBAE/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9E,OAAA;kBACE+E,OAAO,EAAEjB,aAAc;kBACvBW,SAAS,EAAC,6HAA6H;kBAAAC,QAAA,gBAEvI1E,OAAA,CAACH,CAAC;oBAAC4E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAE3B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACA9E,OAAA;cAAKyE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC1E,OAAA;gBACEyE,SAAS,EAAE,4EACTjB,IAAI,CAACvC,SAAS,GACV,+BAA+B,GAC/B,iBAAiB,EACpB;gBACH8D,OAAO,EAAEA,CAAA,KAAMzB,UAAU,CAACE,IAAI,CAACzC,EAAE,CAAE;gBAAA2D,QAAA,EAElClB,IAAI,CAACvC,SAAS,iBAAIjB,OAAA;kBAAKyE,SAAS,EAAC;gBAA+B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B1E,OAAA;kBAAMyE,SAAS,EAAE,cAAcjB,IAAI,CAACvC,SAAS,GAAG,4BAA4B,GAAG,EAAE,EAAG;kBAAAyD,QAAA,EACjFlB,IAAI,CAACxC;gBAAI;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACNtB,IAAI,CAACtC,IAAI,iBACRlB,OAAA;kBAAKyE,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3C1E,OAAA,CAACJ,QAAQ;oBAAC6E,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C9E,OAAA;oBAAMyE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAElB,IAAI,CAACtC;kBAAI;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC1E,OAAA;kBAAMyE,SAAS,EAAE,kCAAkCN,gBAAgB,CAACX,IAAI,CAACrC,QAAQ,CAAC,cAAe;kBAAAuD,QAAA,EAC9FlB,IAAI,CAACrC,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAGqC,IAAI,CAACrC,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG;gBAAO;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC,eACP9E,OAAA;kBACE+E,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACH,IAAI,CAAE;kBACtCiB,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eAEhE1E,OAAA,CAACN,IAAI;oBAAC+E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACT9E,OAAA;kBACE+E,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAACD,IAAI,CAACzC,EAAE,CAAE;kBACnC0D,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,eAElE1E,OAAA,CAACL,MAAM;oBAAC8E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GAxEItB,IAAI,CAACzC,EAAE;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyET,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD1E,OAAA;YAAKyE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,iBAC3B,EAAC7D,KAAK,CAAC6C,MAAM,CAAC+B,CAAC,IAAIA,CAAC,CAACxE,SAAS,CAAC,CAACyE,MAAM,EAAC,WAAI,EAAC7E,KAAK,CAAC6E,MAAM;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9E,OAAA;QAAKyE,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF1E,OAAA;UAAKyE,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C1E,OAAA,CAACb,UAAU;YAACsF,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClD9E,OAAA;YAAIyE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBtD,qBAAqB,CAACmC,GAAG,CAAEoC,YAAY,iBACtC3F,OAAA;YAEEyE,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBAEzH1E,OAAA;cAAKyE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD1E,OAAA;gBAAKyE,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACrDiB,YAAY,CAACrE;cAAK;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCiB,YAAY,CAACpE;cAAQ;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9E,OAAA;cAAKyE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD1E,OAAA;gBAAKyE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCiB,YAAY,CAACzE;cAAI;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB1E,OAAA;kBAAQyE,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACtE1E,OAAA,CAACX,IAAI;oBAACoF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACT9E,OAAA;kBAAQyE,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACtE1E,OAAA,CAACZ,QAAQ;oBAACqF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAvBDa,YAAY,CAAC5E,EAAE;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBjB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD1E,OAAA;YAAKyE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,gBAC5B,EAACtD,qBAAqB,CAACsE,MAAM,EAAC,gBACzC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9E,OAAA;QAAKyE,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF1E,OAAA;UAAKyE,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C1E,OAAA,CAACP,SAAS;YAACgF,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjD9E,OAAA;YAAIyE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB1E,OAAA;YAAKyE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC1E,OAAA;cAAKyE,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3E1E,OAAA;gBAAKyE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAEtD,qBAAqB,CAACsE;cAAM;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnF9E,OAAA;gBAAKyE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACN9E,OAAA;cAAKyE,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3E1E,OAAA;gBAAKyE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC3CtD,qBAAqB,CAACwE,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAK;kBAC7C,MAAM,CAACvB,IAAI,EAAEC,IAAI,CAAC,GAAGsB,IAAI,CAACvE,QAAQ,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACwC,MAAM,CAAC;kBACzD,OAAOF,KAAK,GAAGtB,IAAI,GAAG,EAAE,GAAGC,IAAI;gBACjC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9E,OAAA;YAAKyE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB1E,OAAA;cAAA0E,QAAA,gBACE1E,OAAA;gBAAKyE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD1E,OAAA;kBAAMyE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjE9E,OAAA;kBAAMyE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACpC1B,IAAI,CAACgD,KAAK,CAAEnF,KAAK,CAAC6C,MAAM,CAAC+B,CAAC,IAAIA,CAAC,CAACxE,SAAS,CAAC,CAACyE,MAAM,GAAG7E,KAAK,CAAC6E,MAAM,GAAI,GAAG,CAAC,EAAC,GAC5E;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD1E,OAAA;kBACEyE,SAAS,EAAC,2DAA2D;kBACrEwB,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAIrF,KAAK,CAAC6C,MAAM,CAAC+B,CAAC,IAAIA,CAAC,CAACxE,SAAS,CAAC,CAACyE,MAAM,GAAG7E,KAAK,CAAC6E,MAAM,GAAI,GAAG;kBAAI;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAA0E,QAAA,gBACE1E,OAAA;gBAAKyE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD1E,OAAA;kBAAMyE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7D9E,OAAA;kBAAMyE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD1E,OAAA;kBAAKyE,SAAS,EAAC,8BAA8B;kBAACwB,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAA0E,QAAA,gBACE1E,OAAA;gBAAKyE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD1E,OAAA;kBAAMyE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvE9E,OAAA;kBAAMyE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD1E,OAAA;kBAAKyE,SAAS,EAAC,gCAAgC;kBAACwB,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CAzdIP,aAA2C;AAAAkG,EAAA,GAA3ClG,aAA2C;AA2djD,eAAeA,aAAa;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}