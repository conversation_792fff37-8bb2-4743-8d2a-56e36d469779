{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\TodosPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Plus, Edit, Trash2, X, Check } from 'lucide-react';\nimport { Search, ChevronDown, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TodosPage = () => {\n  _s();\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: 'Pasir<PERSON><PERSON><PERSON> prezentacijai',\n    completed: false,\n    date: '2024-01-20',\n    priority: 'high',\n    project: 'Aexn CRM analitika',\n    owner: '<PERSON>'\n  }, {\n    id: 2,\n    text: 'Susitikimas su klientu',\n    completed: true,\n    date: '2024-01-18',\n    priority: 'medium',\n    project: 'Aexn | <PERSON><PERSON><PERSON>',\n    owner: '<PERSON><PERSON>'\n  }, {\n    id: 3,\n    text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokument<PERSON>',\n    completed: false,\n    date: '2024-01-22',\n    priority: 'low',\n    project: 'Aexn_analitika',\n    owner: 'Ona Onaitytė'\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false,\n    priority: 'medium',\n    project: 'Aexn_analitika',\n    owner: 'Jonas Jonaitis'\n  }]);\n  const [editingTodo, setEditingTodo] = useState(null);\n  const [editingText, setEditingText] = useState('');\n  const [editingProject, setEditingProject] = useState('');\n  const [editingOwner, setEditingOwner] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState('medium');\n  const [newTodoProject, setNewTodoProject] = useState('');\n  const [newTodoOwner, setNewTodoOwner] = useState('');\n  const [search, setSearch] = useState('');\n  const [filterProject, setFilterProject] = useState('');\n  const [collapsedProjects, setCollapsedProjects] = useState({});\n\n  // Pašalinu rūšiavimo select funkciją ir visus su ja susijusius state/kintamuosius\n\n  const toggleTodo = id => {\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const deleteTodo = id => {\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n  const startEditingTodo = todo => {\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n    setEditingProject(todo.project || '');\n    setEditingOwner(todo.owner || '');\n  };\n  const saveEditedTodo = () => {\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => todo.id === editingTodo ? {\n        ...todo,\n        text: editingText.trim(),\n        project: editingProject,\n        owner: editingOwner\n      } : todo));\n      setEditingTodo(null);\n      setEditingText('');\n      setEditingProject('');\n      setEditingOwner('');\n    }\n  };\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n    setEditingProject('');\n    setEditingOwner('');\n  };\n  const addNewTodo = () => {\n    if (newTodoText.trim()) {\n      const newTodo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority,\n        project: newTodoProject,\n        owner: newTodoOwner\n      };\n      setTodos(prev => [...prev, newTodo]);\n      setNewTodoText('');\n      setNewTodoDate('');\n      setNewTodoPriority('medium');\n      setNewTodoProject('');\n      setNewTodoOwner('');\n      setShowNewTodoForm(false);\n    }\n  };\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n    setNewTodoProject('');\n    setNewTodoOwner('');\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-400';\n      case 'medium':\n        return 'text-yellow-400';\n      case 'low':\n        return 'text-green-400';\n      default:\n        return 'text-white/60';\n    }\n  };\n  // Filtruotos ir ieškomos užduotys\n  const filteredTodos = todos.filter(todo => {\n    const matchesSearch = todo.text.toLowerCase().includes(search.toLowerCase()) || todo.owner && todo.owner.toLowerCase().includes(search.toLowerCase());\n    const matchesProject = !filterProject || todo.project === filterProject;\n    return matchesSearch && matchesProject;\n  });\n\n  // Grupavimas pagal projektą\n  const groupedTodos = filteredTodos.reduce((groups, todo) => {\n    const key = todo.project || 'Kiti projektai';\n    if (!groups[key]) groups[key] = [];\n    groups[key].push(todo);\n    return groups;\n  }, {});\n\n  // Statistika\n  const projectCount = Object.keys(groupedTodos).length;\n  const totalCount = filteredTodos.length;\n  const doneCount = filteredTodos.filter(t => t.completed).length;\n  const undoneCount = totalCount - doneCount;\n  const progress = totalCount > 0 ? Math.round(doneCount / totalCount * 100) : 0;\n\n  // Collapse/expand projektų grupes\n  const toggleCollapse = project => {\n    setCollapsedProjects(prev => ({\n      ...prev,\n      [project]: !prev[project]\n    }));\n  };\n\n  // Atlikti visas užduotis projekte\n  const completeAllInProject = project => {\n    setTodos(prev => prev.map(todo => todo.project === project ? {\n      ...todo,\n      completed: true\n    } : todo));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-6 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white text-2xl font-bold\",\n            children: projectCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Projekt\\u0173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white text-2xl font-bold\",\n            children: totalCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"U\\u017Eduo\\u010Di\\u0173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-green-400 text-2xl font-bold\",\n            children: doneCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Atlikta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-red-400 text-2xl font-bold\",\n            children: undoneCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Neatlikta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col gap-2 min-w-[200px]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Progresas:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white font-semibold\",\n            children: [progress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-white/20 rounded-full h-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full transition-all duration-500\",\n            style: {\n              width: `${progress}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col md:flex-row gap-4 items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2 items-center w-full md:w-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: search,\n          onChange: e => setSearch(e.target.value),\n          placeholder: \"Ie\\u0161koti u\\u017Eduoties ar asmens...\",\n          className: \"w-full md:w-64 pl-10 pr-4 py-2 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Search, {\n          className: \"absolute ml-3 mt-2 h-4 w-4 text-white/60 pointer-events-none\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterProject,\n          onChange: e => setFilterProject(e.target.value),\n          className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Visi projektai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), Object.keys(groupedTodos).map(project => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: project,\n            children: project\n          }, project, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowNewTodoForm(true),\n        className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), \"Prid\\u0117ti u\\u017Eduot\\u012F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: Object.entries(groupedTodos).map(([project, projectTodos]) => {\n        const projectDone = projectTodos.filter(t => t.completed).length;\n        const projectTotal = projectTodos.length;\n        const projectProgress = projectTotal > 0 ? Math.round(projectDone / projectTotal * 100) : 0;\n        const isCollapsed = collapsedProjects[project];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-2 mt-4 border-b border-white/20 pb-1 pl-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleCollapse(project),\n                className: \"focus:outline-none\",\n                children: isCollapsed ? /*#__PURE__*/_jsxDEV(ChevronRight, {\n                  className: \"h-5 w-5 text-white/60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 36\n                }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                  className: \"h-5 w-5 text-white/60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 89\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white/80 text-base font-semibold\",\n                children: project\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-xs text-white/50\",\n                children: [projectDone, \" / \", projectTotal, \" atlikta\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-24 bg-white/20 rounded-full h-2 ml-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-500\",\n                  style: {\n                    width: `${projectProgress}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => completeAllInProject(project),\n              className: \"px-3 py-1 bg-green-500/20 hover:bg-green-500/40 text-green-400 rounded-lg text-xs font-semibold transition-all duration-200\",\n              children: \"Atlikti visas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full text-sm text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Pavadinimas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Atsakingas asmuo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Prioritetas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"B\\u016Bsena\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Veiksmai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: projectTodos.map(todo => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: `border-b border-white/10 hover:bg-white/5 transition-colors cursor-pointer ${todo.completed ? 'bg-green-500/10 text-white/70' : ''}`,\n                onDoubleClick: () => startEditingTodo(todo),\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: `px-3 py-2 ${todo.completed ? 'line-through' : ''}`,\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: editingText,\n                    onChange: e => setEditingText(e.target.value),\n                    className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 27\n                  }, this) : todo.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: editingOwner,\n                    onChange: e => setEditingOwner(e.target.value),\n                    className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 27\n                  }, this) : todo.owner\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: todo.date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`,\n                    children: todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleTodo(todo.id),\n                    className: `relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none ${todo.completed ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg' : 'bg-white/20'}`,\n                    \"aria-label\": \"Pa\\u017Eym\\u0117ti kaip atlikt\\u0105\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center ${todo.completed ? 'translate-x-4 bg-green-400' : ''}`,\n                      children: todo.completed && /*#__PURE__*/_jsxDEV(Check, {\n                        className: \"h-3 w-3 text-white animate-bounceIn\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 282,\n                        columnNumber: 50\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2 flex gap-1\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: saveEditedTodo,\n                      className: \"p-1 text-green-400 hover:text-green-600\",\n                      children: /*#__PURE__*/_jsxDEV(Check, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 289,\n                        columnNumber: 116\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: cancelEditing,\n                      className: \"p-1 text-red-400 hover:text-red-600\",\n                      children: /*#__PURE__*/_jsxDEV(X, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 290,\n                        columnNumber: 111\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => startEditingTodo(todo),\n                      className: \"p-1 text-white/60 hover:text-white\",\n                      children: /*#__PURE__*/_jsxDEV(Edit, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 125\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => deleteTodo(todo.id),\n                      className: \"p-1 text-white/60 hover:text-red-400\",\n                      children: /*#__PURE__*/_jsxDEV(Trash2, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 124\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 25\n                }, this)]\n              }, todo.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this)]\n        }, project, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 pt-4 border-t border-white/10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-white/60 text-sm\",\n        children: [\"U\\u017Ebaigta: \", doneCount, \" i\\u0161 \", totalCount]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n_s(TodosPage, \"DjD6OTOKwi2Vzs3ciSkL4z9WCdQ=\");\n_c = TodosPage;\nexport default TodosPage;\nvar _c;\n$RefreshReg$(_c, \"TodosPage\");", "map": {"version": 3, "names": ["React", "useState", "Plus", "Edit", "Trash2", "X", "Check", "Search", "ChevronDown", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TodosPage", "_s", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "date", "priority", "project", "owner", "editingTodo", "setEditingTodo", "editingText", "setEditingText", "editingProject", "setEditingProject", "<PERSON><PERSON><PERSON><PERSON>", "setEditingOwner", "newTodoText", "setNewTodoText", "showNewTodoForm", "setShowNewTodoForm", "newTodoDate", "setNewTodoDate", "newTodoPriority", "setNewTodoPriority", "newTodoProject", "setNewTodoProject", "newTodoOwner", "setNewTodoOwner", "search", "setSearch", "filterProject", "setFilterProject", "collapsedProjects", "setCollapsedProjects", "toggleTodo", "prev", "map", "todo", "deleteTodo", "filter", "startEditingTodo", "saveEditedTodo", "trim", "cancelEditing", "addNewTodo", "newTodo", "Date", "now", "undefined", "cancelNewTodo", "getPriorityColor", "filteredTodos", "matchesSearch", "toLowerCase", "includes", "matchesProject", "groupedTodos", "reduce", "groups", "key", "push", "projectCount", "Object", "keys", "length", "totalCount", "doneCount", "t", "undoneCount", "progress", "Math", "round", "toggleCollapse", "completeAllInProject", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "type", "value", "onChange", "e", "target", "placeholder", "onClick", "entries", "projectTodos", "projectDone", "projectTotal", "projectProgress", "isCollapsed", "onDoubleClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TodosPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Plus, Edit, Trash2, Calendar, X, Check } from 'lucide-react';\r\nimport { Search, ChevronDown, ChevronRight } from 'lucide-react';\r\n\r\ninterface Todo {\r\n  id: number;\r\n  text: string;\r\n  completed: boolean;\r\n  date?: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n  project?: string;\r\n  owner?: string;\r\n}\r\n\r\nconst TodosPage: React.FC = () => {\r\n  const [todos, setTodos] = useState<Todo[]>([\r\n    { id: 1, text: 'Pasir<PERSON><PERSON><PERSON> prezentacijai', completed: false, date: '2024-01-20', priority: 'high', project: 'Aexn CRM analitika', owner: '<PERSON>' },\r\n    { id: 2, text: 'Susitikimas su klientu', completed: true, date: '2024-01-18', priority: 'medium', project: 'Aexn | Analitika', owner: '<PERSON><PERSON>' },\r\n    { id: 3, text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokumentus', completed: false, date: '2024-01-22', priority: 'low', project: 'Aexn_analitika', owner: '<PERSON><PERSON>' },\r\n    { id: 4, text: 'Planuoti kitą savaitę', completed: false, priority: 'medium', project: 'Aexn_analitika', owner: 'Jonas Jonaitis' }\r\n  ]);\r\n  const [editingTodo, setEditingTodo] = useState<number | null>(null);\r\n  const [editingText, setEditingText] = useState('');\r\n  const [editingProject, setEditingProject] = useState('');\r\n  const [editingOwner, setEditingOwner] = useState('');\r\n  const [newTodoText, setNewTodoText] = useState('');\r\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\r\n  const [newTodoDate, setNewTodoDate] = useState('');\r\n  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');\r\n  const [newTodoProject, setNewTodoProject] = useState('');\r\n  const [newTodoOwner, setNewTodoOwner] = useState('');\r\n  const [search, setSearch] = useState('');\r\n  const [filterProject, setFilterProject] = useState('');\r\n  const [collapsedProjects, setCollapsedProjects] = useState<Record<string, boolean>>({});\r\n\r\n  // Pašalinu rūšiavimo select funkciją ir visus su ja susijusius state/kintamuosius\r\n\r\n  const toggleTodo = (id: number) => {\r\n    setTodos(prev => prev.map(todo => \r\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\r\n    ));\r\n  };\r\n  const deleteTodo = (id: number) => {\r\n    setTodos(prev => prev.filter(todo => todo.id !== id));\r\n  };\r\n  const startEditingTodo = (todo: Todo) => {\r\n    setEditingTodo(todo.id);\r\n    setEditingText(todo.text);\r\n    setEditingProject(todo.project || '');\r\n    setEditingOwner(todo.owner || '');\r\n  };\r\n  const saveEditedTodo = () => {\r\n    if (editingTodo && editingText.trim()) {\r\n      setTodos(prev => prev.map(todo => \r\n        todo.id === editingTodo ? { ...todo, text: editingText.trim(), project: editingProject, owner: editingOwner } : todo\r\n      ));\r\n      setEditingTodo(null);\r\n      setEditingText('');\r\n      setEditingProject('');\r\n      setEditingOwner('');\r\n    }\r\n  };\r\n  const cancelEditing = () => {\r\n    setEditingTodo(null);\r\n    setEditingText('');\r\n    setEditingProject('');\r\n    setEditingOwner('');\r\n  };\r\n  const addNewTodo = () => {\r\n    if (newTodoText.trim()) {\r\n      const newTodo: Todo = {\r\n        id: Date.now(),\r\n        text: newTodoText.trim(),\r\n        completed: false,\r\n        date: newTodoDate || undefined,\r\n        priority: newTodoPriority,\r\n        project: newTodoProject,\r\n        owner: newTodoOwner\r\n      };\r\n      setTodos(prev => [...prev, newTodo]);\r\n      setNewTodoText('');\r\n      setNewTodoDate('');\r\n      setNewTodoPriority('medium');\r\n      setNewTodoProject('');\r\n      setNewTodoOwner('');\r\n      setShowNewTodoForm(false);\r\n    }\r\n  };\r\n  const cancelNewTodo = () => {\r\n    setShowNewTodoForm(false);\r\n    setNewTodoText('');\r\n    setNewTodoDate('');\r\n    setNewTodoPriority('medium');\r\n    setNewTodoProject('');\r\n    setNewTodoOwner('');\r\n  };\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'text-red-400';\r\n      case 'medium': return 'text-yellow-400';\r\n      case 'low': return 'text-green-400';\r\n      default: return 'text-white/60';\r\n    }\r\n  };\r\n  // Filtruotos ir ieškomos užduotys\r\n  const filteredTodos = todos.filter(todo => {\r\n    const matchesSearch = todo.text.toLowerCase().includes(search.toLowerCase()) ||\r\n      (todo.owner && todo.owner.toLowerCase().includes(search.toLowerCase()));\r\n    const matchesProject = !filterProject || todo.project === filterProject;\r\n    return matchesSearch && matchesProject;\r\n  });\r\n\r\n  // Grupavimas pagal projektą\r\n  const groupedTodos = filteredTodos.reduce((groups, todo) => {\r\n    const key = todo.project || 'Kiti projektai';\r\n    if (!groups[key]) groups[key] = [];\r\n    groups[key].push(todo);\r\n    return groups;\r\n  }, {} as Record<string, Todo[]>);\r\n\r\n  // Statistika\r\n  const projectCount = Object.keys(groupedTodos).length;\r\n  const totalCount = filteredTodos.length;\r\n  const doneCount = filteredTodos.filter(t => t.completed).length;\r\n  const undoneCount = totalCount - doneCount;\r\n  const progress = totalCount > 0 ? Math.round((doneCount / totalCount) * 100) : 0;\r\n\r\n  // Collapse/expand projektų grupes\r\n  const toggleCollapse = (project: string) => {\r\n    setCollapsedProjects(prev => ({ ...prev, [project]: !prev[project] }));\r\n  };\r\n\r\n  // Atlikti visas užduotis projekte\r\n  const completeAllInProject = (project: string) => {\r\n    setTodos(prev => prev.map(todo => todo.project === project ? { ...todo, completed: true } : todo));\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Apžvalgos panelė */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6\">\r\n        <div className=\"flex flex-wrap gap-6 items-center\">\r\n          <div>\r\n            <div className=\"text-white text-2xl font-bold\">{projectCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Projektų</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-white text-2xl font-bold\">{totalCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Užduočių</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-green-400 text-2xl font-bold\">{doneCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Atlikta</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-red-400 text-2xl font-bold\">{undoneCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Neatlikta</div>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex-1 flex flex-col gap-2 min-w-[200px]\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-white/60 text-sm\">Progresas:</span>\r\n            <span className=\"text-white font-semibold\">{progress}%</span>\r\n          </div>\r\n          <div className=\"w-full bg-white/20 rounded-full h-3\">\r\n            <div className=\"bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full transition-all duration-500\" style={{ width: `${progress}%` }}></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {/* Filtravimas ir paieška */}\r\n      <div className=\"flex flex-col md:flex-row gap-4 items-center justify-between\">\r\n        <div className=\"flex gap-2 items-center w-full md:w-auto\">\r\n          <input\r\n            type=\"text\"\r\n            value={search}\r\n            onChange={e => setSearch(e.target.value)}\r\n            placeholder=\"Ieškoti užduoties ar asmens...\"\r\n            className=\"w-full md:w-64 pl-10 pr-4 py-2 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n          />\r\n          <Search className=\"absolute ml-3 mt-2 h-4 w-4 text-white/60 pointer-events-none\" />\r\n          <select\r\n            value={filterProject}\r\n            onChange={e => setFilterProject(e.target.value)}\r\n            className=\"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n          >\r\n            <option value=\"\">Visi projektai</option>\r\n            {Object.keys(groupedTodos).map(project => (\r\n              <option key={project} value={project}>{project}</option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n        <button\r\n          onClick={() => setShowNewTodoForm(true)}\r\n          className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200 flex items-center gap-2\"\r\n        >\r\n          <Plus className=\"h-4 w-4 text-white\" />\r\n          Pridėti užduotį\r\n        </button>\r\n      </div>\r\n      {/* Užduočių grupės */}\r\n      <div className=\"overflow-x-auto\">\r\n        {Object.entries(groupedTodos).map(([project, projectTodos]) => {\r\n          const projectDone = projectTodos.filter(t => t.completed).length;\r\n          const projectTotal = projectTodos.length;\r\n          const projectProgress = projectTotal > 0 ? Math.round((projectDone / projectTotal) * 100) : 0;\r\n          const isCollapsed = collapsedProjects[project];\r\n          return (\r\n            <div key={project} className=\"mb-8\">\r\n              <div className=\"flex items-center justify-between mb-2 mt-4 border-b border-white/20 pb-1 pl-1\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <button onClick={() => toggleCollapse(project)} className=\"focus:outline-none\">\r\n                    {isCollapsed ? <ChevronRight className=\"h-5 w-5 text-white/60\" /> : <ChevronDown className=\"h-5 w-5 text-white/60\" />}\r\n                  </button>\r\n                  <span className=\"text-white/80 text-base font-semibold\">{project}</span>\r\n                  <span className=\"ml-2 text-xs text-white/50\">{projectDone} / {projectTotal} atlikta</span>\r\n                  <div className=\"w-24 bg-white/20 rounded-full h-2 ml-2\">\r\n                    <div className=\"bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-500\" style={{ width: `${projectProgress}%` }}></div>\r\n                  </div>\r\n                </div>\r\n                <button\r\n                  onClick={() => completeAllInProject(project)}\r\n                  className=\"px-3 py-1 bg-green-500/20 hover:bg-green-500/40 text-green-400 rounded-lg text-xs font-semibold transition-all duration-200\"\r\n                >\r\n                  Atlikti visas\r\n                </button>\r\n              </div>\r\n              {!isCollapsed && (\r\n                <table className=\"min-w-full text-sm text-white\">\r\n                  <thead>\r\n                    <tr className=\"bg-white/10\">\r\n                      <th className=\"px-3 py-2 text-left\">Pavadinimas</th>\r\n                      <th className=\"px-3 py-2 text-left\">Atsakingas asmuo</th>\r\n                      <th className=\"px-3 py-2 text-left\">Data</th>\r\n                      <th className=\"px-3 py-2 text-left\">Prioritetas</th>\r\n                      <th className=\"px-3 py-2 text-left\">Būsena</th>\r\n                      <th className=\"px-3 py-2 text-left\">Veiksmai</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {projectTodos.map((todo) => (\r\n                      <tr\r\n                        key={todo.id}\r\n                        className={`border-b border-white/10 hover:bg-white/5 transition-colors cursor-pointer ${todo.completed ? 'bg-green-500/10 text-white/70' : ''}`}\r\n                        onDoubleClick={() => startEditingTodo(todo)}\r\n                      >\r\n                        <td className={`px-3 py-2 ${todo.completed ? 'line-through' : ''}`}>{editingTodo === todo.id ? (\r\n                          <input\r\n                            type=\"text\"\r\n                            value={editingText}\r\n                            onChange={e => setEditingText(e.target.value)}\r\n                            className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                          />\r\n                        ) : (\r\n                          todo.text\r\n                        )}</td>\r\n                        <td className=\"px-3 py-2\">{editingTodo === todo.id ? (\r\n                          <input\r\n                            type=\"text\"\r\n                            value={editingOwner}\r\n                            onChange={e => setEditingOwner(e.target.value)}\r\n                            className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                          />\r\n                        ) : (\r\n                          todo.owner\r\n                        )}</td>\r\n                        <td className=\"px-3 py-2\">{todo.date}</td>\r\n                        <td className=\"px-3 py-2\">\r\n                          <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`}>\r\n                            {todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'}\r\n                          </span>\r\n                        </td>\r\n                        <td className=\"px-3 py-2\">\r\n                          {/* Modernus toggle mygtukas su animacija ir žalia varnele */}\r\n                          <button\r\n                            onClick={() => toggleTodo(todo.id)}\r\n                            className={`relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none ${todo.completed ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg' : 'bg-white/20'}`}\r\n                            aria-label=\"Pažymėti kaip atliktą\"\r\n                          >\r\n                            <span\r\n                              className={`absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center ${todo.completed ? 'translate-x-4 bg-green-400' : ''}`}\r\n                            >\r\n                              {todo.completed && <Check className=\"h-3 w-3 text-white animate-bounceIn\" />}\r\n                            </span>\r\n                          </button>\r\n                        </td>\r\n                        <td className=\"px-3 py-2 flex gap-1\">\r\n                          {editingTodo === todo.id ? (\r\n                            <>\r\n                              <button onClick={saveEditedTodo} className=\"p-1 text-green-400 hover:text-green-600\"><Check className=\"h-4 w-4\" /></button>\r\n                              <button onClick={cancelEditing} className=\"p-1 text-red-400 hover:text-red-600\"><X className=\"h-4 w-4\" /></button>\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <button onClick={() => startEditingTodo(todo)} className=\"p-1 text-white/60 hover:text-white\"><Edit className=\"h-4 w-4\" /></button>\r\n                              <button onClick={() => deleteTodo(todo.id)} className=\"p-1 text-white/60 hover:text-red-400\"><Trash2 className=\"h-4 w-4\" /></button>\r\n                            </>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                </table>\r\n              )}\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n      <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n        <div className=\"text-white/60 text-sm\">\r\n          Užbaigta: {doneCount} iš {totalCount}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TodosPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAYC,CAAC,EAAEC,KAAK,QAAQ,cAAc;AACrE,SAASC,MAAM,EAAEC,WAAW,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAYjE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAS,CACzC;IAAEiB,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,MAAM;IAAEC,OAAO,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC3J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC1J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,KAAK;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAe,CAAC,EACjJ;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEE,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACnI,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAA4B,QAAQ,CAAC;EAC3F,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAA0B,CAAC,CAAC,CAAC;;EAEvF;;EAEA,MAAMkD,UAAU,GAAIjC,EAAU,IAAK;IACjCD,QAAQ,CAACmC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACpC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGoC,IAAI;MAAElC,SAAS,EAAE,CAACkC,IAAI,CAAClC;IAAU,CAAC,GAAGkC,IAC7D,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,UAAU,GAAIrC,EAAU,IAAK;IACjCD,QAAQ,CAACmC,IAAI,IAAIA,IAAI,CAACI,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACpC,EAAE,KAAKA,EAAE,CAAC,CAAC;EACvD,CAAC;EACD,MAAMuC,gBAAgB,GAAIH,IAAU,IAAK;IACvC5B,cAAc,CAAC4B,IAAI,CAACpC,EAAE,CAAC;IACvBU,cAAc,CAAC0B,IAAI,CAACnC,IAAI,CAAC;IACzBW,iBAAiB,CAACwB,IAAI,CAAC/B,OAAO,IAAI,EAAE,CAAC;IACrCS,eAAe,CAACsB,IAAI,CAAC9B,KAAK,IAAI,EAAE,CAAC;EACnC,CAAC;EACD,MAAMkC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIjC,WAAW,IAAIE,WAAW,CAACgC,IAAI,CAAC,CAAC,EAAE;MACrC1C,QAAQ,CAACmC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACpC,EAAE,KAAKO,WAAW,GAAG;QAAE,GAAG6B,IAAI;QAAEnC,IAAI,EAAEQ,WAAW,CAACgC,IAAI,CAAC,CAAC;QAAEpC,OAAO,EAAEM,cAAc;QAAEL,KAAK,EAAEO;MAAa,CAAC,GAAGuB,IAClH,CAAC,CAAC;MACF5B,cAAc,CAAC,IAAI,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC;EACD,MAAM4B,aAAa,GAAGA,CAAA,KAAM;IAC1BlC,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;IAClBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAM6B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI5B,WAAW,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACtB,MAAMG,OAAa,GAAG;QACpB5C,EAAE,EAAE6C,IAAI,CAACC,GAAG,CAAC,CAAC;QACd7C,IAAI,EAAEc,WAAW,CAAC0B,IAAI,CAAC,CAAC;QACxBvC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAEgB,WAAW,IAAI4B,SAAS;QAC9B3C,QAAQ,EAAEiB,eAAe;QACzBhB,OAAO,EAAEkB,cAAc;QACvBjB,KAAK,EAAEmB;MACT,CAAC;MACD1B,QAAQ,CAACmC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEU,OAAO,CAAC,CAAC;MACpC5B,cAAc,CAAC,EAAE,CAAC;MAClBI,cAAc,CAAC,EAAE,CAAC;MAClBE,kBAAkB,CAAC,QAAQ,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;MACnBR,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EACD,MAAM8B,aAAa,GAAGA,CAAA,KAAM;IAC1B9B,kBAAkB,CAAC,KAAK,CAAC;IACzBF,cAAc,CAAC,EAAE,CAAC;IAClBI,cAAc,CAAC,EAAE,CAAC;IAClBE,kBAAkB,CAAC,QAAQ,CAAC;IAC5BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAMuB,gBAAgB,GAAI7C,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EACD;EACA,MAAM8C,aAAa,GAAGpD,KAAK,CAACwC,MAAM,CAACF,IAAI,IAAI;IACzC,MAAMe,aAAa,GAAGf,IAAI,CAACnC,IAAI,CAACmD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1B,MAAM,CAACyB,WAAW,CAAC,CAAC,CAAC,IACzEhB,IAAI,CAAC9B,KAAK,IAAI8B,IAAI,CAAC9B,KAAK,CAAC8C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1B,MAAM,CAACyB,WAAW,CAAC,CAAC,CAAE;IACzE,MAAME,cAAc,GAAG,CAACzB,aAAa,IAAIO,IAAI,CAAC/B,OAAO,KAAKwB,aAAa;IACvE,OAAOsB,aAAa,IAAIG,cAAc;EACxC,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAGL,aAAa,CAACM,MAAM,CAAC,CAACC,MAAM,EAAErB,IAAI,KAAK;IAC1D,MAAMsB,GAAG,GAAGtB,IAAI,CAAC/B,OAAO,IAAI,gBAAgB;IAC5C,IAAI,CAACoD,MAAM,CAACC,GAAG,CAAC,EAAED,MAAM,CAACC,GAAG,CAAC,GAAG,EAAE;IAClCD,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAACvB,IAAI,CAAC;IACtB,OAAOqB,MAAM;EACf,CAAC,EAAE,CAAC,CAA2B,CAAC;;EAEhC;EACA,MAAMG,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACP,YAAY,CAAC,CAACQ,MAAM;EACrD,MAAMC,UAAU,GAAGd,aAAa,CAACa,MAAM;EACvC,MAAME,SAAS,GAAGf,aAAa,CAACZ,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAAChE,SAAS,CAAC,CAAC6D,MAAM;EAC/D,MAAMI,WAAW,GAAGH,UAAU,GAAGC,SAAS;EAC1C,MAAMG,QAAQ,GAAGJ,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAEL,SAAS,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;EAEhF;EACA,MAAMO,cAAc,GAAIlE,OAAe,IAAK;IAC1C2B,oBAAoB,CAACE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC7B,OAAO,GAAG,CAAC6B,IAAI,CAAC7B,OAAO;IAAE,CAAC,CAAC,CAAC;EACxE,CAAC;;EAED;EACA,MAAMmE,oBAAoB,GAAInE,OAAe,IAAK;IAChDN,QAAQ,CAACmC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC/B,OAAO,KAAKA,OAAO,GAAG;MAAE,GAAG+B,IAAI;MAAElC,SAAS,EAAE;IAAK,CAAC,GAAGkC,IAAI,CAAC,CAAC;EACpG,CAAC;EAED,oBACE3C,OAAA;IAAKgF,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBjF,OAAA;MAAKgF,SAAS,EAAC,wIAAwI;MAAAC,QAAA,gBACrJjF,OAAA;QAAKgF,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDjF,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAKgF,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAEd;UAAY;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnErF,OAAA;YAAKgF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNrF,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAKgF,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAEV;UAAU;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjErF,OAAA;YAAKgF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNrF,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAKgF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAET;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpErF,OAAA;YAAKgF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNrF,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAKgF,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEP;UAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpErF,OAAA;YAAKgF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrF,OAAA;QAAKgF,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDjF,OAAA;UAAKgF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCjF,OAAA;YAAMgF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzDrF,OAAA;YAAMgF,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAEN,QAAQ,EAAC,GAAC;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDjF,OAAA;YAAKgF,SAAS,EAAC,6FAA6F;YAACM,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAGZ,QAAQ;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrF,OAAA;MAAKgF,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAC3EjF,OAAA;QAAKgF,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDjF,OAAA;UACEwF,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEvD,MAAO;UACdwD,QAAQ,EAAEC,CAAC,IAAIxD,SAAS,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACzCI,WAAW,EAAC,0CAAgC;UAC5Cb,SAAS,EAAC;QAA6L;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxM,CAAC,eACFrF,OAAA,CAACJ,MAAM;UAACoF,SAAS,EAAC;QAA8D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFrF,OAAA;UACEyF,KAAK,EAAErD,aAAc;UACrBsD,QAAQ,EAAEC,CAAC,IAAItD,gBAAgB,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDT,SAAS,EAAC,6GAA6G;UAAAC,QAAA,gBAEvHjF,OAAA;YAAQyF,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACvCjB,MAAM,CAACC,IAAI,CAACP,YAAY,CAAC,CAACpB,GAAG,CAAC9B,OAAO,iBACpCZ,OAAA;YAAsByF,KAAK,EAAE7E,OAAQ;YAAAqE,QAAA,EAAErE;UAAO,GAAjCA,OAAO;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmC,CACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNrF,OAAA;QACE8F,OAAO,EAAEA,CAAA,KAAMrE,kBAAkB,CAAC,IAAI,CAAE;QACxCuD,SAAS,EAAC,kGAAkG;QAAAC,QAAA,gBAE5GjF,OAAA,CAACT,IAAI;UAACyF,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAEzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENrF,OAAA;MAAKgF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7Bb,MAAM,CAAC2B,OAAO,CAACjC,YAAY,CAAC,CAACpB,GAAG,CAAC,CAAC,CAAC9B,OAAO,EAAEoF,YAAY,CAAC,KAAK;QAC7D,MAAMC,WAAW,GAAGD,YAAY,CAACnD,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAAChE,SAAS,CAAC,CAAC6D,MAAM;QAChE,MAAM4B,YAAY,GAAGF,YAAY,CAAC1B,MAAM;QACxC,MAAM6B,eAAe,GAAGD,YAAY,GAAG,CAAC,GAAGtB,IAAI,CAACC,KAAK,CAAEoB,WAAW,GAAGC,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC;QAC7F,MAAME,WAAW,GAAG9D,iBAAiB,CAAC1B,OAAO,CAAC;QAC9C,oBACEZ,OAAA;UAAmBgF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjCjF,OAAA;YAAKgF,SAAS,EAAC,gFAAgF;YAAAC,QAAA,gBAC7FjF,OAAA;cAAKgF,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCjF,OAAA;gBAAQ8F,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAClE,OAAO,CAAE;gBAACoE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAC3EmB,WAAW,gBAAGpG,OAAA,CAACF,YAAY;kBAACkF,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGrF,OAAA,CAACH,WAAW;kBAACmF,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,eACTrF,OAAA;gBAAMgF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAErE;cAAO;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxErF,OAAA;gBAAMgF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAEgB,WAAW,EAAC,KAAG,EAACC,YAAY,EAAC,UAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1FrF,OAAA;gBAAKgF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrDjF,OAAA;kBAAKgF,SAAS,EAAC,6FAA6F;kBAACM,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGY,eAAe;kBAAI;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrF,OAAA;cACE8F,OAAO,EAAEA,CAAA,KAAMf,oBAAoB,CAACnE,OAAO,CAAE;cAC7CoE,SAAS,EAAC,6HAA6H;cAAAC,QAAA,EACxI;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACL,CAACe,WAAW,iBACXpG,OAAA;YAAOgF,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC9CjF,OAAA;cAAAiF,QAAA,eACEjF,OAAA;gBAAIgF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACzBjF,OAAA;kBAAIgF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpDrF,OAAA;kBAAIgF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzDrF,OAAA;kBAAIgF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7CrF,OAAA;kBAAIgF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpDrF,OAAA;kBAAIgF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/CrF,OAAA;kBAAIgF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRrF,OAAA;cAAAiF,QAAA,EACGe,YAAY,CAACtD,GAAG,CAAEC,IAAI,iBACrB3C,OAAA;gBAEEgF,SAAS,EAAE,8EAA8ErC,IAAI,CAAClC,SAAS,GAAG,+BAA+B,GAAG,EAAE,EAAG;gBACjJ4F,aAAa,EAAEA,CAAA,KAAMvD,gBAAgB,CAACH,IAAI,CAAE;gBAAAsC,QAAA,gBAE5CjF,OAAA;kBAAIgF,SAAS,EAAE,aAAarC,IAAI,CAAClC,SAAS,GAAG,cAAc,GAAG,EAAE,EAAG;kBAAAwE,QAAA,EAAEnE,WAAW,KAAK6B,IAAI,CAACpC,EAAE,gBAC1FP,OAAA;oBACEwF,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEzE,WAAY;oBACnB0E,QAAQ,EAAEC,CAAC,IAAI1E,cAAc,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC9CT,SAAS,EAAC;kBAAoH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/H,CAAC,GAEF1C,IAAI,CAACnC;gBACN;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACPrF,OAAA;kBAAIgF,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEnE,WAAW,KAAK6B,IAAI,CAACpC,EAAE,gBAChDP,OAAA;oBACEwF,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAErE,YAAa;oBACpBsE,QAAQ,EAAEC,CAAC,IAAItE,eAAe,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CT,SAAS,EAAC;kBAAoH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/H,CAAC,GAEF1C,IAAI,CAAC9B;gBACN;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACPrF,OAAA;kBAAIgF,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEtC,IAAI,CAACjC;gBAAI;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CrF,OAAA;kBAAIgF,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACvBjF,OAAA;oBAAMgF,SAAS,EAAE,kCAAkCxB,gBAAgB,CAACb,IAAI,CAAChC,QAAQ,CAAC,cAAe;oBAAAsE,QAAA,EAC9FtC,IAAI,CAAChC,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAGgC,IAAI,CAAChC,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG;kBAAO;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLrF,OAAA;kBAAIgF,SAAS,EAAC,WAAW;kBAAAC,QAAA,eAEvBjF,OAAA;oBACE8F,OAAO,EAAEA,CAAA,KAAMtD,UAAU,CAACG,IAAI,CAACpC,EAAE,CAAE;oBACnCyE,SAAS,EAAE,oFAAoFrC,IAAI,CAAClC,SAAS,GAAG,0DAA0D,GAAG,aAAa,EAAG;oBAC7L,cAAW,sCAAuB;oBAAAwE,QAAA,eAElCjF,OAAA;sBACEgF,SAAS,EAAE,oIAAoIrC,IAAI,CAAClC,SAAS,GAAG,4BAA4B,GAAG,EAAE,EAAG;sBAAAwE,QAAA,EAEnMtC,IAAI,CAAClC,SAAS,iBAAIT,OAAA,CAACL,KAAK;wBAACqF,SAAS,EAAC;sBAAqC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACLrF,OAAA;kBAAIgF,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EACjCnE,WAAW,KAAK6B,IAAI,CAACpC,EAAE,gBACtBP,OAAA,CAAAE,SAAA;oBAAA+E,QAAA,gBACEjF,OAAA;sBAAQ8F,OAAO,EAAE/C,cAAe;sBAACiC,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,eAACjF,OAAA,CAACL,KAAK;wBAACqF,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC3HrF,OAAA;sBAAQ8F,OAAO,EAAE7C,aAAc;sBAAC+B,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,eAACjF,OAAA,CAACN,CAAC;wBAACsF,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eAClH,CAAC,gBAEHrF,OAAA,CAAAE,SAAA;oBAAA+E,QAAA,gBACEjF,OAAA;sBAAQ8F,OAAO,EAAEA,CAAA,KAAMhD,gBAAgB,CAACH,IAAI,CAAE;sBAACqC,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,eAACjF,OAAA,CAACR,IAAI;wBAACwF,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACnIrF,OAAA;sBAAQ8F,OAAO,EAAEA,CAAA,KAAMlD,UAAU,CAACD,IAAI,CAACpC,EAAE,CAAE;sBAACyE,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,eAACjF,OAAA,CAACP,MAAM;wBAACuF,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACpI;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAxDA1C,IAAI,CAACpC,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyDV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACR;QAAA,GA/FOzE,OAAO;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgGZ,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNrF,OAAA;MAAKgF,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDjF,OAAA;QAAKgF,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,iBAC3B,EAACT,SAAS,EAAC,WAAI,EAACD,UAAU;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjF,EAAA,CA5SID,SAAmB;AAAAmG,EAAA,GAAnBnG,SAAmB;AA8SzB,eAAeA,SAAS;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}