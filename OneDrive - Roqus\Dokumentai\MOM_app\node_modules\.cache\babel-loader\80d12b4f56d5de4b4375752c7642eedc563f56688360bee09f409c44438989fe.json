{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\TodosPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Plus, Edit, Trash2, X, Check } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TodosPage = () => {\n  _s();\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: 'Pasir<PERSON><PERSON><PERSON> prezentacijai',\n    completed: false,\n    date: '2024-01-20',\n    priority: 'high',\n    project: 'Aexn CRM analitika',\n    owner: '<PERSON>'\n  }, {\n    id: 2,\n    text: 'Susitikimas su klientu',\n    completed: true,\n    date: '2024-01-18',\n    priority: 'medium',\n    project: 'Aexn | Analitika',\n    owner: '<PERSON><PERSON>'\n  }, {\n    id: 3,\n    text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokument<PERSON>',\n    completed: false,\n    date: '2024-01-22',\n    priority: 'low',\n    project: 'Aexn_analitika',\n    owner: '<PERSON><PERSON>'\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false,\n    priority: 'medium',\n    project: 'Aexn_analitika',\n    owner: 'Jonas Jonaitis'\n  }]);\n  const [editingTodo, setEditingTodo] = useState(null);\n  const [editingText, setEditingText] = useState('');\n  const [editingProject, setEditingProject] = useState('');\n  const [editingOwner, setEditingOwner] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState('medium');\n  const [newTodoProject, setNewTodoProject] = useState('');\n  const [newTodoOwner, setNewTodoOwner] = useState('');\n  // Pašalinu rūšiavimo select funkciją ir visus su ja susijusius state/kintamuosius\n\n  const toggleTodo = id => {\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const deleteTodo = id => {\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n  const startEditingTodo = todo => {\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n    setEditingProject(todo.project || '');\n    setEditingOwner(todo.owner || '');\n  };\n  const saveEditedTodo = () => {\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => todo.id === editingTodo ? {\n        ...todo,\n        text: editingText.trim(),\n        project: editingProject,\n        owner: editingOwner\n      } : todo));\n      setEditingTodo(null);\n      setEditingText('');\n      setEditingProject('');\n      setEditingOwner('');\n    }\n  };\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n    setEditingProject('');\n    setEditingOwner('');\n  };\n  const addNewTodo = () => {\n    if (newTodoText.trim()) {\n      const newTodo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority,\n        project: newTodoProject,\n        owner: newTodoOwner\n      };\n      setTodos(prev => [...prev, newTodo]);\n      setNewTodoText('');\n      setNewTodoDate('');\n      setNewTodoPriority('medium');\n      setNewTodoProject('');\n      setNewTodoOwner('');\n      setShowNewTodoForm(false);\n    }\n  };\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n    setNewTodoProject('');\n    setNewTodoOwner('');\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-400';\n      case 'medium':\n        return 'text-yellow-400';\n      case 'low':\n        return 'text-green-400';\n      default:\n        return 'text-white/60';\n    }\n  };\n  // Grupavimas pagal projektą\n  const groupedTodos = todos.reduce((groups, todo) => {\n    const key = todo.project || 'Kiti projektai';\n    if (!groups[key]) groups[key] = [];\n    groups[key].push(todo);\n    return groups;\n  }, {});\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",\n          children: /*#__PURE__*/_jsxDEV(Check, {\n            className: \"h-6 w-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-white\",\n            children: \"Susitikimo u\\u017Eduotys\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-white/60\",\n            children: \"Tvarkykite, planuokite ir sekite susitikimo u\\u017Eduotis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"U\\u017Eduo\\u010Di\\u0173 s\\u0105ra\\u0161as\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowNewTodoForm(true),\n          className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\",\n          children: /*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-4 w-4 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), showNewTodoForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-4 bg-white/5 rounded-xl border border-white/10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newTodoText,\n            onChange: e => setNewTodoText(e.target.value),\n            placeholder: \"\\u012Eveskite u\\u017Eduot\\u012F...\",\n            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\",\n            onKeyPress: e => e.key === 'Enter' && addNewTodo()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: newTodoDate,\n              onChange: e => setNewTodoDate(e.target.value),\n              className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: newTodoPriority,\n              onChange: e => setNewTodoPriority(e.target.value),\n              className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"low\",\n                children: \"\\u017Demas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"medium\",\n                children: \"Vidutinis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"high\",\n                children: \"Auk\\u0161tas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoProject,\n              onChange: e => setNewTodoProject(e.target.value),\n              placeholder: \"Projekto pavadinimas\",\n              className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoOwner,\n              onChange: e => setNewTodoOwner(e.target.value),\n              placeholder: \"Atsakingas asmuo\",\n              className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: addNewTodo,\n              className: \"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Check, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), \"Prid\\u0117ti\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: cancelNewTodo,\n              className: \"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(X, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), \"At\\u0161aukti\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: Object.entries(groupedTodos).map(([project, projectTodos]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/80 text-base font-semibold mb-2 mt-4 border-b border-white/20 pb-1 pl-1\",\n            children: project\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full text-sm text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Pavadinimas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Atsakingas asmuo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Prioritetas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"B\\u016Bsena\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Veiksmai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: projectTodos.map(todo => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: `border-b border-white/10 hover:bg-white/5 transition-colors cursor-pointer`,\n                onDoubleClick: () => startEditingTodo(todo),\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: editingText,\n                    onChange: e => setEditingText(e.target.value),\n                    className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 27\n                  }, this) : todo.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: editingOwner,\n                    onChange: e => setEditingOwner(e.target.value),\n                    className: \"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 27\n                  }, this) : todo.owner\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: todo.date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`,\n                    children: todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleTodo(todo.id),\n                    className: `relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none ${todo.completed ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg' : 'bg-white/20'}`,\n                    \"aria-label\": \"Pa\\u017Eym\\u0117ti kaip atlikt\\u0105\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 ${todo.completed ? 'translate-x-4 bg-green-400' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2 flex gap-1\",\n                  children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: saveEditedTodo,\n                      className: \"p-1 text-green-400 hover:text-green-600\",\n                      children: /*#__PURE__*/_jsxDEV(Check, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 264,\n                        columnNumber: 114\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: cancelEditing,\n                      className: \"p-1 text-red-400 hover:text-red-600\",\n                      children: /*#__PURE__*/_jsxDEV(X, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 265,\n                        columnNumber: 109\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => startEditingTodo(todo),\n                      className: \"p-1 text-white/60 hover:text-white\",\n                      children: /*#__PURE__*/_jsxDEV(Edit, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 269,\n                        columnNumber: 123\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => deleteTodo(todo.id),\n                      className: \"p-1 text-white/60 hover:text-red-400\",\n                      children: /*#__PURE__*/_jsxDEV(Trash2, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 122\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this)]\n              }, todo.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, project, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 pt-4 border-t border-white/10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/60 text-sm\",\n          children: [\"U\\u017Ebaigta: \", todos.filter(t => t.completed).length, \" i\\u0161 \", todos.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n_s(TodosPage, \"KMLcvhnThrUBxrkTbeS3xVMiW7o=\");\n_c = TodosPage;\nexport default TodosPage;\nvar _c;\n$RefreshReg$(_c, \"TodosPage\");", "map": {"version": 3, "names": ["React", "useState", "Plus", "Edit", "Trash2", "X", "Check", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TodosPage", "_s", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "date", "priority", "project", "owner", "editingTodo", "setEditingTodo", "editingText", "setEditingText", "editingProject", "setEditingProject", "<PERSON><PERSON><PERSON><PERSON>", "setEditingOwner", "newTodoText", "setNewTodoText", "showNewTodoForm", "setShowNewTodoForm", "newTodoDate", "setNewTodoDate", "newTodoPriority", "setNewTodoPriority", "newTodoProject", "setNewTodoProject", "newTodoOwner", "setNewTodoOwner", "toggleTodo", "prev", "map", "todo", "deleteTodo", "filter", "startEditingTodo", "saveEditedTodo", "trim", "cancelEditing", "addNewTodo", "newTodo", "Date", "now", "undefined", "cancelNewTodo", "getPriorityColor", "groupedTodos", "reduce", "groups", "key", "push", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "value", "onChange", "e", "target", "placeholder", "onKeyPress", "Object", "entries", "projectTodos", "onDoubleClick", "t", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TodosPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Plus, Edit, Trash2, Calendar, X, Check } from 'lucide-react';\r\n\r\ninterface Todo {\r\n  id: number;\r\n  text: string;\r\n  completed: boolean;\r\n  date?: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n  project?: string;\r\n  owner?: string;\r\n}\r\n\r\nconst TodosPage: React.FC = () => {\r\n  const [todos, setTodos] = useState<Todo[]>([\r\n    { id: 1, text: '<PERSON><PERSON><PERSON><PERSON><PERSON> prezenta<PERSON>', completed: false, date: '2024-01-20', priority: 'high', project: 'Aexn CRM analitika', owner: '<PERSON>' },\r\n    { id: 2, text: 'Susi<PERSON><PERSON><PERSON> su klientu', completed: true, date: '2024-01-18', priority: 'medium', project: 'Aexn | Ana<PERSON><PERSON>', owner: '<PERSON><PERSON>' },\r\n    { id: 3, text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokumentus', completed: false, date: '2024-01-22', priority: 'low', project: 'Aexn_analitika', owner: '<PERSON><PERSON>' },\r\n    { id: 4, text: '<PERSON><PERSON>ti kitą savait<PERSON>', completed: false, priority: 'medium', project: 'Aexn_analitika', owner: '<PERSON>' }\r\n  ]);\r\n  const [editingTodo, setEditingTodo] = useState<number | null>(null);\r\n  const [editingText, setEditingText] = useState('');\r\n  const [editingProject, setEditingProject] = useState('');\r\n  const [editingOwner, setEditingOwner] = useState('');\r\n  const [newTodoText, setNewTodoText] = useState('');\r\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\r\n  const [newTodoDate, setNewTodoDate] = useState('');\r\n  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');\r\n  const [newTodoProject, setNewTodoProject] = useState('');\r\n  const [newTodoOwner, setNewTodoOwner] = useState('');\r\n  // Pašalinu rūšiavimo select funkciją ir visus su ja susijusius state/kintamuosius\r\n\r\n  const toggleTodo = (id: number) => {\r\n    setTodos(prev => prev.map(todo => \r\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\r\n    ));\r\n  };\r\n  const deleteTodo = (id: number) => {\r\n    setTodos(prev => prev.filter(todo => todo.id !== id));\r\n  };\r\n  const startEditingTodo = (todo: Todo) => {\r\n    setEditingTodo(todo.id);\r\n    setEditingText(todo.text);\r\n    setEditingProject(todo.project || '');\r\n    setEditingOwner(todo.owner || '');\r\n  };\r\n  const saveEditedTodo = () => {\r\n    if (editingTodo && editingText.trim()) {\r\n      setTodos(prev => prev.map(todo => \r\n        todo.id === editingTodo ? { ...todo, text: editingText.trim(), project: editingProject, owner: editingOwner } : todo\r\n      ));\r\n      setEditingTodo(null);\r\n      setEditingText('');\r\n      setEditingProject('');\r\n      setEditingOwner('');\r\n    }\r\n  };\r\n  const cancelEditing = () => {\r\n    setEditingTodo(null);\r\n    setEditingText('');\r\n    setEditingProject('');\r\n    setEditingOwner('');\r\n  };\r\n  const addNewTodo = () => {\r\n    if (newTodoText.trim()) {\r\n      const newTodo: Todo = {\r\n        id: Date.now(),\r\n        text: newTodoText.trim(),\r\n        completed: false,\r\n        date: newTodoDate || undefined,\r\n        priority: newTodoPriority,\r\n        project: newTodoProject,\r\n        owner: newTodoOwner\r\n      };\r\n      setTodos(prev => [...prev, newTodo]);\r\n      setNewTodoText('');\r\n      setNewTodoDate('');\r\n      setNewTodoPriority('medium');\r\n      setNewTodoProject('');\r\n      setNewTodoOwner('');\r\n      setShowNewTodoForm(false);\r\n    }\r\n  };\r\n  const cancelNewTodo = () => {\r\n    setShowNewTodoForm(false);\r\n    setNewTodoText('');\r\n    setNewTodoDate('');\r\n    setNewTodoPriority('medium');\r\n    setNewTodoProject('');\r\n    setNewTodoOwner('');\r\n  };\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'text-red-400';\r\n      case 'medium': return 'text-yellow-400';\r\n      case 'low': return 'text-green-400';\r\n      default: return 'text-white/60';\r\n    }\r\n  };\r\n  // Grupavimas pagal projektą\r\n  const groupedTodos = todos.reduce((groups, todo) => {\r\n    const key = todo.project || 'Kiti projektai';\r\n    if (!groups[key]) groups[key] = [];\r\n    groups[key].push(todo);\r\n    return groups;\r\n  }, {} as Record<string, Todo[]>);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\r\n            <Check className=\"h-6 w-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-white\">Susitikimo užduotys</h2>\r\n            <p className=\"text-sm text-white/60\">Tvarkykite, planuokite ir sekite susitikimo užduotis</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <h3 className=\"text-lg font-semibold text-white\">Užduočių sąrašas</h3>\r\n          </div>\r\n          <button\r\n            onClick={() => setShowNewTodoForm(true)}\r\n            className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\"\r\n          >\r\n            <Plus className=\"h-4 w-4 text-white\" />\r\n          </button>\r\n        </div>\r\n        {/* Pašalinta rūšiavimo select */}\r\n        {showNewTodoForm && (\r\n          <div className=\"mb-4 p-4 bg-white/5 rounded-xl border border-white/10\">\r\n            <div className=\"space-y-3\">\r\n              <input\r\n                type=\"text\"\r\n                value={newTodoText}\r\n                onChange={(e) => setNewTodoText(e.target.value)}\r\n                placeholder=\"Įveskite užduotį...\"\r\n                className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                onKeyPress={(e) => e.key === 'Enter' && addNewTodo()}\r\n              />\r\n              <div className=\"flex gap-2\">\r\n                <input\r\n                  type=\"date\"\r\n                  value={newTodoDate}\r\n                  onChange={(e) => setNewTodoDate(e.target.value)}\r\n                  className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                />\r\n                <select\r\n                  value={newTodoPriority}\r\n                  onChange={(e) => setNewTodoPriority(e.target.value as 'low' | 'medium' | 'high')}\r\n                  className=\"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                >\r\n                  <option value=\"low\">Žemas</option>\r\n                  <option value=\"medium\">Vidutinis</option>\r\n                  <option value=\"high\">Aukštas</option>\r\n                </select>\r\n              </div>\r\n              <div className=\"flex gap-2\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoProject}\r\n                  onChange={e => setNewTodoProject(e.target.value)}\r\n                  placeholder=\"Projekto pavadinimas\"\r\n                  className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                />\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoOwner}\r\n                  onChange={e => setNewTodoOwner(e.target.value)}\r\n                  placeholder=\"Atsakingas asmuo\"\r\n                  className=\"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:border-blue-400\"\r\n                />\r\n              </div>\r\n              <div className=\"flex gap-2\">\r\n                <button\r\n                  onClick={addNewTodo}\r\n                  className=\"flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                >\r\n                  <Check className=\"h-4 w-4\" />\r\n                  Pridėti\r\n                </button>\r\n                <button\r\n                  onClick={cancelNewTodo}\r\n                  className=\"flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors\"\r\n                >\r\n                  <X className=\"h-4 w-4\" />\r\n                  Atšaukti\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div className=\"overflow-x-auto\">\r\n          {Object.entries(groupedTodos).map(([project, projectTodos]) => (\r\n            <div key={project} className=\"mb-8\">\r\n              <div className=\"text-white/80 text-base font-semibold mb-2 mt-4 border-b border-white/20 pb-1 pl-1\">{project}</div>\r\n              <table className=\"min-w-full text-sm text-white\">\r\n                <thead>\r\n                  <tr className=\"bg-white/10\">\r\n                    <th className=\"px-3 py-2 text-left\">Pavadinimas</th>\r\n                    <th className=\"px-3 py-2 text-left\">Atsakingas asmuo</th>\r\n                    <th className=\"px-3 py-2 text-left\">Data</th>\r\n                    <th className=\"px-3 py-2 text-left\">Prioritetas</th>\r\n                    <th className=\"px-3 py-2 text-left\">Būsena</th>\r\n                    <th className=\"px-3 py-2 text-left\">Veiksmai</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {projectTodos.map((todo) => (\r\n                    <tr\r\n                      key={todo.id}\r\n                      className={`border-b border-white/10 hover:bg-white/5 transition-colors cursor-pointer`}\r\n                      onDoubleClick={() => startEditingTodo(todo)}\r\n                    >\r\n                      <td className=\"px-3 py-2\">\r\n                        {editingTodo === todo.id ? (\r\n                          <input\r\n                            type=\"text\"\r\n                            value={editingText}\r\n                            onChange={e => setEditingText(e.target.value)}\r\n                            className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                          />\r\n                        ) : (\r\n                          todo.text\r\n                        )}\r\n                      </td>\r\n                      <td className=\"px-3 py-2\">\r\n                        {editingTodo === todo.id ? (\r\n                          <input\r\n                            type=\"text\"\r\n                            value={editingOwner}\r\n                            onChange={e => setEditingOwner(e.target.value)}\r\n                            className=\"w-full bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white focus:outline-none focus:border-blue-400\"\r\n                          />\r\n                        ) : (\r\n                          todo.owner\r\n                        )}\r\n                      </td>\r\n                      <td className=\"px-3 py-2\">{todo.date}</td>\r\n                      <td className=\"px-3 py-2\">\r\n                        <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`}>\r\n                          {todo.priority === 'high' ? 'Aukštas' : todo.priority === 'medium' ? 'Vidutinis' : 'Žemas'}\r\n                        </span>\r\n                      </td>\r\n                      <td className=\"px-3 py-2\">\r\n                        {/* Modernus toggle mygtukas */}\r\n                        <button\r\n                          onClick={() => toggleTodo(todo.id)}\r\n                          className={`relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none ${todo.completed ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg' : 'bg-white/20'}`}\r\n                          aria-label=\"Pažymėti kaip atliktą\"\r\n                        >\r\n                          <span\r\n                            className={`absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 ${todo.completed ? 'translate-x-4 bg-green-400' : ''}`}\r\n                          ></span>\r\n                        </button>\r\n                      </td>\r\n                      <td className=\"px-3 py-2 flex gap-1\">\r\n                        {editingTodo === todo.id ? (\r\n                          <>\r\n                            <button onClick={saveEditedTodo} className=\"p-1 text-green-400 hover:text-green-600\"><Check className=\"h-4 w-4\" /></button>\r\n                            <button onClick={cancelEditing} className=\"p-1 text-red-400 hover:text-red-600\"><X className=\"h-4 w-4\" /></button>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <button onClick={() => startEditingTodo(todo)} className=\"p-1 text-white/60 hover:text-white\"><Edit className=\"h-4 w-4\" /></button>\r\n                            <button onClick={() => deleteTodo(todo.id)} className=\"p-1 text-white/60 hover:text-red-400\"><Trash2 className=\"h-4 w-4\" /></button>\r\n                          </>\r\n                        )}\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n          <div className=\"text-white/60 text-sm\">\r\n            Užbaigta: {todos.filter(t => t.completed).length} iš {todos.length}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TodosPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAYC,CAAC,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAYtE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAS,CACzC;IAAEc,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,MAAM;IAAEC,OAAO,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC3J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC1J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,KAAK;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAe,CAAC,EACjJ;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEE,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACnI,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAA4B,QAAQ,CAAC;EAC3F,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD;;EAEA,MAAMyC,UAAU,GAAI3B,EAAU,IAAK;IACjCD,QAAQ,CAAC6B,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAAC9B,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAG8B,IAAI;MAAE5B,SAAS,EAAE,CAAC4B,IAAI,CAAC5B;IAAU,CAAC,GAAG4B,IAC7D,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,UAAU,GAAI/B,EAAU,IAAK;IACjCD,QAAQ,CAAC6B,IAAI,IAAIA,IAAI,CAACI,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAC9B,EAAE,KAAKA,EAAE,CAAC,CAAC;EACvD,CAAC;EACD,MAAMiC,gBAAgB,GAAIH,IAAU,IAAK;IACvCtB,cAAc,CAACsB,IAAI,CAAC9B,EAAE,CAAC;IACvBU,cAAc,CAACoB,IAAI,CAAC7B,IAAI,CAAC;IACzBW,iBAAiB,CAACkB,IAAI,CAACzB,OAAO,IAAI,EAAE,CAAC;IACrCS,eAAe,CAACgB,IAAI,CAACxB,KAAK,IAAI,EAAE,CAAC;EACnC,CAAC;EACD,MAAM4B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI3B,WAAW,IAAIE,WAAW,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACrCpC,QAAQ,CAAC6B,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAAC9B,EAAE,KAAKO,WAAW,GAAG;QAAE,GAAGuB,IAAI;QAAE7B,IAAI,EAAEQ,WAAW,CAAC0B,IAAI,CAAC,CAAC;QAAE9B,OAAO,EAAEM,cAAc;QAAEL,KAAK,EAAEO;MAAa,CAAC,GAAGiB,IAClH,CAAC,CAAC;MACFtB,cAAc,CAAC,IAAI,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC;EACD,MAAMsB,aAAa,GAAGA,CAAA,KAAM;IAC1B5B,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;IAClBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAMuB,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItB,WAAW,CAACoB,IAAI,CAAC,CAAC,EAAE;MACtB,MAAMG,OAAa,GAAG;QACpBtC,EAAE,EAAEuC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdvC,IAAI,EAAEc,WAAW,CAACoB,IAAI,CAAC,CAAC;QACxBjC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAEgB,WAAW,IAAIsB,SAAS;QAC9BrC,QAAQ,EAAEiB,eAAe;QACzBhB,OAAO,EAAEkB,cAAc;QACvBjB,KAAK,EAAEmB;MACT,CAAC;MACD1B,QAAQ,CAAC6B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEU,OAAO,CAAC,CAAC;MACpCtB,cAAc,CAAC,EAAE,CAAC;MAClBI,cAAc,CAAC,EAAE,CAAC;MAClBE,kBAAkB,CAAC,QAAQ,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;MACnBR,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EACD,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1BxB,kBAAkB,CAAC,KAAK,CAAC;IACzBF,cAAc,CAAC,EAAE,CAAC;IAClBI,cAAc,CAAC,EAAE,CAAC;IAClBE,kBAAkB,CAAC,QAAQ,CAAC;IAC5BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAMiB,gBAAgB,GAAIvC,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EACD;EACA,MAAMwC,YAAY,GAAG9C,KAAK,CAAC+C,MAAM,CAAC,CAACC,MAAM,EAAEhB,IAAI,KAAK;IAClD,MAAMiB,GAAG,GAAGjB,IAAI,CAACzB,OAAO,IAAI,gBAAgB;IAC5C,IAAI,CAACyC,MAAM,CAACC,GAAG,CAAC,EAAED,MAAM,CAACC,GAAG,CAAC,GAAG,EAAE;IAClCD,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAClB,IAAI,CAAC;IACtB,OAAOgB,MAAM;EACf,CAAC,EAAE,CAAC,CAA2B,CAAC;EAEhC,oBACErD,OAAA;IAAKwD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBzD,OAAA;MAAKwD,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFzD,OAAA;QAAKwD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCzD,OAAA;UAAKwD,SAAS,EAAC,+JAA+J;UAAAC,QAAA,eAC5KzD,OAAA,CAACF,KAAK;YAAC0D,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACN7D,OAAA;UAAAyD,QAAA,gBACEzD,OAAA;YAAIwD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzE7D,OAAA;YAAGwD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN7D,OAAA;MAAKwD,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAClFzD,OAAA;QAAKwD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzD,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtCzD,OAAA;YAAIwD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN7D,OAAA;UACE8D,OAAO,EAAEA,CAAA,KAAMrC,kBAAkB,CAAC,IAAI,CAAE;UACxC+B,SAAS,EAAC,0EAA0E;UAAAC,QAAA,eAEpFzD,OAAA,CAACN,IAAI;YAAC8D,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELrC,eAAe,iBACdxB,OAAA;QAAKwD,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpEzD,OAAA;UAAKwD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzD,OAAA;YACE+D,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE1C,WAAY;YACnB2C,QAAQ,EAAGC,CAAC,IAAK3C,cAAc,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDI,WAAW,EAAC,oCAAqB;YACjCZ,SAAS,EAAC,yIAAyI;YACnJa,UAAU,EAAGH,CAAC,IAAKA,CAAC,CAACZ,GAAG,KAAK,OAAO,IAAIV,UAAU,CAAC;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACF7D,OAAA;YAAKwD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzD,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEtC,WAAY;cACnBuC,QAAQ,EAAGC,CAAC,IAAKvC,cAAc,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDR,SAAS,EAAC;YAAoH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/H,CAAC,eACF7D,OAAA;cACEgE,KAAK,EAAEpC,eAAgB;cACvBqC,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAkC,CAAE;cACjFR,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvHzD,OAAA;gBAAQgE,KAAK,EAAC,KAAK;gBAAAP,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC7D,OAAA;gBAAQgE,KAAK,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzC7D,OAAA;gBAAQgE,KAAK,EAAC,MAAM;gBAAAP,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzD,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXC,KAAK,EAAElC,cAAe;cACtBmC,QAAQ,EAAEC,CAAC,IAAInC,iBAAiB,CAACmC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACjDI,WAAW,EAAC,sBAAsB;cAClCZ,SAAS,EAAC;YAAyI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpJ,CAAC,eACF7D,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEhC,YAAa;cACpBiC,QAAQ,EAAEC,CAAC,IAAIjC,eAAe,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CI,WAAW,EAAC,kBAAkB;cAC9BZ,SAAS,EAAC;YAAyI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzD,OAAA;cACE8D,OAAO,EAAElB,UAAW;cACpBY,SAAS,EAAC,iIAAiI;cAAAC,QAAA,gBAE3IzD,OAAA,CAACF,KAAK;gBAAC0D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7D,OAAA;cACE8D,OAAO,EAAEb,aAAc;cACvBO,SAAS,EAAC,6HAA6H;cAAAC,QAAA,gBAEvIzD,OAAA,CAACH,CAAC;gBAAC2D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAE3B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACD7D,OAAA;QAAKwD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7Ba,MAAM,CAACC,OAAO,CAACpB,YAAY,CAAC,CAACf,GAAG,CAAC,CAAC,CAACxB,OAAO,EAAE4D,YAAY,CAAC,kBACxDxE,OAAA;UAAmBwD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjCzD,OAAA;YAAKwD,SAAS,EAAC,oFAAoF;YAAAC,QAAA,EAAE7C;UAAO;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnH7D,OAAA;YAAOwD,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC9CzD,OAAA;cAAAyD,QAAA,eACEzD,OAAA;gBAAIwD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACzBzD,OAAA;kBAAIwD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpD7D,OAAA;kBAAIwD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzD7D,OAAA;kBAAIwD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7C7D,OAAA;kBAAIwD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpD7D,OAAA;kBAAIwD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/C7D,OAAA;kBAAIwD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR7D,OAAA;cAAAyD,QAAA,EACGe,YAAY,CAACpC,GAAG,CAAEC,IAAI,iBACrBrC,OAAA;gBAEEwD,SAAS,EAAE,4EAA6E;gBACxFiB,aAAa,EAAEA,CAAA,KAAMjC,gBAAgB,CAACH,IAAI,CAAE;gBAAAoB,QAAA,gBAE5CzD,OAAA;kBAAIwD,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACtB3C,WAAW,KAAKuB,IAAI,CAAC9B,EAAE,gBACtBP,OAAA;oBACE+D,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEhD,WAAY;oBACnBiD,QAAQ,EAAEC,CAAC,IAAIjD,cAAc,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC9CR,SAAS,EAAC;kBAAoH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/H,CAAC,GAEFxB,IAAI,CAAC7B;gBACN;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACL7D,OAAA;kBAAIwD,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACtB3C,WAAW,KAAKuB,IAAI,CAAC9B,EAAE,gBACtBP,OAAA;oBACE+D,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE5C,YAAa;oBACpB6C,QAAQ,EAAEC,CAAC,IAAI7C,eAAe,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CR,SAAS,EAAC;kBAAoH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/H,CAAC,GAEFxB,IAAI,CAACxB;gBACN;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACL7D,OAAA;kBAAIwD,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEpB,IAAI,CAAC3B;gBAAI;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1C7D,OAAA;kBAAIwD,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACvBzD,OAAA;oBAAMwD,SAAS,EAAE,kCAAkCN,gBAAgB,CAACb,IAAI,CAAC1B,QAAQ,CAAC,cAAe;oBAAA8C,QAAA,EAC9FpB,IAAI,CAAC1B,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG0B,IAAI,CAAC1B,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG;kBAAO;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL7D,OAAA;kBAAIwD,SAAS,EAAC,WAAW;kBAAAC,QAAA,eAEvBzD,OAAA;oBACE8D,OAAO,EAAEA,CAAA,KAAM5B,UAAU,CAACG,IAAI,CAAC9B,EAAE,CAAE;oBACnCiD,SAAS,EAAE,oFAAoFnB,IAAI,CAAC5B,SAAS,GAAG,0DAA0D,GAAG,aAAa,EAAG;oBAC7L,cAAW,sCAAuB;oBAAAgD,QAAA,eAElCzD,OAAA;sBACEwD,SAAS,EAAE,mGAAmGnB,IAAI,CAAC5B,SAAS,GAAG,4BAA4B,GAAG,EAAE;oBAAG;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9J;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACL7D,OAAA;kBAAIwD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EACjC3C,WAAW,KAAKuB,IAAI,CAAC9B,EAAE,gBACtBP,OAAA,CAAAE,SAAA;oBAAAuD,QAAA,gBACEzD,OAAA;sBAAQ8D,OAAO,EAAErB,cAAe;sBAACe,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,eAACzD,OAAA,CAACF,KAAK;wBAAC0D,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC3H7D,OAAA;sBAAQ8D,OAAO,EAAEnB,aAAc;sBAACa,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,eAACzD,OAAA,CAACH,CAAC;wBAAC2D,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eAClH,CAAC,gBAEH7D,OAAA,CAAAE,SAAA;oBAAAuD,QAAA,gBACEzD,OAAA;sBAAQ8D,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAACH,IAAI,CAAE;sBAACmB,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,eAACzD,OAAA,CAACL,IAAI;wBAAC6D,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACnI7D,OAAA;sBAAQ8D,OAAO,EAAEA,CAAA,KAAMxB,UAAU,CAACD,IAAI,CAAC9B,EAAE,CAAE;sBAACiD,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,eAACzD,OAAA,CAACJ,MAAM;wBAAC4D,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACpI;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GA1DAxB,IAAI,CAAC9B,EAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2DV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA9EAjD,OAAO;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+EZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN7D,OAAA;QAAKwD,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDzD,OAAA;UAAKwD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,iBAC3B,EAACpD,KAAK,CAACkC,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAACjE,SAAS,CAAC,CAACkE,MAAM,EAAC,WAAI,EAACtE,KAAK,CAACsE,MAAM;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CAnRID,SAAmB;AAAAyE,EAAA,GAAnBzE,SAAmB;AAqRzB,eAAeA,SAAS;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}