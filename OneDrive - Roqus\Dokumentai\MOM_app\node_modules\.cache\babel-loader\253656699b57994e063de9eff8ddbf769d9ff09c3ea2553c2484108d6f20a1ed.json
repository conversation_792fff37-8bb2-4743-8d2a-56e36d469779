{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\Sidebar.tsx\";\nimport React from 'react';\nimport { Mic2, Zap, Headphones, Trophy, Settings, ChevronUp, User, Check } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  activeView,\n  onViewChange\n}) => {\n  const navigationItems = [{\n    id: 'recording',\n    label: 'Įrašymas',\n    icon: Mic2\n  }, {\n    id: 'transcription',\n    label: 'Transkribavimas',\n    icon: Zap\n  }, {\n    id: 'transcript',\n    label: 'Rezultatai',\n    icon: Headphones\n  }, {\n    id: 'todos',\n    label: 'Užduotys',\n    icon: Check\n  }, {\n    id: 'goals',\n    label: 'Tikslai',\n    icon: Trophy\n  }, {\n    id: 'settings',\n    label: 'Nustatymai',\n    icon: Settings\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-64 bg-white/10 backdrop-blur-xl border-r border-white/20 h-screen fixed left-0 top-0 z-40\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b border-white/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(User, {\n            className: \"h-6 w-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-white font-semibold\",\n            children: \"Your Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/60 text-sm\",\n            children: \"UI UX Designer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"p-4 space-y-2\",\n      children: navigationItems.map(item => {\n        const Icon = item.icon;\n        const isActive = activeView === item.id;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onViewChange(item.id),\n          className: `w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${isActive ? 'bg-white/20 text-white border border-white/30' : 'text-white/70 hover:text-white hover:bg-white/10'}`,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-6 left-4 right-4\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center space-x-2 hover:from-purple-600 hover:to-pink-600 transition-all duration-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Upgrade to premium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ChevronUp, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Mic2", "Zap", "Headphones", "Trophy", "Settings", "ChevronUp", "User", "Check", "jsxDEV", "_jsxDEV", "Sidebar", "activeView", "onViewChange", "navigationItems", "id", "label", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "Icon", "isActive", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { \n  Mic2, \n  Zap, \n  Headphones, \n  FileText, \n  Trophy, \n  Settings, \n  ChevronUp,\n  User,\n  Check\n} from 'lucide-react';\n\ninterface SidebarProps {\n  activeView: string;\n  onViewChange: (view: string) => void;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ activeView, onViewChange }) => {\n  const navigationItems = [\n    { id: 'recording', label: 'Įrašymas', icon: Mic2 },\n    { id: 'transcription', label: 'Transkribavimas', icon: Zap },\n    { id: 'transcript', label: 'Rezultatai', icon: Headphones },\n    { id: 'todos', label: 'Užduot<PERSON>', icon: Check },\n    { id: 'goals', label: 'Tikslai', icon: Trophy },\n    { id: 'settings', label: 'Nustatymai', icon: Settings },\n  ];\n\n  return (\n    <div className=\"w-64 bg-white/10 backdrop-blur-xl border-r border-white/20 h-screen fixed left-0 top-0 z-40\">\n      {/* User Profile */}\n      <div className=\"p-6 border-b border-white/20\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\n            <User className=\"h-6 w-6 text-white\" />\n          </div>\n          <div>\n            <h3 className=\"text-white font-semibold\">Your Name</h3>\n            <p className=\"text-white/60 text-sm\">UI UX Designer</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"p-4 space-y-2\">\n        {navigationItems.map((item) => {\n          const Icon = item.icon;\n          const isActive = activeView === item.id;\n          \n          return (\n            <button\n              key={item.id}\n              onClick={() => onViewChange(item.id)}\n              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${\n                isActive\n                  ? 'bg-white/20 text-white border border-white/30'\n                  : 'text-white/70 hover:text-white hover:bg-white/10'\n              }`}\n            >\n              <Icon className=\"h-5 w-5\" />\n              <span className=\"font-medium\">{item.label}</span>\n            </button>\n          );\n        })}\n      </nav>\n\n      {/* Premium Upgrade */}\n      <div className=\"absolute bottom-6 left-4 right-4\">\n        <button className=\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center space-x-2 hover:from-purple-600 hover:to-pink-600 transition-all duration-200\">\n          <span>Upgrade to premium</span>\n          <ChevronUp className=\"h-4 w-4\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,GAAG,EACHC,UAAU,EAEVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,KAAK,QACA,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtB,MAAMC,OAA+B,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EACxE,MAAMC,eAAe,GAAG,CACtB;IAAEC,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAEhB;EAAK,CAAC,EAClD;IAAEc,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAEf;EAAI,CAAC,EAC5D;IAAEa,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAEd;EAAW,CAAC,EAC3D;IAAEY,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAET;EAAM,CAAC,EAC/C;IAAEO,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEb;EAAO,CAAC,EAC/C;IAAEW,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAEZ;EAAS,CAAC,CACxD;EAED,oBACEK,OAAA;IAAKQ,SAAS,EAAC,6FAA6F;IAAAC,QAAA,gBAE1GT,OAAA;MAAKQ,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CT,OAAA;QAAKQ,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CT,OAAA;UAAKQ,SAAS,EAAC,uGAAuG;UAAAC,QAAA,eACpHT,OAAA,CAACH,IAAI;YAACW,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNb,OAAA;UAAAS,QAAA,gBACET,OAAA;YAAIQ,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDb,OAAA;YAAGQ,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNb,OAAA;MAAKQ,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BL,eAAe,CAACU,GAAG,CAAEC,IAAI,IAAK;QAC7B,MAAMC,IAAI,GAAGD,IAAI,CAACR,IAAI;QACtB,MAAMU,QAAQ,GAAGf,UAAU,KAAKa,IAAI,CAACV,EAAE;QAEvC,oBACEL,OAAA;UAEEkB,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACY,IAAI,CAACV,EAAE,CAAE;UACrCG,SAAS,EAAE,uFACTS,QAAQ,GACJ,+CAA+C,GAC/C,kDAAkD,EACrD;UAAAR,QAAA,gBAEHT,OAAA,CAACgB,IAAI;YAACR,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5Bb,OAAA;YAAMQ,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEM,IAAI,CAACT;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAT5CE,IAAI,CAACV,EAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUN,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNb,OAAA;MAAKQ,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/CT,OAAA;QAAQQ,SAAS,EAAC,gNAAgN;QAAAC,QAAA,gBAChOT,OAAA;UAAAS,QAAA,EAAM;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/Bb,OAAA,CAACJ,SAAS;UAACY,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GAzDIlB,OAA+B;AA2DrC,eAAeA,OAAO;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}