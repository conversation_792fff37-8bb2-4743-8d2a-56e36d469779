{"ast": null, "code": "import React from'react';import{Mic2,Zap,Headphones,Trophy,Setting<PERSON>,ChevronUp,User,Check}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Sidebar=_ref=>{let{activeView,onViewChange}=_ref;const navigationItems=[{id:'recording',label:'Įrašymas',icon:Mic2},{id:'transcription',label:'Transkribavimas',icon:Zap},{id:'transcript',label:'Rezultatai',icon:Headphones},{id:'todos',label:'Užduotys',icon:Check},{id:'goals',label:'Tikslai',icon:Trophy},{id:'settings',label:'Nustatymai',icon:Settings}];return/*#__PURE__*/_jsxs(\"div\",{className:\"w-64 bg-white/10 backdrop-blur-xl border-r border-white/20 h-screen fixed left-0 top-0 z-40\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-6 border-b border-white/20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(User,{className:\"h-6 w-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-white font-semibold\",children:\"Your Name\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white/60 text-sm\",children:\"UI UX Designer\"})]})]})}),/*#__PURE__*/_jsx(\"nav\",{className:\"p-4 space-y-2\",children:navigationItems.map(item=>{const Icon=item.icon;const isActive=activeView===item.id;return/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onViewChange(item.id),className:`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${isActive?'bg-white/20 text-white border border-white/30':'text-white/70 hover:text-white hover:bg-white/10'}`,children:[/*#__PURE__*/_jsx(Icon,{className:\"h-5 w-5\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:item.label})]},item.id);})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-6 left-4 right-4\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center space-x-2 hover:from-purple-600 hover:to-pink-600 transition-all duration-200\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Upgrade to premium\"}),/*#__PURE__*/_jsx(ChevronUp,{className:\"h-4 w-4\"})]})})]});};export default Sidebar;", "map": {"version": 3, "names": ["React", "Mic2", "Zap", "Headphones", "Trophy", "Settings", "ChevronUp", "User", "Check", "jsx", "_jsx", "jsxs", "_jsxs", "Sidebar", "_ref", "activeView", "onViewChange", "navigationItems", "id", "label", "icon", "className", "children", "map", "item", "Icon", "isActive", "onClick"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { \n  Mic2, \n  Zap, \n  Headphones, \n  FileText, \n  Trophy, \n  Setting<PERSON>, \n  ChevronUp,\n  User,\n  Check\n} from 'lucide-react';\n\ntype ViewType = 'todos' | 'recording' | 'transcription' | 'transcript' | 'invoices' | 'goals' | 'settings';\n\ninterface SidebarProps {\n  activeView: ViewType;\n  onViewChange: (view: ViewType) => void;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ activeView, onViewChange }) => {\n  const navigationItems: { id: ViewType; label: string; icon: React.ComponentType<any> }[] = [\n    { id: 'recording', label: 'Įrašymas', icon: Mic2 },\n    { id: 'transcription', label: 'Transkribavimas', icon: Zap },\n    { id: 'transcript', label: 'Rezultatai', icon: Headphones },\n    { id: 'todos', label: 'Užduotys', icon: Check },\n    { id: 'goals', label: 'Tiksla<PERSON>', icon: Trophy },\n    { id: 'settings', label: 'Nustatymai', icon: Settings },\n  ];\n\n  return (\n    <div className=\"w-64 bg-white/10 backdrop-blur-xl border-r border-white/20 h-screen fixed left-0 top-0 z-40\">\n      {/* User Profile */}\n      <div className=\"p-6 border-b border-white/20\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\n            <User className=\"h-6 w-6 text-white\" />\n          </div>\n          <div>\n            <h3 className=\"text-white font-semibold\">Your Name</h3>\n            <p className=\"text-white/60 text-sm\">UI UX Designer</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"p-4 space-y-2\">\n        {navigationItems.map((item) => {\n          const Icon = item.icon;\n          const isActive = activeView === item.id;\n          \n          return (\n            <button\n              key={item.id}\n              onClick={() => onViewChange(item.id)}\n              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${\n                isActive\n                  ? 'bg-white/20 text-white border border-white/30'\n                  : 'text-white/70 hover:text-white hover:bg-white/10'\n              }`}\n            >\n              <Icon className=\"h-5 w-5\" />\n              <span className=\"font-medium\">{item.label}</span>\n            </button>\n          );\n        })}\n      </nav>\n\n      {/* Premium Upgrade */}\n      <div className=\"absolute bottom-6 left-4 right-4\">\n        <button className=\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center space-x-2 hover:from-purple-600 hover:to-pink-600 transition-all duration-200\">\n          <span>Upgrade to premium</span>\n          <ChevronUp className=\"h-4 w-4\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,IAAI,CACJC,GAAG,CACHC,UAAU,CAEVC,MAAM,CACNC,QAAQ,CACRC,SAAS,CACTC,IAAI,CACJC,KAAK,KACA,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAStB,KAAM,CAAAC,OAA+B,CAAGC,IAAA,EAAkC,IAAjC,CAAEC,UAAU,CAAEC,YAAa,CAAC,CAAAF,IAAA,CACnE,KAAM,CAAAG,eAAkF,CAAG,CACzF,CAAEC,EAAE,CAAE,WAAW,CAAEC,KAAK,CAAE,UAAU,CAAEC,IAAI,CAAEnB,IAAK,CAAC,CAClD,CAAEiB,EAAE,CAAE,eAAe,CAAEC,KAAK,CAAE,iBAAiB,CAAEC,IAAI,CAAElB,GAAI,CAAC,CAC5D,CAAEgB,EAAE,CAAE,YAAY,CAAEC,KAAK,CAAE,YAAY,CAAEC,IAAI,CAAEjB,UAAW,CAAC,CAC3D,CAAEe,EAAE,CAAE,OAAO,CAAEC,KAAK,CAAE,UAAU,CAAEC,IAAI,CAAEZ,KAAM,CAAC,CAC/C,CAAEU,EAAE,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAEhB,MAAO,CAAC,CAC/C,CAAEc,EAAE,CAAE,UAAU,CAAEC,KAAK,CAAE,YAAY,CAAEC,IAAI,CAAEf,QAAS,CAAC,CACxD,CAED,mBACEO,KAAA,QAAKS,SAAS,CAAC,6FAA6F,CAAAC,QAAA,eAE1GZ,IAAA,QAAKW,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CV,KAAA,QAAKS,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CZ,IAAA,QAAKW,SAAS,CAAC,uGAAuG,CAAAC,QAAA,cACpHZ,IAAA,CAACH,IAAI,EAACc,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACpC,CAAC,cACNT,KAAA,QAAAU,QAAA,eACEZ,IAAA,OAAIW,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cACvDZ,IAAA,MAAGW,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,EACpD,CAAC,EACH,CAAC,CACH,CAAC,cAGNZ,IAAA,QAAKW,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BL,eAAe,CAACM,GAAG,CAAEC,IAAI,EAAK,CAC7B,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACJ,IAAI,CACtB,KAAM,CAAAM,QAAQ,CAAGX,UAAU,GAAKS,IAAI,CAACN,EAAE,CAEvC,mBACEN,KAAA,WAEEe,OAAO,CAAEA,CAAA,GAAMX,YAAY,CAACQ,IAAI,CAACN,EAAE,CAAE,CACrCG,SAAS,CAAE,uFACTK,QAAQ,CACJ,+CAA+C,CAC/C,kDAAkD,EACrD,CAAAJ,QAAA,eAEHZ,IAAA,CAACe,IAAI,EAACJ,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5BX,IAAA,SAAMW,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEE,IAAI,CAACL,KAAK,CAAO,CAAC,GAT5CK,IAAI,CAACN,EAUJ,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,cAGNR,IAAA,QAAKW,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC/CV,KAAA,WAAQS,SAAS,CAAC,gNAAgN,CAAAC,QAAA,eAChOZ,IAAA,SAAAY,QAAA,CAAM,oBAAkB,CAAM,CAAC,cAC/BZ,IAAA,CAACJ,SAAS,EAACe,SAAS,CAAC,SAAS,CAAE,CAAC,EAC3B,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}