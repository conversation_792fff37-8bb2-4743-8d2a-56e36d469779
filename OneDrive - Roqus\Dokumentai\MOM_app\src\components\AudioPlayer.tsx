import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, SkipBack, SkipForward } from 'lucide-react';

interface AudioPlayerProps {
  audioBlob?: Blob;
  audioUrl?: string;
  title?: string;
  className?: string;
}

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioBlob,
  audioUrl,
  title = 'Audio įrašas',
  className = '',
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [audioSrc, setAudioSrc] = useState<string>('');

  // Initialize audio source
  useEffect(() => {
    if (audioBlob) {
      const url = URL.createObjectURL(audioBlob);
      setAudioSrc(url);
      return () => URL.revokeObjectURL(url);
    } else if (audioUrl) {
      setAudioSrc(audioUrl);
    }
  }, [audioBlob, audioUrl]);

  // Audio event listeners
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioSrc]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const seekTime = (parseFloat(e.target.value) / 100) * duration;
    audio.currentTime = seekTime;
    setCurrentTime(seekTime);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    const newVolume = parseFloat(e.target.value) / 100;
    
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
    
    if (audio) {
      audio.volume = newVolume;
    }
  };

  const toggleMute = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isMuted) {
      audio.volume = volume;
      setIsMuted(false);
    } else {
      audio.volume = 0;
      setIsMuted(true);
    }
  };

  const skip = (seconds: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = Math.max(0, Math.min(duration, currentTime + seconds));
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

  if (!audioSrc) {
    return (
      <div className={`gradient-border-fade rounded-3xl shadow-soft bg-unique-gradient-1 backdrop-blur-2xl p-4 transition-smooth hover:shadow-elegant border border-white/30 ring-1 ring-white/15 ${className}`}>
        <div className="flex items-center justify-center text-gray-500">
          <span className="text-sm">Nėra audio įrašo</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-2 backdrop-blur-2xl p-4 transition-smooth hover:shadow-primary hover-gradient-shift border border-white/30 ring-1 ring-white/15 ${className}`}>
      <audio ref={audioRef} src={audioSrc} preload="metadata" />
      
      {/* Title */}
      {title && (
        <div className="mb-3">
          <h4 className="text-sm font-medium text-gray-900 truncate">{title}</h4>
        </div>
      )}

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="range"
            min="0"
            max="100"
            value={progress}
            onChange={handleSeek}
            className="w-full h-2 bg-gray-200 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            style={{
              background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${progress}%, #e5e7eb ${progress}%, #e5e7eb 100%)`,
            }}
          />
        </div>
        <div className="flex justify-between mt-1 text-xs text-gray-500">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* Skip backward */}
          <button
            onClick={() => skip(-10)}
            className="p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/40 ring-1 ring-white/20 shadow-soft hover:shadow-elegant"
            title="Atšokti 10s"
          >
            <SkipBack className="h-4 w-4" />
          </button>

          {/* Play/Pause */}
          <button
            onClick={togglePlay}
            className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 text-white rounded-full shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-2 focus:ring-blue-500/20 border border-white/30 ring-1 ring-white/15 hover:border-blue-300/40"
          >
            {isPlaying ? (
              <Pause className="w-4 h-4" />
            ) : (
              <Play className="w-4 h-4 ml-0.5" />
            )}
          </button>

          {/* Skip forward */}
          <button
            onClick={() => skip(10)}
            className="p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/40 ring-1 ring-white/20 shadow-soft hover:shadow-elegant"
            title="Prašokti 10s"
          >
            <SkipForward className="h-4 w-4" />
          </button>
        </div>

        {/* Volume Control */}
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleMute}
            className="p-1.5 text-gray-600 hover:text-gray-800 bg-gradient-to-r from-white/60 via-slate-50/50 to-white/60 hover:from-white/80 hover:via-slate-100/60 hover:to-white/80 rounded-full transition-smooth border border-white/40 ring-1 ring-white/20 shadow-soft hover:shadow-elegant"
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="h-4 w-4" />
            ) : (
              <Volume2 className="h-4 w-4" />
            )}
          </button>
          <input
            type="range"
            min="0"
            max="100"
            value={isMuted ? 0 : volume * 100}
            onChange={handleVolumeChange}
            className="w-20 h-2 bg-gray-200 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            style={{
              background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${isMuted ? 0 : volume * 100}%, #e5e7eb ${isMuted ? 0 : volume * 100}%, #e5e7eb 100%)`,
            }}
          />
        </div>
      </div>
    </div>
  );
}; 