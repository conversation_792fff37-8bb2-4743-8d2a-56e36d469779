{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\DynamicAudioVisualizer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DynamicAudioVisualizer = ({\n  recordingState,\n  className = ''\n}) => {\n  _s();\n  const {\n    isRecording,\n    isPaused,\n    audioLevel\n  } = recordingState;\n  const [visualizerData, setVisualizerData] = useState(new Array(8).fill(0));\n  useEffect(() => {\n    if (!isRecording || isPaused) {\n      // Reset to baseline when not recording\n      setVisualizerData(new Array(8).fill(0));\n      return;\n    }\n\n    // Create dynamic visualization based on audio level\n    const interval = setInterval(() => {\n      setVisualizerData(prev => {\n        const newData = [...prev];\n\n        // Create wave-like pattern based on audio level\n        const baseLevel = audioLevel * 0.8;\n        const randomVariation = 0.2;\n        for (let i = 0; i < newData.length; i++) {\n          // Create a wave pattern with some randomness\n          const waveOffset = Math.sin(Date.now() / 200 + i * 0.5) * 0.3;\n          const randomOffset = (Math.random() - 0.5) * randomVariation;\n          newData[i] = Math.max(0, Math.min(1, baseLevel + waveOffset + randomOffset));\n        }\n        return newData;\n      });\n    }, 50); // Update every 50ms for smooth animation\n\n    return () => clearInterval(interval);\n  }, [isRecording, isPaused, audioLevel]);\n  if (!isRecording) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center justify-center space-x-1.5 ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-0.5\",\n      children: visualizerData.map((level, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-t from-blue-400 to-blue-300 rounded-full transition-all duration-100 ease-out\",\n        style: {\n          width: '3px',\n          height: `${Math.max(4, level * 16)}px`,\n          opacity: isPaused ? 0.3 : 0.7 + level * 0.3,\n          transform: `scaleY(${isPaused ? 0.5 : 1})`\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicAudioVisualizer, \"h4x1Wpv7B2SGTADlgAkahY4OMPM=\");\n_c = DynamicAudioVisualizer;\nvar _c;\n$RefreshReg$(_c, \"DynamicAudioVisualizer\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsxDEV", "_jsxDEV", "DynamicAudioVisualizer", "recordingState", "className", "_s", "isRecording", "isPaused", "audioLevel", "visualizerData", "setVisualizerData", "Array", "fill", "interval", "setInterval", "prev", "newData", "baseLevel", "randomVariation", "i", "length", "waveOffset", "Math", "sin", "Date", "now", "randomOffset", "random", "max", "min", "clearInterval", "children", "map", "level", "index", "style", "width", "height", "opacity", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/DynamicAudioVisualizer.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { RecordingState } from '../types/meeting';\n\ninterface DynamicAudioVisualizerProps {\n  recordingState: RecordingState;\n  className?: string;\n}\n\nexport const DynamicAudioVisualizer: React.FC<DynamicAudioVisualizerProps> = ({\n  recordingState,\n  className = '',\n}) => {\n  const { isRecording, isPaused, audioLevel } = recordingState;\n  const [visualizerData, setVisualizerData] = useState<number[]>(new Array(8).fill(0));\n\n  useEffect(() => {\n    if (!isRecording || isPaused) {\n      // Reset to baseline when not recording\n      setVisualizerData(new Array(8).fill(0));\n      return;\n    }\n\n    // Create dynamic visualization based on audio level\n    const interval = setInterval(() => {\n      setVisualizerData(prev => {\n        const newData = [...prev];\n        \n        // Create wave-like pattern based on audio level\n        const baseLevel = audioLevel * 0.8;\n        const randomVariation = 0.2;\n        \n        for (let i = 0; i < newData.length; i++) {\n          // Create a wave pattern with some randomness\n          const waveOffset = Math.sin((Date.now() / 200) + (i * 0.5)) * 0.3;\n          const randomOffset = (Math.random() - 0.5) * randomVariation;\n          \n          newData[i] = Math.max(0, Math.min(1, baseLevel + waveOffset + randomOffset));\n        }\n        \n        return newData;\n      });\n    }, 50); // Update every 50ms for smooth animation\n\n    return () => clearInterval(interval);\n  }, [isRecording, isPaused, audioLevel]);\n\n  if (!isRecording) {\n    return null;\n  }\n\n  return (\n    <div className={`flex items-center justify-center space-x-1.5 ${className}`}>\n      {/* Dynamic bars visualization */}\n      <div className=\"flex items-center space-x-0.5\">\n        {visualizerData.map((level, index) => (\n          <div\n            key={index}\n            className=\"bg-gradient-to-t from-blue-400 to-blue-300 rounded-full transition-all duration-100 ease-out\"\n            style={{\n              width: '3px',\n              height: `${Math.max(4, level * 16)}px`,\n              opacity: isPaused ? 0.3 : 0.7 + (level * 0.3),\n              transform: `scaleY(${isPaused ? 0.5 : 1})`,\n            }}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQnD,OAAO,MAAMC,sBAA6D,GAAGA,CAAC;EAC5EC,cAAc;EACdC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,WAAW;IAAEC,QAAQ;IAAEC;EAAW,CAAC,GAAGL,cAAc;EAC5D,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGX,QAAQ,CAAW,IAAIY,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EAEpFd,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,WAAW,IAAIC,QAAQ,EAAE;MAC5B;MACAG,iBAAiB,CAAC,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;MACvC;IACF;;IAEA;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCJ,iBAAiB,CAACK,IAAI,IAAI;QACxB,MAAMC,OAAO,GAAG,CAAC,GAAGD,IAAI,CAAC;;QAEzB;QACA,MAAME,SAAS,GAAGT,UAAU,GAAG,GAAG;QAClC,MAAMU,eAAe,GAAG,GAAG;QAE3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC;UACA,MAAME,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAKN,CAAC,GAAG,GAAI,CAAC,GAAG,GAAG;UACjE,MAAMO,YAAY,GAAG,CAACJ,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,GAAG,IAAIT,eAAe;UAE5DF,OAAO,CAACG,CAAC,CAAC,GAAGG,IAAI,CAACM,GAAG,CAAC,CAAC,EAAEN,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEZ,SAAS,GAAGI,UAAU,GAAGK,YAAY,CAAC,CAAC;QAC9E;QAEA,OAAOV,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;IAER,OAAO,MAAMc,aAAa,CAACjB,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACP,WAAW,EAAEC,QAAQ,EAAEC,UAAU,CAAC,CAAC;EAEvC,IAAI,CAACF,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,oBACEL,OAAA;IAAKG,SAAS,EAAE,gDAAgDA,SAAS,EAAG;IAAA2B,QAAA,eAE1E9B,OAAA;MAAKG,SAAS,EAAC,+BAA+B;MAAA2B,QAAA,EAC3CtB,cAAc,CAACuB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/BjC,OAAA;QAEEG,SAAS,EAAC,8FAA8F;QACxG+B,KAAK,EAAE;UACLC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,GAAGf,IAAI,CAACM,GAAG,CAAC,CAAC,EAAEK,KAAK,GAAG,EAAE,CAAC,IAAI;UACtCK,OAAO,EAAE/B,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAI0B,KAAK,GAAG,GAAI;UAC7CM,SAAS,EAAE,UAAUhC,QAAQ,GAAG,GAAG,GAAG,CAAC;QACzC;MAAE,GAPG2B,KAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CA7DWH,sBAA6D;AAAA0C,EAAA,GAA7D1C,sBAA6D;AAAA,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}