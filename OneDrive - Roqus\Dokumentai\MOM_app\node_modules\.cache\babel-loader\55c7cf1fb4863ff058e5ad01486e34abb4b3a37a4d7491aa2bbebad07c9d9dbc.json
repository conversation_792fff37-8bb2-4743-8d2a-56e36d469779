{"ast": null, "code": "import React,{useState,useCallback,useEffect}from'react';import{ErrorBoundary,Sidebar,RecordingPage,TranscriptionPage,ResultsPage,TodosPage}from'./components';import Button from'./components/Button';import{useAudioRecorder,useTranscription}from'./hooks';import{createDemoMeetings}from'./utils/demoData';import{Plus,Mic2,TestTube,Settings,Trophy}from'lucide-react';import'./styles/background.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[meetings,setMeetings]=useState([]);const[currentMeeting,setCurrentMeeting]=useState(null);const[selectedMeetingForTranscript,setSelectedMeetingForTranscript]=useState(null);const[activeView,setActiveView]=useState('todos');const[isMobileMenuOpen,setIsMobileMenuOpen]=useState(false);const[showTitleModal,setShowTitleModal]=useState(false);const[newMeetingTitle,setNewMeetingTitle]=useState('');const{recordingState,startRecording,stopRecording,pauseRecording,resumeRecording}=useAudioRecorder();const{transcript,isTranscribing,transcribeAudioEnhanced,cancelTranscription,editSegment,clearTranscript,clearError,currentTranscriptionId,progress,isWhisperConfigured}=useTranscription();const handleStartRecording=useCallback(async title=>{try{await startRecording();const newMeeting={id:Date.now().toString(),title:title,date:new Date(),duration:0,status:'recording',transcriptionStatus:{state:'not_started'}};setCurrentMeeting(newMeeting);setMeetings(prev=>[newMeeting,...prev]);clearTranscript();}catch(error){console.error('Nepavyko pradėti įrašymo:',error);throw error;}},[startRecording,clearTranscript]);const handleStartRecordingWithTitle=useCallback(()=>{setNewMeetingTitle(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);setShowTitleModal(true);},[]);const confirmStartRecording=useCallback(()=>{if(newMeetingTitle.trim()){handleStartRecording(newMeetingTitle.trim());setShowTitleModal(false);setNewMeetingTitle('');setActiveView('recording');}},[newMeetingTitle,handleStartRecording]);const handleStopRecording=useCallback(async()=>{try{const audioBlob=await stopRecording();if(currentMeeting&&audioBlob){const updatedMeeting={...currentMeeting,status:'completed',duration:Math.floor((Date.now()-currentMeeting.date.getTime())/1000),audioBlob,transcriptionStatus:{state:'not_started'}};setCurrentMeeting(updatedMeeting);setMeetings(prev=>prev.map(m=>m.id===currentMeeting.id?updatedMeeting:m));}}catch(error){console.error('Nepavyko sustabdyti įrašymo:',error);alert('Nepavyko sustabdyti įrašymo.');}},[stopRecording,currentMeeting]);const handleStartTranscription=useCallback(async meetingId=>{const meeting=meetings.find(m=>m.id===meetingId);if(!meeting||!meeting.audioBlob)return;// Update meeting status to pending\nconst updatedMeeting={...meeting,transcriptionStatus:{state:'pending',startedAt:new Date()}};setMeetings(prev=>prev.map(m=>m.id===meetingId?updatedMeeting:m));setSelectedMeetingForTranscript(updatedMeeting);try{// Start professional transcription\nconst result=await transcribeAudioEnhanced(meeting.audioBlob,meetingId,{onProgress:progress=>{setMeetings(prev=>prev.map(m=>m.id===meetingId?{...m,transcriptionStatus:{...m.transcriptionStatus,progress,state:'processing'}}:m));},onStatusUpdate:status=>{setMeetings(prev=>prev.map(m=>m.id===meetingId?{...m,transcriptionStatus:status}:m));},enhanceSpeakers:true});// Update meeting with completed transcription\nconst completedMeeting={...updatedMeeting,transcript:result.segments,participants:result.speakers,metadata:result.metadata,transcriptionStatus:{state:'completed',progress:100,startedAt:updatedMeeting.transcriptionStatus.startedAt,completedAt:new Date()}};setMeetings(prev=>prev.map(m=>m.id===meetingId?completedMeeting:m));setSelectedMeetingForTranscript(completedMeeting);console.log('✅ Transkribavimas sėkmingai baigtas:',{segments:result.segments.length,speakers:result.speakers.length,words:result.metadata.totalWords,confidence:result.metadata.averageConfidence});}catch(error){console.error('❌ Transkribavimo klaida:',error);const errorMeeting={...updatedMeeting,transcriptionStatus:{state:'failed',error:error.message,startedAt:updatedMeeting.transcriptionStatus.startedAt}};setMeetings(prev=>prev.map(m=>m.id===meetingId?errorMeeting:m));}},[meetings,transcribeAudioEnhanced]);const handleCancelTranscription=useCallback(meetingId=>{cancelTranscription();setMeetings(prev=>prev.map(m=>m.id===meetingId?{...m,transcriptionStatus:{...m.transcriptionStatus,state:'cancelled'}}:m));},[cancelTranscription]);const handleEditSegment=useCallback((meetingId,segmentId,newText)=>{editSegment(segmentId,newText);// Update the meeting's transcript\nsetMeetings(prev=>prev.map(meeting=>{var _meeting$transcript;return meeting.id===meetingId?{...meeting,transcript:(_meeting$transcript=meeting.transcript)===null||_meeting$transcript===void 0?void 0:_meeting$transcript.map(segment=>segment.id===segmentId?{...segment,text:newText,isEdited:true,editedAt:new Date(),editedBy:'user'}:segment)}:meeting;}));},[editSegment]);const handleSelectMeeting=useCallback(meeting=>{setCurrentMeeting(meeting);if(meeting.transcript&&meeting.transcript.length>0){setSelectedMeetingForTranscript(meeting);setActiveView('transcript');}},[]);const handleDeleteMeeting=useCallback(meetingId=>{setMeetings(prev=>prev.filter(m=>m.id!==meetingId));if((currentMeeting===null||currentMeeting===void 0?void 0:currentMeeting.id)===meetingId){setCurrentMeeting(null);}if((selectedMeetingForTranscript===null||selectedMeetingForTranscript===void 0?void 0:selectedMeetingForTranscript.id)===meetingId){setSelectedMeetingForTranscript(null);}},[currentMeeting,selectedMeetingForTranscript]);const handleExportMeeting=useCallback(meeting=>{const exportData={title:meeting.title,date:meeting.date.toISOString(),duration:meeting.duration,transcript:meeting.transcript||transcript,participants:meeting.participants||[],metadata:meeting.metadata||{},transcriptionStatus:meeting.transcriptionStatus};const dataStr=JSON.stringify(exportData,null,2);const dataUri='data:application/json;charset=utf-8,'+encodeURIComponent(dataStr);const exportFileDefaultName=`meeting-${meeting.title.replace(/\\s+/g,'-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;const linkElement=document.createElement('a');linkElement.setAttribute('href',dataUri);linkElement.setAttribute('download',exportFileDefaultName);linkElement.click();},[transcript]);const loadDemoData=useCallback(()=>{const demoMeetings=createDemoMeetings().map(meeting=>({...meeting,transcriptionStatus:{state:'completed',progress:100,completedAt:meeting.date}}));setMeetings(demoMeetings);},[]);// Close mobile menu when clicking outside or on escape\nuseEffect(()=>{const handleClickOutside=event=>{const target=event.target;if(isMobileMenuOpen&&!target.closest('.mobile-menu-container')){setIsMobileMenuOpen(false);}};const handleEscape=event=>{if(event.key==='Escape'&&isMobileMenuOpen){setIsMobileMenuOpen(false);}};if(isMobileMenuOpen){document.addEventListener('mousedown',handleClickOutside);document.addEventListener('keydown',handleEscape);// Prevent body scroll when mobile menu is open\ndocument.body.classList.add('mobile-menu-open');}else{document.body.classList.remove('mobile-menu-open');}return()=>{document.removeEventListener('mousedown',handleClickOutside);document.removeEventListener('keydown',handleEscape);document.body.classList.remove('mobile-menu-open');};},[isMobileMenuOpen]);return/*#__PURE__*/_jsxs(ErrorBoundary,{children:[showTitleModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl max-w-md w-full p-6 animate-scale-in\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center space-y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-xl border border-blue-400/20\",children:/*#__PURE__*/_jsx(Mic2,{className:\"h-6 w-6 text-blue-400\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white\",children:\"Naujas pokalbis\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-white/60\",children:\"\\u012Eveskite pokalbio pavadinim\\u0105\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newMeetingTitle,onChange:e=>setNewMeetingTitle(e.target.value),onKeyDown:e=>e.key==='Enter'&&confirmStartRecording(),className:\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200\",placeholder:\"Pokalbio pavadinimas...\",autoFocus:true}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-3\",children:[/*#__PURE__*/_jsx(Button,{onClick:()=>{setShowTitleModal(false);setNewMeetingTitle('');},variant:\"secondary\",fullWidth:true,children:\"At\\u0161aukti\"}),/*#__PURE__*/_jsx(Button,{onClick:confirmStartRecording,disabled:!newMeetingTitle.trim(),variant:\"primary\",fullWidth:true,children:\"Prad\\u0117ti\"})]})]})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen elegant-background font-inter\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"elegant-grid\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl\"})]}),/*#__PURE__*/_jsx(Sidebar,{activeView:activeView,onViewChange:setActiveView}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-64 p-6\",children:[activeView==='todos'&&/*#__PURE__*/_jsx(TodosPage,{}),activeView==='recording'&&/*#__PURE__*/_jsx(RecordingPage,{recordingState:recordingState,currentMeeting:currentMeeting,onStartRecording:handleStartRecording,onStopRecording:handleStopRecording,onPauseRecording:pauseRecording,onResumeRecording:resumeRecording}),activeView==='transcription'&&/*#__PURE__*/_jsx(TranscriptionPage,{meetings:meetings,onStartTranscription:handleStartTranscription,onCancelTranscription:handleCancelTranscription,isTranscribing:isTranscribing,currentTranscriptionId:currentTranscriptionId,onDeleteMeeting:handleDeleteMeeting,onViewResults:()=>setActiveView('transcript')}),activeView==='transcript'&&/*#__PURE__*/_jsx(ResultsPage,{meetings:meetings,onDeleteMeeting:handleDeleteMeeting,onGoToTranscription:()=>setActiveView('transcription')}),activeView==='goals'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-gradient-to-br from-yellow-500/80 to-orange-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",children:/*#__PURE__*/_jsx(Trophy,{className:\"h-6 w-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-white\",children:\"Tikslai\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-white/60\",children:\"Sekite savo pokalbi\\u0173 \\u012Fra\\u0161ymo tikslus\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Trophy,{className:\"h-16 w-16 text-white/20 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white/60\",children:\"Tiksl\\u0173 funkcija bus prid\\u0117ta v\\u0117liau\"})]})]})}),activeView==='settings'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-gradient-to-br from-gray-500/80 to-gray-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",children:/*#__PURE__*/_jsx(Settings,{className:\"h-6 w-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-white\",children:\"Nustatymai\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-white/60\",children:\"Programos konfig\\u016Bracija\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Settings,{className:\"h-16 w-16 text-white/20 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white/60\",children:\"Nustatym\\u0173 funkcija bus prid\\u0117ta v\\u0117liau\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"fixed bottom-6 right-6 flex flex-col gap-3 z-40\",children:[meetings.length===0&&/*#__PURE__*/_jsx(Button,{onClick:loadDemoData,icon:/*#__PURE__*/_jsx(TestTube,{className:\"h-4 w-4\"}),variant:\"secondary\",fullWidth:true,children:\"Demo\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>{if(!recordingState.isRecording){handleStartRecordingWithTitle();}},disabled:recordingState.isRecording,icon:/*#__PURE__*/_jsx(Plus,{className:\"h-4 w-4\"}),variant:\"primary\",fullWidth:true,children:\"Naujas pokalbis\"})]})]})]})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "Error<PERSON>ou<PERSON><PERSON>", "Sidebar", "RecordingPage", "TranscriptionPage", "ResultsPage", "TodosPage", "<PERSON><PERSON>", "useAudioRecorder", "useTranscription", "createDemoMeetings", "Plus", "Mic2", "TestTube", "Settings", "Trophy", "jsx", "_jsx", "jsxs", "_jsxs", "App", "meetings", "setMeetings", "currentMeeting", "setCurrentMeeting", "selectedMeetingForTranscript", "setSelectedMeetingForTranscript", "activeView", "setActiveView", "isMobileMenuOpen", "setIsMobileMenuOpen", "showTitleModal", "setShowTitleModal", "newMeetingTitle", "setNewMeetingTitle", "recordingState", "startRecording", "stopRecording", "pauseRecording", "resumeRecording", "transcript", "isTranscribing", "transcribeAudioEnhanced", "cancelTranscription", "editSegment", "clearTranscript", "clearError", "currentTranscriptionId", "progress", "isWhisperConfigured", "handleStartRecording", "title", "newMeeting", "id", "Date", "now", "toString", "date", "duration", "status", "transcriptionStatus", "state", "prev", "error", "console", "handleStartRecordingWithTitle", "toLocaleString", "confirmStartRecording", "trim", "handleStopRecording", "audioBlob", "updatedMeeting", "Math", "floor", "getTime", "map", "m", "alert", "handleStartTranscription", "meetingId", "meeting", "find", "startedAt", "result", "onProgress", "onStatusUpdate", "enhanceSpeakers", "completedMeeting", "segments", "participants", "speakers", "metadata", "completedAt", "log", "length", "words", "totalWords", "confidence", "averageConfidence", "errorMeeting", "message", "handleCancelTranscription", "handleEditSegment", "segmentId", "newText", "_meeting$transcript", "segment", "text", "isEdited", "editedAt", "editedBy", "handleSelectMeeting", "handleDeleteMeeting", "filter", "handleExportMeeting", "exportData", "toISOString", "dataStr", "JSON", "stringify", "dataUri", "encodeURIComponent", "exportFileDefaultName", "replace", "toLowerCase", "split", "linkElement", "document", "createElement", "setAttribute", "click", "loadDemoData", "demoMeetings", "handleClickOutside", "event", "target", "closest", "handleEscape", "key", "addEventListener", "body", "classList", "add", "remove", "removeEventListener", "children", "className", "type", "value", "onChange", "e", "onKeyDown", "placeholder", "autoFocus", "onClick", "variant", "fullWidth", "disabled", "onViewChange", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "onStartTranscription", "onCancelTranscription", "onDeleteMeeting", "onViewResults", "onGoToTranscription", "icon", "isRecording"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from 'react';\nimport {\n  <PERSON>rror<PERSON>ou<PERSON>ry,\n  WhisperStatusIndicator,\n  TranscriptionManager,\n  ProfessionalTranscriptViewer,\n  RecordingPanel,\n  Sidebar,\n  CardsSection,\n  AnalyticsSection,\n  TransactionHistory,\n  ShareContacts,\n  RecordingPage,\n  TranscriptionPage,\n  ResultsPage,\n  TodosPage\n} from './components';\nimport Button from './components/Button';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { Meeting, TranscriptionStatus, Speaker } from './types/meeting';\nimport { createDemoMeetings } from './utils/demoData';\nimport { identifySpeakers } from './services/speakerService';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List, Square, Menu, X, Trophy } from 'lucide-react';\nimport './styles/background.css';\n\nfunction App() {\n  const [meetings, setMeetings] = useState<Meeting[]>([]);\n  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);\n  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript' | 'invoices' | 'goals' | 'settings' | 'todos'>('todos');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showTitleModal, setShowTitleModal] = useState(false);\n  const [newMeetingTitle, setNewMeetingTitle] = useState('');\n\n  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();\n  const { \n    transcript, \n    isTranscribing, \n    transcribeAudioEnhanced, \n    cancelTranscription,\n    editSegment,\n    clearTranscript, \n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n\n  const handleStartRecording = useCallback(async (title: string) => {\n    try {\n      await startRecording();\n      \n      const newMeeting: Meeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started',\n        },\n      };\n      \n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n\n  const handleStartRecordingWithTitle = useCallback(() => {\n    setNewMeetingTitle(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n    setShowTitleModal(true);\n  }, []);\n\n  const confirmStartRecording = useCallback(() => {\n    if (newMeetingTitle.trim()) {\n      handleStartRecording(newMeetingTitle.trim());\n      setShowTitleModal(false);\n      setNewMeetingTitle('');\n      setActiveView('recording');\n    }\n  }, [newMeetingTitle, handleStartRecording]);\n\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      \n      if (currentMeeting && audioBlob) {\n        const updatedMeeting: Meeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started',\n          },\n        };\n\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => \n          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)\n        );\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n\n  const handleStartTranscription = useCallback(async (meetingId: string) => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting: Meeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date(),\n      },\n    };\n\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: (progress) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: { \n                    ...m.transcriptionStatus, \n                    progress,\n                    state: 'processing' \n                  } \n                }\n              : m\n          ));\n        },\n        onStatusUpdate: (status) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: status \n                }\n              : m\n          ));\n        },\n        enhanceSpeakers: true,\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting: Meeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date(),\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence,\n      });\n\n    } catch (error: any) {\n      console.error('❌ Transkribavimo klaida:', error);\n      \n      const errorMeeting: Meeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n\n  const handleCancelTranscription = useCallback((meetingId: string) => {\n    cancelTranscription();\n    \n    setMeetings(prev => prev.map(m => \n      m.id === meetingId \n        ? { \n            ...m, \n            transcriptionStatus: { \n              ...m.transcriptionStatus, \n              state: 'cancelled' as const \n            } \n          }\n        : m\n    ));\n  }, [cancelTranscription]);\n\n  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {\n    editSegment(segmentId, newText);\n    \n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => \n      meeting.id === meetingId\n        ? {\n            ...meeting,\n            transcript: meeting.transcript?.map(segment => \n              segment.id === segmentId \n                ? {\n                    ...segment,\n                    text: newText,\n                    isEdited: true,\n                    editedAt: new Date(),\n                    editedBy: 'user'\n                  }\n                : segment\n            ),\n          }\n        : meeting\n    ));\n  }, [editSegment]);\n\n  const handleSelectMeeting = useCallback((meeting: Meeting) => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n\n  const handleDeleteMeeting = useCallback((meetingId: string) => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if (currentMeeting?.id === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if (selectedMeetingForTranscript?.id === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n\n  const handleExportMeeting = useCallback((meeting: Meeting) => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus,\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);\n    \n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    \n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed' as const,\n        progress: 100,\n        completedAt: meeting.date,\n      },\n    }));\n    setMeetings(demoMeetings);\n  }, []);\n\n  // Close mobile menu when clicking outside or on escape\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Element;\n      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isMobileMenuOpen) {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    if (isMobileMenuOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when mobile menu is open\n      document.body.classList.add('mobile-menu-open');\n    } else {\n      document.body.classList.remove('mobile-menu-open');\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('keydown', handleEscape);\n      document.body.classList.remove('mobile-menu-open');\n    };\n  }, [isMobileMenuOpen]);\n\n  return (\n    <ErrorBoundary>\n      {/* Title Input Modal */}\n      {showTitleModal && (\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl max-w-md w-full p-6 animate-scale-in\">\n            <div className=\"space-y-4\">\n              <div className=\"text-center space-y-2\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-xl border border-blue-400/20\">\n                  <Mic2 className=\"h-6 w-6 text-blue-400\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-white\">Naujas pokalbis</h3>\n                <p className=\"text-sm text-white/60\">Įveskite pokalbio pavadinimą</p>\n              </div>\n\n              <div className=\"space-y-3\">\n                <input\n                  type=\"text\"\n                  value={newMeetingTitle}\n                  onChange={(e) => setNewMeetingTitle(e.target.value)}\n                  onKeyDown={(e) => e.key === 'Enter' && confirmStartRecording()}\n                  className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200\"\n                  placeholder=\"Pokalbio pavadinimas...\"\n                  autoFocus\n                />\n\n                {/* Modal mygtukai */}\n                <div className=\"flex gap-3\">\n                  <Button\n                    onClick={() => {\n                      setShowTitleModal(false);\n                      setNewMeetingTitle('');\n                    }}\n                    variant=\"secondary\"\n                    fullWidth\n                  >\n                    Atšaukti\n                  </Button>\n                  <Button\n                    onClick={confirmStartRecording}\n                    disabled={!newMeetingTitle.trim()}\n                    variant=\"primary\"\n                    fullWidth\n                  >\n                    Pradėti\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"min-h-screen elegant-background font-inter\">\n        {/* Elegant Grid Pattern */}\n        <div className=\"elegant-grid\"></div>\n\n        {/* Sophisticated Background Elements */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl\"></div>\n          <div className=\"absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl\"></div>\n        </div>\n\n        {/* Sidebar */}\n        <Sidebar activeView={activeView} onViewChange={setActiveView} />\n\n        {/* Main Content */}\n        <div className=\"ml-64 p-6\">\n          {/* Dashboard View (invoices) */}\n          {activeView === 'todos' && (\n            <TodosPage />\n          )}\n\n          {/* Recording Page */}\n          {activeView === 'recording' && (\n            <RecordingPage\n                        recordingState={recordingState}\n                        currentMeeting={currentMeeting}\n                        onStartRecording={handleStartRecording}\n                        onStopRecording={handleStopRecording}\n                        onPauseRecording={pauseRecording}\n                        onResumeRecording={resumeRecording}\n                      />\n              )}\n\n          {/* Transcription Page */}\n              {activeView === 'transcription' && (\n            <TranscriptionPage\n                      meetings={meetings}\n                      onStartTranscription={handleStartTranscription}\n                      onCancelTranscription={handleCancelTranscription}\n                      isTranscribing={isTranscribing}\n                      currentTranscriptionId={currentTranscriptionId}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onViewResults={() => setActiveView('transcript')}\n                    />\n              )}\n\n          {/* Results Page */}\n              {activeView === 'transcript' && (\n            <ResultsPage\n                      meetings={meetings}\n                      onDeleteMeeting={handleDeleteMeeting}\n                      onGoToTranscription={() => setActiveView('transcription')}\n                    />\n          )}\n\n          {/* Goals Page */}\n          {activeView === 'goals' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\n                <div className=\"flex items-center gap-4 mb-4\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-yellow-500/80 to-orange-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\n                    <Trophy className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h2 className=\"text-xl font-semibold text-white\">Tikslai</h2>\n                    <p className=\"text-sm text-white/60\">Sekite savo pokalbių įrašymo tikslus</p>\n                  </div>\n                </div>\n                <div className=\"text-center py-12\">\n                  <Trophy className=\"h-16 w-16 text-white/20 mx-auto mb-4\" />\n                  <p className=\"text-white/60\">Tikslų funkcija bus pridėta vėliau</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Settings Page */}\n          {activeView === 'settings' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\n                <div className=\"flex items-center gap-4 mb-4\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-gray-500/80 to-gray-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\n                    <Settings className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h2 className=\"text-xl font-semibold text-white\">Nustatymai</h2>\n                    <p className=\"text-sm text-white/60\">Programos konfigūracija</p>\n                  </div>\n                </div>\n                <div className=\"text-center py-12\">\n                  <Settings className=\"h-16 w-16 text-white/20 mx-auto mb-4\" />\n                  <p className=\"text-white/60\">Nustatymų funkcija bus pridėta vėliau</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Demo and New Meeting Buttons */}\n          <div className=\"fixed bottom-6 right-6 flex flex-col gap-3 z-40\">\n            {/* Demo ir Naujas pokalbis mygtukai */}\n            {meetings.length === 0 && (\n              <Button\n                onClick={loadDemoData}\n                icon={<TestTube className=\"h-4 w-4\" />}\n                variant=\"secondary\"\n                fullWidth\n              >\n                Demo\n              </Button>\n            )}\n            <Button\n              onClick={() => {\n                if (!recordingState.isRecording) {\n                  handleStartRecordingWithTitle();\n                }\n              }}\n              disabled={recordingState.isRecording}\n              icon={<Plus className=\"h-4 w-4\" />}\n              variant=\"primary\"\n              fullWidth\n            >\n              Naujas pokalbis\n            </Button>\n          </div>\n        </div>\n      </div>\n    </ErrorBoundary>\n  );\n}\n\nexport default App; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,CAAEC,SAAS,KAAQ,OAAO,CAC/D,OACEC,aAAa,CAKbC,OAAO,CAKPC,aAAa,CACbC,iBAAiB,CACjBC,WAAW,CACXC,SAAS,KACJ,cAAc,CACrB,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,OAASC,gBAAgB,CAAEC,gBAAgB,KAAQ,SAAS,CAE5D,OAASC,kBAAkB,KAAQ,kBAAkB,CAErD,OAAqBC,IAAI,CAAEC,IAAI,CAAEC,QAAQ,CAAOC,QAAQ,CAAyBC,MAAM,KAAQ,cAAc,CAC7G,MAAO,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjC,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGxB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACyB,cAAc,CAAEC,iBAAiB,CAAC,CAAG1B,QAAQ,CAAiB,IAAI,CAAC,CAC1E,KAAM,CAAC2B,4BAA4B,CAAEC,+BAA+B,CAAC,CAAG5B,QAAQ,CAAiB,IAAI,CAAC,CACtG,KAAM,CAAC6B,UAAU,CAAEC,aAAa,CAAC,CAAG9B,QAAQ,CAA6F,OAAO,CAAC,CACjJ,KAAM,CAAC+B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACiC,cAAc,CAAEC,iBAAiB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACmC,eAAe,CAAEC,kBAAkB,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAAEqC,cAAc,CAAEC,cAAc,CAAEC,aAAa,CAAEC,cAAc,CAAEC,eAAgB,CAAC,CAAG/B,gBAAgB,CAAC,CAAC,CAC7G,KAAM,CACJgC,UAAU,CACVC,cAAc,CACdC,uBAAuB,CACvBC,mBAAmB,CACnBC,WAAW,CACXC,eAAe,CACfC,UAAU,CACVC,sBAAsB,CACtBC,QAAQ,CACRC,mBACF,CAAC,CAAGxC,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAAyC,oBAAoB,CAAGnD,WAAW,CAAC,KAAO,CAAAoD,KAAa,EAAK,CAChE,GAAI,CACF,KAAM,CAAAf,cAAc,CAAC,CAAC,CAEtB,KAAM,CAAAgB,UAAmB,CAAG,CAC1BC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CACzBL,KAAK,CAAEA,KAAK,CACZM,IAAI,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAChBI,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAE,WAAW,CACnBC,mBAAmB,CAAE,CACnBC,KAAK,CAAE,aACT,CACF,CAAC,CAEDrC,iBAAiB,CAAC4B,UAAU,CAAC,CAC7B9B,WAAW,CAACwC,IAAI,EAAI,CAACV,UAAU,CAAE,GAAGU,IAAI,CAAC,CAAC,CAC1CjB,eAAe,CAAC,CAAC,CACnB,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAAE,CAAC3B,cAAc,CAAES,eAAe,CAAC,CAAC,CAErC,KAAM,CAAAoB,6BAA6B,CAAGlE,WAAW,CAAC,IAAM,CACtDmC,kBAAkB,CAAC,YAAY,GAAI,CAAAoB,IAAI,CAAC,CAAC,CAACY,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CACpElC,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAmC,qBAAqB,CAAGpE,WAAW,CAAC,IAAM,CAC9C,GAAIkC,eAAe,CAACmC,IAAI,CAAC,CAAC,CAAE,CAC1BlB,oBAAoB,CAACjB,eAAe,CAACmC,IAAI,CAAC,CAAC,CAAC,CAC5CpC,iBAAiB,CAAC,KAAK,CAAC,CACxBE,kBAAkB,CAAC,EAAE,CAAC,CACtBN,aAAa,CAAC,WAAW,CAAC,CAC5B,CACF,CAAC,CAAE,CAACK,eAAe,CAAEiB,oBAAoB,CAAC,CAAC,CAE3C,KAAM,CAAAmB,mBAAmB,CAAGtE,WAAW,CAAC,SAAY,CAClD,GAAI,CACF,KAAM,CAAAuE,SAAS,CAAG,KAAM,CAAAjC,aAAa,CAAC,CAAC,CAEvC,GAAId,cAAc,EAAI+C,SAAS,CAAE,CAC/B,KAAM,CAAAC,cAAuB,CAAG,CAC9B,GAAGhD,cAAc,CACjBoC,MAAM,CAAE,WAAW,CACnBD,QAAQ,CAAEc,IAAI,CAACC,KAAK,CAAC,CAACnB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGhC,cAAc,CAACkC,IAAI,CAACiB,OAAO,CAAC,CAAC,EAAI,IAAI,CAAC,CACzEJ,SAAS,CACTV,mBAAmB,CAAE,CACnBC,KAAK,CAAE,aACT,CACF,CAAC,CAEDrC,iBAAiB,CAAC+C,cAAc,CAAC,CACjCjD,WAAW,CAACwC,IAAI,EACdA,IAAI,CAACa,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACvB,EAAE,GAAK9B,cAAc,CAAC8B,EAAE,CAAGkB,cAAc,CAAGK,CAAC,CAC/D,CAAC,CACH,CACF,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpDc,KAAK,CAAC,8BAA8B,CAAC,CACvC,CACF,CAAC,CAAE,CAACxC,aAAa,CAAEd,cAAc,CAAC,CAAC,CAEnC,KAAM,CAAAuD,wBAAwB,CAAG/E,WAAW,CAAC,KAAO,CAAAgF,SAAiB,EAAK,CACxE,KAAM,CAAAC,OAAO,CAAG3D,QAAQ,CAAC4D,IAAI,CAACL,CAAC,EAAIA,CAAC,CAACvB,EAAE,GAAK0B,SAAS,CAAC,CACtD,GAAI,CAACC,OAAO,EAAI,CAACA,OAAO,CAACV,SAAS,CAAE,OAEpC;AACA,KAAM,CAAAC,cAAuB,CAAG,CAC9B,GAAGS,OAAO,CACVpB,mBAAmB,CAAE,CACnBC,KAAK,CAAE,SAAS,CAChBqB,SAAS,CAAE,GAAI,CAAA5B,IAAI,CAAC,CACtB,CACF,CAAC,CAEDhC,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACvB,EAAE,GAAK0B,SAAS,CAAGR,cAAc,CAAGK,CAAC,CAAC,CAAC,CAC3ElD,+BAA+B,CAAC6C,cAAc,CAAC,CAE/C,GAAI,CACF;AACA,KAAM,CAAAY,MAAM,CAAG,KAAM,CAAAzC,uBAAuB,CAACsC,OAAO,CAACV,SAAS,CAAES,SAAS,CAAE,CACzEK,UAAU,CAAGpC,QAAQ,EAAK,CACxB1B,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,EAC5BA,CAAC,CAACvB,EAAE,GAAK0B,SAAS,CACd,CACE,GAAGH,CAAC,CACJhB,mBAAmB,CAAE,CACnB,GAAGgB,CAAC,CAAChB,mBAAmB,CACxBZ,QAAQ,CACRa,KAAK,CAAE,YACT,CACF,CAAC,CACDe,CACN,CAAC,CAAC,CACJ,CAAC,CACDS,cAAc,CAAG1B,MAAM,EAAK,CAC1BrC,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,EAC5BA,CAAC,CAACvB,EAAE,GAAK0B,SAAS,CACd,CACE,GAAGH,CAAC,CACJhB,mBAAmB,CAAED,MACvB,CAAC,CACDiB,CACN,CAAC,CAAC,CACJ,CAAC,CACDU,eAAe,CAAE,IACnB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,gBAAyB,CAAG,CAChC,GAAGhB,cAAc,CACjB/B,UAAU,CAAE2C,MAAM,CAACK,QAAQ,CAC3BC,YAAY,CAAEN,MAAM,CAACO,QAAQ,CAC7BC,QAAQ,CAAER,MAAM,CAACQ,QAAQ,CACzB/B,mBAAmB,CAAE,CACnBC,KAAK,CAAE,WAAW,CAClBb,QAAQ,CAAE,GAAG,CACbkC,SAAS,CAAEX,cAAc,CAACX,mBAAmB,CAACsB,SAAS,CACvDU,WAAW,CAAE,GAAI,CAAAtC,IAAI,CAAC,CACxB,CACF,CAAC,CAEDhC,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACvB,EAAE,GAAK0B,SAAS,CAAGQ,gBAAgB,CAAGX,CAAC,CAAC,CAAC,CAC7ElD,+BAA+B,CAAC6D,gBAAgB,CAAC,CAEjDvB,OAAO,CAAC6B,GAAG,CAAC,sCAAsC,CAAE,CAClDL,QAAQ,CAAEL,MAAM,CAACK,QAAQ,CAACM,MAAM,CAChCJ,QAAQ,CAAEP,MAAM,CAACO,QAAQ,CAACI,MAAM,CAChCC,KAAK,CAAEZ,MAAM,CAACQ,QAAQ,CAACK,UAAU,CACjCC,UAAU,CAAEd,MAAM,CAACQ,QAAQ,CAACO,iBAC9B,CAAC,CAAC,CAEJ,CAAE,MAAOnC,KAAU,CAAE,CACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAEhD,KAAM,CAAAoC,YAAqB,CAAG,CAC5B,GAAG5B,cAAc,CACjBX,mBAAmB,CAAE,CACnBC,KAAK,CAAE,QAAQ,CACfE,KAAK,CAAEA,KAAK,CAACqC,OAAO,CACpBlB,SAAS,CAAEX,cAAc,CAACX,mBAAmB,CAACsB,SAChD,CACF,CAAC,CAED5D,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACvB,EAAE,GAAK0B,SAAS,CAAGoB,YAAY,CAAGvB,CAAC,CAAC,CAAC,CAC3E,CACF,CAAC,CAAE,CAACvD,QAAQ,CAAEqB,uBAAuB,CAAC,CAAC,CAEvC,KAAM,CAAA2D,yBAAyB,CAAGtG,WAAW,CAAEgF,SAAiB,EAAK,CACnEpC,mBAAmB,CAAC,CAAC,CAErBrB,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACa,GAAG,CAACC,CAAC,EAC5BA,CAAC,CAACvB,EAAE,GAAK0B,SAAS,CACd,CACE,GAAGH,CAAC,CACJhB,mBAAmB,CAAE,CACnB,GAAGgB,CAAC,CAAChB,mBAAmB,CACxBC,KAAK,CAAE,WACT,CACF,CAAC,CACDe,CACN,CAAC,CAAC,CACJ,CAAC,CAAE,CAACjC,mBAAmB,CAAC,CAAC,CAEzB,KAAM,CAAA2D,iBAAiB,CAAGvG,WAAW,CAAC,CAACgF,SAAiB,CAAEwB,SAAiB,CAAEC,OAAe,GAAK,CAC/F5D,WAAW,CAAC2D,SAAS,CAAEC,OAAO,CAAC,CAE/B;AACAlF,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACa,GAAG,CAACK,OAAO,OAAAyB,mBAAA,OAClC,CAAAzB,OAAO,CAAC3B,EAAE,GAAK0B,SAAS,CACpB,CACE,GAAGC,OAAO,CACVxC,UAAU,EAAAiE,mBAAA,CAAEzB,OAAO,CAACxC,UAAU,UAAAiE,mBAAA,iBAAlBA,mBAAA,CAAoB9B,GAAG,CAAC+B,OAAO,EACzCA,OAAO,CAACrD,EAAE,GAAKkD,SAAS,CACpB,CACE,GAAGG,OAAO,CACVC,IAAI,CAAEH,OAAO,CACbI,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,GAAI,CAAAvD,IAAI,CAAC,CAAC,CACpBwD,QAAQ,CAAE,MACZ,CAAC,CACDJ,OACN,CACF,CAAC,CACD1B,OAAO,EACb,CAAC,CAAC,CACJ,CAAC,CAAE,CAACpC,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAAmE,mBAAmB,CAAGhH,WAAW,CAAEiF,OAAgB,EAAK,CAC5DxD,iBAAiB,CAACwD,OAAO,CAAC,CAC1B,GAAIA,OAAO,CAACxC,UAAU,EAAIwC,OAAO,CAACxC,UAAU,CAACsD,MAAM,CAAG,CAAC,CAAE,CACvDpE,+BAA+B,CAACsD,OAAO,CAAC,CACxCpD,aAAa,CAAC,YAAY,CAAC,CAC7B,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAoF,mBAAmB,CAAGjH,WAAW,CAAEgF,SAAiB,EAAK,CAC7DzD,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACmD,MAAM,CAACrC,CAAC,EAAIA,CAAC,CAACvB,EAAE,GAAK0B,SAAS,CAAC,CAAC,CACzD,GAAI,CAAAxD,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE8B,EAAE,IAAK0B,SAAS,CAAE,CACpCvD,iBAAiB,CAAC,IAAI,CAAC,CACzB,CACA,GAAI,CAAAC,4BAA4B,SAA5BA,4BAA4B,iBAA5BA,4BAA4B,CAAE4B,EAAE,IAAK0B,SAAS,CAAE,CAClDrD,+BAA+B,CAAC,IAAI,CAAC,CACvC,CACF,CAAC,CAAE,CAACH,cAAc,CAAEE,4BAA4B,CAAC,CAAC,CAElD,KAAM,CAAAyF,mBAAmB,CAAGnH,WAAW,CAAEiF,OAAgB,EAAK,CAC5D,KAAM,CAAAmC,UAAU,CAAG,CACjBhE,KAAK,CAAE6B,OAAO,CAAC7B,KAAK,CACpBM,IAAI,CAAEuB,OAAO,CAACvB,IAAI,CAAC2D,WAAW,CAAC,CAAC,CAChC1D,QAAQ,CAAEsB,OAAO,CAACtB,QAAQ,CAC1BlB,UAAU,CAAEwC,OAAO,CAACxC,UAAU,EAAIA,UAAU,CAC5CiD,YAAY,CAAET,OAAO,CAACS,YAAY,EAAI,EAAE,CACxCE,QAAQ,CAAEX,OAAO,CAACW,QAAQ,EAAI,CAAC,CAAC,CAChC/B,mBAAmB,CAAEoB,OAAO,CAACpB,mBAC/B,CAAC,CAED,KAAM,CAAAyD,OAAO,CAAGC,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAE,IAAI,CAAE,CAAC,CAAC,CACnD,KAAM,CAAAK,OAAO,CAAG,sCAAsC,CAAEC,kBAAkB,CAACJ,OAAO,CAAC,CAEnF,KAAM,CAAAK,qBAAqB,CAAG,WAAW1C,OAAO,CAAC7B,KAAK,CAACwE,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI5C,OAAO,CAACvB,IAAI,CAAC2D,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAE5I,KAAM,CAAAC,WAAW,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CAC/CF,WAAW,CAACG,YAAY,CAAC,MAAM,CAAET,OAAO,CAAC,CACzCM,WAAW,CAACG,YAAY,CAAC,UAAU,CAAEP,qBAAqB,CAAC,CAC3DI,WAAW,CAACI,KAAK,CAAC,CAAC,CACrB,CAAC,CAAE,CAAC1F,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAA2F,YAAY,CAAGpI,WAAW,CAAC,IAAM,CACrC,KAAM,CAAAqI,YAAY,CAAG1H,kBAAkB,CAAC,CAAC,CAACiE,GAAG,CAACK,OAAO,GAAK,CACxD,GAAGA,OAAO,CACVpB,mBAAmB,CAAE,CACnBC,KAAK,CAAE,WAAoB,CAC3Bb,QAAQ,CAAE,GAAG,CACb4C,WAAW,CAAEZ,OAAO,CAACvB,IACvB,CACF,CAAC,CAAC,CAAC,CACHnC,WAAW,CAAC8G,YAAY,CAAC,CAC3B,CAAC,CAAE,EAAE,CAAC,CAEN;AACApI,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqI,kBAAkB,CAAIC,KAAiB,EAAK,CAChD,KAAM,CAAAC,MAAM,CAAGD,KAAK,CAACC,MAAiB,CACtC,GAAI1G,gBAAgB,EAAI,CAAC0G,MAAM,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAE,CACjE1G,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAA2G,YAAY,CAAIH,KAAoB,EAAK,CAC7C,GAAIA,KAAK,CAACI,GAAG,GAAK,QAAQ,EAAI7G,gBAAgB,CAAE,CAC9CC,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CACF,CAAC,CAED,GAAID,gBAAgB,CAAE,CACpBkG,QAAQ,CAACY,gBAAgB,CAAC,WAAW,CAAEN,kBAAkB,CAAC,CAC1DN,QAAQ,CAACY,gBAAgB,CAAC,SAAS,CAAEF,YAAY,CAAC,CAClD;AACAV,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC,CACjD,CAAC,IAAM,CACLf,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC,CACpD,CAEA,MAAO,IAAM,CACXhB,QAAQ,CAACiB,mBAAmB,CAAC,WAAW,CAAEX,kBAAkB,CAAC,CAC7DN,QAAQ,CAACiB,mBAAmB,CAAC,SAAS,CAAEP,YAAY,CAAC,CACrDV,QAAQ,CAACa,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC,CACpD,CAAC,CACH,CAAC,CAAE,CAAClH,gBAAgB,CAAC,CAAC,CAEtB,mBACEV,KAAA,CAAClB,aAAa,EAAAgJ,QAAA,EAEXlH,cAAc,eACbd,IAAA,QAAKiI,SAAS,CAAC,sFAAsF,CAAAD,QAAA,cACnGhI,IAAA,QAAKiI,SAAS,CAAC,qHAAqH,CAAAD,QAAA,cAClI9H,KAAA,QAAK+H,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxB9H,KAAA,QAAK+H,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpChI,IAAA,QAAKiI,SAAS,CAAC,4IAA4I,CAAAD,QAAA,cACzJhI,IAAA,CAACL,IAAI,EAACsI,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACvC,CAAC,cACNjI,IAAA,OAAIiI,SAAS,CAAC,kCAAkC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrEhI,IAAA,MAAGiI,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,wCAA4B,CAAG,CAAC,EAClE,CAAC,cAEN9H,KAAA,QAAK+H,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBhI,IAAA,UACEkI,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEnH,eAAgB,CACvBoH,QAAQ,CAAGC,CAAC,EAAKpH,kBAAkB,CAACoH,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE,CACpDG,SAAS,CAAGD,CAAC,EAAKA,CAAC,CAACZ,GAAG,GAAK,OAAO,EAAIvE,qBAAqB,CAAC,CAAE,CAC/D+E,SAAS,CAAC,2MAA2M,CACrNM,WAAW,CAAC,yBAAyB,CACrCC,SAAS,MACV,CAAC,cAGFtI,KAAA,QAAK+H,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBhI,IAAA,CAACV,MAAM,EACLmJ,OAAO,CAAEA,CAAA,GAAM,CACb1H,iBAAiB,CAAC,KAAK,CAAC,CACxBE,kBAAkB,CAAC,EAAE,CAAC,CACxB,CAAE,CACFyH,OAAO,CAAC,WAAW,CACnBC,SAAS,MAAAX,QAAA,CACV,eAED,CAAQ,CAAC,cACThI,IAAA,CAACV,MAAM,EACLmJ,OAAO,CAAEvF,qBAAsB,CAC/B0F,QAAQ,CAAE,CAAC5H,eAAe,CAACmC,IAAI,CAAC,CAAE,CAClCuF,OAAO,CAAC,SAAS,CACjBC,SAAS,MAAAX,QAAA,CACV,cAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CACN,cAED9H,KAAA,QAAK+H,SAAS,CAAC,4CAA4C,CAAAD,QAAA,eAEzDhI,IAAA,QAAKiI,SAAS,CAAC,cAAc,CAAM,CAAC,cAGpC/H,KAAA,QAAK+H,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/ChI,IAAA,QAAKiI,SAAS,CAAC,gFAAgF,CAAM,CAAC,cACtGjI,IAAA,QAAKiI,SAAS,CAAC,kFAAkF,CAAM,CAAC,cACxGjI,IAAA,QAAKiI,SAAS,CAAC,2HAA2H,CAAM,CAAC,EAC9I,CAAC,cAGNjI,IAAA,CAACf,OAAO,EAACyB,UAAU,CAAEA,UAAW,CAACmI,YAAY,CAAElI,aAAc,CAAE,CAAC,cAGhET,KAAA,QAAK+H,SAAS,CAAC,WAAW,CAAAD,QAAA,EAEvBtH,UAAU,GAAK,OAAO,eACrBV,IAAA,CAACX,SAAS,GAAE,CACb,CAGAqB,UAAU,GAAK,WAAW,eACzBV,IAAA,CAACd,aAAa,EACFgC,cAAc,CAAEA,cAAe,CAC/BZ,cAAc,CAAEA,cAAe,CAC/BwI,gBAAgB,CAAE7G,oBAAqB,CACvC8G,eAAe,CAAE3F,mBAAoB,CACrC4F,gBAAgB,CAAE3H,cAAe,CACjC4H,iBAAiB,CAAE3H,eAAgB,CACpC,CACR,CAGAZ,UAAU,GAAK,eAAe,eACjCV,IAAA,CAACb,iBAAiB,EACRiB,QAAQ,CAAEA,QAAS,CACnB8I,oBAAoB,CAAErF,wBAAyB,CAC/CsF,qBAAqB,CAAE/D,yBAA0B,CACjD5D,cAAc,CAAEA,cAAe,CAC/BM,sBAAsB,CAAEA,sBAAuB,CAC/CsH,eAAe,CAAErD,mBAAoB,CACrCsD,aAAa,CAAEA,CAAA,GAAM1I,aAAa,CAAC,YAAY,CAAE,CAClD,CACN,CAGAD,UAAU,GAAK,YAAY,eAC9BV,IAAA,CAACZ,WAAW,EACFgB,QAAQ,CAAEA,QAAS,CACnBgJ,eAAe,CAAErD,mBAAoB,CACrCuD,mBAAmB,CAAEA,CAAA,GAAM3I,aAAa,CAAC,eAAe,CAAE,CAC3D,CACV,CAGAD,UAAU,GAAK,OAAO,eACrBV,IAAA,QAAKiI,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxB9H,KAAA,QAAK+H,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF9H,KAAA,QAAK+H,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3ChI,IAAA,QAAKiI,SAAS,CAAC,+JAA+J,CAAAD,QAAA,cAC5KhI,IAAA,CAACF,MAAM,EAACmI,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACtC,CAAC,cACN/H,KAAA,QAAA8H,QAAA,eACEhI,IAAA,OAAIiI,SAAS,CAAC,kCAAkC,CAAAD,QAAA,CAAC,SAAO,CAAI,CAAC,cAC7DhI,IAAA,MAAGiI,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,qDAAoC,CAAG,CAAC,EAC1E,CAAC,EACH,CAAC,cACN9H,KAAA,QAAK+H,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChChI,IAAA,CAACF,MAAM,EAACmI,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC3DjI,IAAA,MAAGiI,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,mDAAkC,CAAG,CAAC,EAChE,CAAC,EACH,CAAC,CACH,CACN,CAGAtH,UAAU,GAAK,UAAU,eACxBV,IAAA,QAAKiI,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxB9H,KAAA,QAAK+H,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF9H,KAAA,QAAK+H,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3ChI,IAAA,QAAKiI,SAAS,CAAC,2JAA2J,CAAAD,QAAA,cACxKhI,IAAA,CAACH,QAAQ,EAACoI,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACxC,CAAC,cACN/H,KAAA,QAAA8H,QAAA,eACEhI,IAAA,OAAIiI,SAAS,CAAC,kCAAkC,CAAAD,QAAA,CAAC,YAAU,CAAI,CAAC,cAChEhI,IAAA,MAAGiI,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,8BAAuB,CAAG,CAAC,EAC7D,CAAC,EACH,CAAC,cACN9H,KAAA,QAAK+H,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChChI,IAAA,CAACH,QAAQ,EAACoI,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC7DjI,IAAA,MAAGiI,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,sDAAqC,CAAG,CAAC,EACnE,CAAC,EACH,CAAC,CACH,CACN,cAGD9H,KAAA,QAAK+H,SAAS,CAAC,iDAAiD,CAAAD,QAAA,EAE7D5H,QAAQ,CAACyE,MAAM,GAAK,CAAC,eACpB7E,IAAA,CAACV,MAAM,EACLmJ,OAAO,CAAEvB,YAAa,CACtBqC,IAAI,cAAEvJ,IAAA,CAACJ,QAAQ,EAACqI,SAAS,CAAC,SAAS,CAAE,CAAE,CACvCS,OAAO,CAAC,WAAW,CACnBC,SAAS,MAAAX,QAAA,CACV,MAED,CAAQ,CACT,cACDhI,IAAA,CAACV,MAAM,EACLmJ,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAACvH,cAAc,CAACsI,WAAW,CAAE,CAC/BxG,6BAA6B,CAAC,CAAC,CACjC,CACF,CAAE,CACF4F,QAAQ,CAAE1H,cAAc,CAACsI,WAAY,CACrCD,IAAI,cAAEvJ,IAAA,CAACN,IAAI,EAACuI,SAAS,CAAC,SAAS,CAAE,CAAE,CACnCS,OAAO,CAAC,SAAS,CACjBC,SAAS,MAAAX,QAAA,CACV,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,EACO,CAAC,CAEpB,CAEA,cAAe,CAAA7H,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}