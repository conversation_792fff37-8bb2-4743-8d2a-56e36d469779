{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Mic2, Headphones, Settings, Play, Square, Plus, List, BarChart3 } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RecordingPage = ({\n  recordingState,\n  currentMeeting,\n  onStartRecording,\n  onStopRecording,\n  onPauseRecording,\n  onResumeRecording\n}) => {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: 'Pasiruo<PERSON><PERSON> prezentacijai',\n    completed: false\n  }, {\n    id: 2,\n    text: 'Susiti<PERSON>as su klientu',\n    completed: true\n  }, {\n    id: 3,\n    text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ti dokumentus',\n    completed: false\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false\n  }]);\n  const [recordedConversations, setRecordedConversations] = useState([{\n    id: 1,\n    title: 'Susitikimas su komanda',\n    duration: '15:30',\n    date: '2024-01-15'\n  }, {\n    id: 2,\n    title: 'Klientų aptarimas',\n    duration: '22:15',\n    date: '2024-01-14'\n  }, {\n    id: 3,\n    title: 'Projekto planavimas',\n    duration: '18:45',\n    date: '2024-01-13'\n  }]);\n  const handleStartRecording = async () => {\n    setIsRecording(true);\n    setRecordingTime(0);\n    // Simulate recording time\n    const interval = setInterval(() => {\n      setRecordingTime(prev => prev + 1);\n    }, 1000);\n\n    // Store interval for cleanup\n    window.recordingInterval = interval;\n  };\n  const handleStopRecording = () => {\n    setIsRecording(false);\n    if (window.recordingInterval) {\n      clearInterval(window.recordingInterval);\n    }\n    // Add new recording to list\n    const newRecording = {\n      id: Date.now(),\n      title: `Pokalbis ${new Date().toLocaleString('lt-LT')}`,\n      duration: `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`,\n      date: new Date().toISOString().split('T')[0]\n    };\n    setRecordedConversations(prev => [newRecording, ...prev]);\n    setRecordingTime(0);\n  };\n  const toggleTodo = id => {\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const addTodo = () => {\n    const newTodo = {\n      id: Date.now(),\n      text: 'Nauja užduotis',\n      completed: false\n    };\n    setTodos(prev => [...prev, newTodo]);\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\",\n          children: /*#__PURE__*/_jsxDEV(Mic2, {\n            className: \"h-6 w-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-white\",\n            children: \"Pokalbi\\u0173 \\u012Fra\\u0161ymas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-white/60\",\n            children: \"Profesionalus garso \\u012Fra\\u0161ymas su automatine transkribavimo technologija\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Mic2, {\n            className: \"h-5 w-5 text-blue-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"Pokalbi\\u0173 \\u012Fra\\u0161ymas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-medium\",\n                children: \"\\u012Era\\u0161ymo statusas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-white mb-2\",\n              children: formatTime(recordingTime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/60 text-sm\",\n              children: isRecording ? 'Įrašoma...' : 'Pasiruošta įrašymui'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: !isRecording ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStartRecording,\n              className: \"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this), \"Prad\\u0117ti \\u012Fra\\u0161ym\\u0105\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStopRecording,\n              className: \"flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-red-600 hover:to-pink-600 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(Square, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), currentMeeting && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-medium mb-2\",\n              children: \"Dabartinis pokalbis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/80 text-sm\",\n              children: currentMeeting.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/60 text-xs mt-1\",\n              children: [\"Prad\\u0117tas: \", currentMeeting.date.toLocaleTimeString('lt-LT')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(List, {\n              className: \"h-5 w-5 text-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-white\",\n              children: \"Susitikimo u\\u017Eduotys\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: addTodo,\n            className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\",\n            children: /*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: todos.map(todo => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\",\n            onClick: () => toggleTodo(todo.id),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-5 h-5 rounded border-2 flex items-center justify-center ${todo.completed ? 'bg-green-500 border-green-500' : 'border-white/30'}`,\n              children: todo.completed && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 38\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-white flex-1 ${todo.completed ? 'line-through text-white/50' : ''}`,\n              children: todo.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)]\n          }, todo.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: [\"U\\u017Ebaigta: \", todos.filter(t => t.completed).length, \" i\\u0161 \", todos.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Headphones, {\n            className: \"h-5 w-5 text-purple-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"\\u012Era\\u0161yti pokalbiai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recordedConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white font-medium text-sm truncate\",\n                children: conversation.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-xs\",\n                children: conversation.duration\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-xs\",\n                children: conversation.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(Settings, {\n                    className: \"h-3 w-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)]\n          }, conversation.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-white/10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: [\"I\\u0161 viso: \", recordedConversations.length, \" pokalbi\\u0173\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"h-5 w-5 text-orange-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white\",\n            children: \"Analitika\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 rounded-xl p-4 border border-white/10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-white\",\n                children: recordedConversations.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"\\u012Era\\u0161yti pokalbiai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 rounded-xl p-4 border border-white/10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-white\",\n                children: recordedConversations.reduce((total, conv) => {\n                  const [mins, secs] = conv.duration.split(':').map(Number);\n                  return total + mins * 60 + secs;\n                }, 0) / 60 | 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white/60 text-sm\",\n                children: \"Minut\\u0117s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"U\\u017Eduo\\u010Di\\u0173 progresas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: [Math.round(todos.filter(t => t.completed).length / todos.length * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${todos.filter(t => t.completed).length / todos.length * 100}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"\\u012Era\\u0161ymo kokyb\\u0117\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: \"95%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-500 h-2 rounded-full\",\n                  style: {\n                    width: '95%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/80 text-sm\",\n                  children: \"Transkribavimo tikslumas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white/60 text-sm\",\n                  children: \"98%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-500 h-2 rounded-full\",\n                  style: {\n                    width: '98%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(RecordingPage, \"puEM7FFtb6OHlLIHFF5vJRpgYRA=\");\n_c = RecordingPage;\nexport default RecordingPage;\nvar _c;\n$RefreshReg$(_c, \"RecordingPage\");", "map": {"version": 3, "names": ["React", "useState", "Mic2", "Headphones", "Settings", "Play", "Square", "Plus", "List", "BarChart3", "jsxDEV", "_jsxDEV", "RecordingPage", "recordingState", "currentMeeting", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "_s", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "recordedConversations", "setRecordedConversations", "title", "duration", "date", "handleStartRecording", "interval", "setInterval", "prev", "window", "recordingInterval", "handleStopRecording", "clearInterval", "newRecording", "Date", "now", "toLocaleString", "Math", "floor", "toString", "padStart", "toISOString", "split", "toggleTodo", "map", "todo", "addTodo", "newTodo", "formatTime", "seconds", "mins", "secs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "toLocaleTimeString", "filter", "t", "length", "conversation", "reduce", "total", "conv", "Number", "round", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Mic2, Zap, Headphones, Settings, Play, Pause, Square, Plus, List, BarChart3 } from 'lucide-react';\r\nimport { RecordingPanel } from './index';\r\nimport { RecordingState, Meeting } from '../types/meeting';\r\n\r\ninterface RecordingPageProps {\r\n  recordingState: RecordingState;\r\n  currentMeeting: Meeting | null;\r\n  onStartRecording: (title: string) => Promise<void>;\r\n  onStopRecording: () => Promise<void>;\r\n  onPauseRecording: () => void;\r\n  onResumeRecording: () => void;\r\n}\r\n\r\nconst RecordingPage: React.FC<RecordingPageProps> = ({\r\n  recordingState,\r\n  currentMeeting,\r\n  onStartRecording,\r\n  onStopRecording,\r\n  onPauseRecording,\r\n  onResumeRecording\r\n}) => {\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [todos, setTodos] = useState([\r\n    { id: 1, text: '<PERSON><PERSON><PERSON><PERSON><PERSON> prezenta<PERSON>', completed: false },\r\n    { id: 2, text: 'Susitikimas su klientu', completed: true },\r\n    { id: 3, text: '<PERSON><PERSON><PERSON><PERSON>r<PERSON>ti dokumentus', completed: false },\r\n    { id: 4, text: 'Planuoti kitą savaitę', completed: false }\r\n  ]);\r\n  const [recordedConversations, setRecordedConversations] = useState([\r\n    { id: 1, title: 'Susitikimas su komanda', duration: '15:30', date: '2024-01-15' },\r\n    { id: 2, title: 'Klientų aptarimas', duration: '22:15', date: '2024-01-14' },\r\n    { id: 3, title: 'Projekto planavimas', duration: '18:45', date: '2024-01-13' }\r\n  ]);\r\n\r\n  const handleStartRecording = async () => {\r\n    setIsRecording(true);\r\n    setRecordingTime(0);\r\n    // Simulate recording time\r\n    const interval = setInterval(() => {\r\n      setRecordingTime(prev => prev + 1);\r\n    }, 1000);\r\n    \r\n    // Store interval for cleanup\r\n    (window as any).recordingInterval = interval;\r\n  };\r\n\r\n  const handleStopRecording = () => {\r\n    setIsRecording(false);\r\n    if ((window as any).recordingInterval) {\r\n      clearInterval((window as any).recordingInterval);\r\n    }\r\n    // Add new recording to list\r\n    const newRecording = {\r\n      id: Date.now(),\r\n      title: `Pokalbis ${new Date().toLocaleString('lt-LT')}`,\r\n      duration: `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`,\r\n      date: new Date().toISOString().split('T')[0]\r\n    };\r\n    setRecordedConversations(prev => [newRecording, ...prev]);\r\n    setRecordingTime(0);\r\n  };\r\n\r\n  const toggleTodo = (id: number) => {\r\n    setTodos(prev => prev.map(todo => \r\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\r\n    ));\r\n  };\r\n\r\n  const addTodo = () => {\r\n    const newTodo = {\r\n      id: Date.now(),\r\n      text: 'Nauja užduotis',\r\n      completed: false\r\n    };\r\n    setTodos(prev => [...prev, newTodo]);\r\n  };\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/30\">\r\n            <Mic2 className=\"h-6 w-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-white\">Pokalbių įrašymas</h2>\r\n            <p className=\"text-sm text-white/60\">Profesionalus garso įrašymas su automatine transkribavimo technologija</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 2x2 Grid Layout */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        \r\n        {/* Top-Left: Pokalbių įrašymo komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <Mic2 className=\"h-5 w-5 text-blue-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Pokalbių įrašymas</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-4\">\r\n            {/* Recording Status */}\r\n            <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <span className=\"text-white font-medium\">Įrašymo statusas</span>\r\n                <div className={`w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>\r\n              </div>\r\n              <div className=\"text-2xl font-bold text-white mb-2\">\r\n                {formatTime(recordingTime)}\r\n              </div>\r\n              <div className=\"text-white/60 text-sm\">\r\n                {isRecording ? 'Įrašoma...' : 'Pasiruošta įrašymui'}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Recording Controls */}\r\n            <div className=\"flex gap-3\">\r\n              {!isRecording ? (\r\n                <button\r\n                  onClick={handleStartRecording}\r\n                  className=\"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200\"\r\n                >\r\n                  <Play className=\"h-4 w-4\" />\r\n                  Pradėti įrašymą\r\n                </button>\r\n              ) : (\r\n                <button\r\n                  onClick={handleStopRecording}\r\n                  className=\"flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-3 rounded-xl font-medium flex items-center justify-center gap-2 hover:from-red-600 hover:to-pink-600 transition-all duration-200\"\r\n                >\r\n                  <Square className=\"h-4 w-4\" />\r\n                  Sustabdyti įrašymą\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Current Meeting Info */}\r\n            {currentMeeting && (\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n                <div className=\"text-white font-medium mb-2\">Dabartinis pokalbis</div>\r\n                <div className=\"text-white/80 text-sm\">{currentMeeting.title}</div>\r\n                <div className=\"text-white/60 text-xs mt-1\">\r\n                  Pradėtas: {currentMeeting.date.toLocaleTimeString('lt-LT')}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Top-Right: Susitikimo užduotys - TODOS list komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <List className=\"h-5 w-5 text-green-400\" />\r\n              <h3 className=\"text-lg font-semibold text-white\">Susitikimo užduotys</h3>\r\n            </div>\r\n            <button\r\n              onClick={addTodo}\r\n              className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200\"\r\n            >\r\n              <Plus className=\"h-4 w-4 text-white\" />\r\n            </button>\r\n          </div>\r\n          \r\n          <div className=\"space-y-3\">\r\n            {todos.map((todo) => (\r\n              <div\r\n                key={todo.id}\r\n                className=\"flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\"\r\n                onClick={() => toggleTodo(todo.id)}\r\n              >\r\n                <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${\r\n                  todo.completed \r\n                    ? 'bg-green-500 border-green-500' \r\n                    : 'border-white/30'\r\n                }`}>\r\n                  {todo.completed && <div className=\"w-2 h-2 bg-white rounded-full\"></div>}\r\n                </div>\r\n                <span className={`text-white flex-1 ${\r\n                  todo.completed ? 'line-through text-white/50' : ''\r\n                }`}>\r\n                  {todo.text}\r\n                </span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          \r\n          <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n            <div className=\"text-white/60 text-sm\">\r\n              Užbaigta: {todos.filter(t => t.completed).length} iš {todos.length}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom-Left: Įrašyti pokalbiai komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <Headphones className=\"h-5 w-5 text-purple-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Įrašyti pokalbiai</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-3\">\r\n            {recordedConversations.map((conversation) => (\r\n              <div\r\n                key={conversation.id}\r\n                className=\"p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer\"\r\n              >\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <div className=\"text-white font-medium text-sm truncate\">\r\n                    {conversation.title}\r\n                  </div>\r\n                  <div className=\"text-white/60 text-xs\">\r\n                    {conversation.duration}\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"text-white/60 text-xs\">\r\n                    {conversation.date}\r\n                  </div>\r\n                  <div className=\"flex gap-2\">\r\n                    <button className=\"p-1 text-white/60 hover:text-white transition-colors\">\r\n                      <Play className=\"h-3 w-3\" />\r\n                    </button>\r\n                    <button className=\"p-1 text-white/60 hover:text-white transition-colors\">\r\n                      <Settings className=\"h-3 w-3\" />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          \r\n          <div className=\"mt-4 pt-4 border-t border-white/10\">\r\n            <div className=\"text-white/60 text-sm\">\r\n              Iš viso: {recordedConversations.length} pokalbių\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom-Right: Analitikos komponentas */}\r\n        <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6\">\r\n          <div className=\"flex items-center gap-3 mb-4\">\r\n            <BarChart3 className=\"h-5 w-5 text-orange-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Analitika</h3>\r\n          </div>\r\n          \r\n          <div className=\"space-y-4\">\r\n            {/* Recording Stats */}\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10 text-center\">\r\n                <div className=\"text-2xl font-bold text-white\">{recordedConversations.length}</div>\r\n                <div className=\"text-white/60 text-sm\">Įrašyti pokalbiai</div>\r\n              </div>\r\n              <div className=\"bg-white/5 rounded-xl p-4 border border-white/10 text-center\">\r\n                <div className=\"text-2xl font-bold text-white\">\r\n                  {recordedConversations.reduce((total, conv) => {\r\n                    const [mins, secs] = conv.duration.split(':').map(Number);\r\n                    return total + mins * 60 + secs;\r\n                  }, 0) / 60 | 0}\r\n                </div>\r\n                <div className=\"text-white/60 text-sm\">Minutės</div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Progress Bars */}\r\n            <div className=\"space-y-3\">\r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Užduočių progresas</span>\r\n                  <span className=\"text-white/60 text-sm\">\r\n                    {Math.round((todos.filter(t => t.completed).length / todos.length) * 100)}%\r\n                  </span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div \r\n                    className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\r\n                    style={{ width: `${(todos.filter(t => t.completed).length / todos.length) * 100}%` }}\r\n                  ></div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Įrašymo kokybė</span>\r\n                  <span className=\"text-white/60 text-sm\">95%</span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{ width: '95%' }}></div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-white/80 text-sm\">Transkribavimo tikslumas</span>\r\n                  <span className=\"text-white/60 text-sm\">98%</span>\r\n                </div>\r\n                <div className=\"w-full bg-white/20 rounded-full h-2\">\r\n                  <div className=\"bg-purple-500 h-2 rounded-full\" style={{ width: '98%' }}></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RecordingPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAOC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa3G,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,cAAc;EACdC,cAAc;EACdC,gBAAgB;EAChBC,eAAe;EACfC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,CACjC;IAAEyB,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE;EAAM,CAAC,EAC7D;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE;EAAK,CAAC,EAC1D;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE;EAAM,CAAC,EAC1D;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE;EAAM,CAAC,CAC3D,CAAC;EACF,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7B,QAAQ,CAAC,CACjE;IAAEyB,EAAE,EAAE,CAAC;IAAEK,KAAK,EAAE,wBAAwB;IAAEC,QAAQ,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAa,CAAC,EACjF;IAAEP,EAAE,EAAE,CAAC;IAAEK,KAAK,EAAE,mBAAmB;IAAEC,QAAQ,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAa,CAAC,EAC5E;IAAEP,EAAE,EAAE,CAAC;IAAEK,KAAK,EAAE,qBAAqB;IAAEC,QAAQ,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAa,CAAC,CAC/E,CAAC;EAEF,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvCb,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,CAAC,CAAC;IACnB;IACA,MAAMY,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCb,gBAAgB,CAACc,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC;;IAER;IACCC,MAAM,CAASC,iBAAiB,GAAGJ,QAAQ;EAC9C,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChCnB,cAAc,CAAC,KAAK,CAAC;IACrB,IAAKiB,MAAM,CAASC,iBAAiB,EAAE;MACrCE,aAAa,CAAEH,MAAM,CAASC,iBAAiB,CAAC;IAClD;IACA;IACA,MAAMG,YAAY,GAAG;MACnBhB,EAAE,EAAEiB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdb,KAAK,EAAE,YAAY,IAAIY,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC,EAAE;MACvDb,QAAQ,EAAE,GAAGc,IAAI,CAACC,KAAK,CAACzB,aAAa,GAAG,EAAE,CAAC,IAAI,CAACA,aAAa,GAAG,EAAE,EAAE0B,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACjGhB,IAAI,EAAE,IAAIU,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IACDrB,wBAAwB,CAACO,IAAI,IAAI,CAACK,YAAY,EAAE,GAAGL,IAAI,CAAC,CAAC;IACzDd,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM6B,UAAU,GAAI1B,EAAU,IAAK;IACjCD,QAAQ,CAACY,IAAI,IAAIA,IAAI,CAACgB,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAAC5B,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAG4B,IAAI;MAAE1B,SAAS,EAAE,CAAC0B,IAAI,CAAC1B;IAAU,CAAC,GAAG0B,IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,MAAMC,OAAO,GAAG;MACd9B,EAAE,EAAEiB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdjB,IAAI,EAAE,gBAAgB;MACtBC,SAAS,EAAE;IACb,CAAC;IACDH,QAAQ,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEmB,OAAO,CAAC,CAAC;EACtC,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAGb,IAAI,CAACC,KAAK,CAACW,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACZ,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,oBACEtC,OAAA;IAAKkD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnD,OAAA;MAAKkD,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFnD,OAAA;QAAKkD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCnD,OAAA;UAAKkD,SAAS,EAAC,6JAA6J;UAAAC,QAAA,eAC1KnD,OAAA,CAACT,IAAI;YAAC2D,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNvD,OAAA;UAAAmD,QAAA,gBACEnD,OAAA;YAAIkD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEvD,OAAA;YAAGkD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA;MAAKkD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAGpDnD,OAAA;QAAKkD,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClFnD,OAAA;UAAKkD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CnD,OAAA,CAACT,IAAI;YAAC2D,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CvD,OAAA;YAAIkD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENvD,OAAA;UAAKkD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBnD,OAAA;YAAKkD,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/DnD,OAAA;cAAKkD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDnD,OAAA;gBAAMkD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChEvD,OAAA;gBAAKkD,SAAS,EAAE,wBAAwBzC,WAAW,GAAG,0BAA0B,GAAG,aAAa;cAAG;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDL,UAAU,CAACnC,aAAa;YAAC;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnC1C,WAAW,GAAG,YAAY,GAAG;YAAqB;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvD,OAAA;YAAKkD,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxB,CAAC1C,WAAW,gBACXT,OAAA;cACEwD,OAAO,EAAEjC,oBAAqB;cAC9B2B,SAAS,EAAC,4MAA4M;cAAAC,QAAA,gBAEtNnD,OAAA,CAACN,IAAI;gBAACwD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uCAE9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETvD,OAAA;cACEwD,OAAO,EAAE3B,mBAAoB;cAC7BqB,SAAS,EAAC,sMAAsM;cAAAC,QAAA,gBAEhNnD,OAAA,CAACL,MAAM;gBAACuD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qCAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLpD,cAAc,iBACbH,OAAA;YAAKkD,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/DnD,OAAA;cAAKkD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtEvD,OAAA;cAAKkD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEhD,cAAc,CAACiB;YAAK;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnEvD,OAAA;cAAKkD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,iBAChC,EAAChD,cAAc,CAACmB,IAAI,CAACmC,kBAAkB,CAAC,OAAO,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvD,OAAA;QAAKkD,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClFnD,OAAA;UAAKkD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDnD,OAAA;YAAKkD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnD,OAAA,CAACH,IAAI;cAACqD,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CvD,OAAA;cAAIkD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACNvD,OAAA;YACEwD,OAAO,EAAEZ,OAAQ;YACjBM,SAAS,EAAC,0EAA0E;YAAAC,QAAA,eAEpFnD,OAAA,CAACJ,IAAI;cAACsD,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENvD,OAAA;UAAKkD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBtC,KAAK,CAAC6B,GAAG,CAAEC,IAAI,iBACd3C,OAAA;YAEEkD,SAAS,EAAC,uIAAuI;YACjJM,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAACE,IAAI,CAAC5B,EAAE,CAAE;YAAAoC,QAAA,gBAEnCnD,OAAA;cAAKkD,SAAS,EAAE,6DACdP,IAAI,CAAC1B,SAAS,GACV,+BAA+B,GAC/B,iBAAiB,EACpB;cAAAkC,QAAA,EACAR,IAAI,CAAC1B,SAAS,iBAAIjB,OAAA;gBAAKkD,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACNvD,OAAA;cAAMkD,SAAS,EAAE,qBACfP,IAAI,CAAC1B,SAAS,GAAG,4BAA4B,GAAG,EAAE,EACjD;cAAAkC,QAAA,EACAR,IAAI,CAAC3B;YAAI;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAfFZ,IAAI,CAAC5B,EAAE;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBT,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvD,OAAA;UAAKkD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDnD,OAAA;YAAKkD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,iBAC3B,EAACtC,KAAK,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1C,SAAS,CAAC,CAAC2C,MAAM,EAAC,WAAI,EAAC/C,KAAK,CAAC+C,MAAM;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvD,OAAA;QAAKkD,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClFnD,OAAA;UAAKkD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CnD,OAAA,CAACR,UAAU;YAAC0D,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDvD,OAAA;YAAIkD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENvD,OAAA;UAAKkD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBjC,qBAAqB,CAACwB,GAAG,CAAEmB,YAAY,iBACtC7D,OAAA;YAEEkD,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBAEzHnD,OAAA;cAAKkD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDnD,OAAA;gBAAKkD,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACrDU,YAAY,CAACzC;cAAK;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNvD,OAAA;gBAAKkD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCU,YAAY,CAACxC;cAAQ;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnD,OAAA;gBAAKkD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCU,YAAY,CAACvC;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNvD,OAAA;gBAAKkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnD,OAAA;kBAAQkD,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACtEnD,OAAA,CAACN,IAAI;oBAACwD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACTvD,OAAA;kBAAQkD,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACtEnD,OAAA,CAACP,QAAQ;oBAACyD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAvBDM,YAAY,CAAC9C,EAAE;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBjB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvD,OAAA;UAAKkD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDnD,OAAA;YAAKkD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,gBAC5B,EAACjC,qBAAqB,CAAC0C,MAAM,EAAC,gBACzC;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvD,OAAA;QAAKkD,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClFnD,OAAA;UAAKkD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CnD,OAAA,CAACF,SAAS;YAACoD,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDvD,OAAA;YAAIkD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENvD,OAAA;UAAKkD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBnD,OAAA;YAAKkD,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCnD,OAAA;cAAKkD,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3EnD,OAAA;gBAAKkD,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAEjC,qBAAqB,CAAC0C;cAAM;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnFvD,OAAA;gBAAKkD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3EnD,OAAA;gBAAKkD,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC3CjC,qBAAqB,CAAC4C,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAK;kBAC7C,MAAM,CAAChB,IAAI,EAAEC,IAAI,CAAC,GAAGe,IAAI,CAAC3C,QAAQ,CAACmB,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACuB,MAAM,CAAC;kBACzD,OAAOF,KAAK,GAAGf,IAAI,GAAG,EAAE,GAAGC,IAAI;gBACjC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACNvD,OAAA;gBAAKkD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvD,OAAA;YAAKkD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBnD,OAAA;cAAAmD,QAAA,gBACEnD,OAAA;gBAAKkD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDnD,OAAA;kBAAMkD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjEvD,OAAA;kBAAMkD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACpChB,IAAI,CAAC+B,KAAK,CAAErD,KAAK,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1C,SAAS,CAAC,CAAC2C,MAAM,GAAG/C,KAAK,CAAC+C,MAAM,GAAI,GAAG,CAAC,EAAC,GAC5E;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNvD,OAAA;gBAAKkD,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClDnD,OAAA;kBACEkD,SAAS,EAAC,2DAA2D;kBACrEiB,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAIvD,KAAK,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1C,SAAS,CAAC,CAAC2C,MAAM,GAAG/C,KAAK,CAAC+C,MAAM,GAAI,GAAG;kBAAI;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvD,OAAA;cAAAmD,QAAA,gBACEnD,OAAA;gBAAKkD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDnD,OAAA;kBAAMkD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7DvD,OAAA;kBAAMkD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNvD,OAAA;gBAAKkD,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClDnD,OAAA;kBAAKkD,SAAS,EAAC,8BAA8B;kBAACiB,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvD,OAAA;cAAAmD,QAAA,gBACEnD,OAAA;gBAAKkD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDnD,OAAA;kBAAMkD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEvD,OAAA;kBAAMkD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNvD,OAAA;gBAAKkD,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClDnD,OAAA;kBAAKkD,SAAS,EAAC,gCAAgC;kBAACiB,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/C,EAAA,CA9SIP,aAA2C;AAAAoE,EAAA,GAA3CpE,aAA2C;AAgTjD,eAAeA,aAAa;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}