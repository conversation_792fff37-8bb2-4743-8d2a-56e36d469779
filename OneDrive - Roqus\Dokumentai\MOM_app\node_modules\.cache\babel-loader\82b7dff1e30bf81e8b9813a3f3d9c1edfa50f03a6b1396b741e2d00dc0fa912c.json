{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingButton.tsx\";\nimport React from 'react';\nimport { Mic, Square, Pause, Play } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RecordingButton = ({\n  recordingState,\n  onStartRecording,\n  onStopRecording,\n  onPauseRecording,\n  onResumeRecording\n}) => {\n  const {\n    isRecording,\n    isPaused\n  } = recordingState;\n  if (!isRecording) {\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onStartRecording,\n      className: \"flex items-center justify-center w-14 h-14 bg-gradient-to-br from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-red-200/50 border border-white/30 ring-1 ring-white/15 hover:border-red-300/40 pulse-subtle\",\n      children: /*#__PURE__*/_jsxDEV(Mic, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center space-x-3\",\n    children: [isPaused ? /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onResumeRecording,\n      className: \"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-green-500 via-green-600 to-emerald-600 hover:from-green-600 hover:via-green-700 hover:to-emerald-700 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-green-200/50 border border-green-400/30 hover:border-green-300/40\",\n      children: /*#__PURE__*/_jsxDEV(Play, {\n        className: \"w-5 h-5 ml-0.5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onPauseRecording,\n      className: \"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-yellow-500 via-yellow-600 to-orange-600 hover:from-yellow-600 hover:via-yellow-700 hover:to-orange-700 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-yellow-200/50 border border-yellow-400/30 hover:border-yellow-300/40\",\n      children: /*#__PURE__*/_jsxDEV(Pause, {\n        className: \"w-5 h-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onStopRecording,\n      className: \"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-gray-600 via-gray-700 to-slate-700 hover:from-gray-700 hover:via-gray-800 hover:to-slate-800 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-gray-300/50 border border-gray-500/30 hover:border-gray-400/40\",\n      children: /*#__PURE__*/_jsxDEV(Square, {\n        className: \"w-5 h-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_c = RecordingButton;\nvar _c;\n$RefreshReg$(_c, \"RecordingButton\");", "map": {"version": 3, "names": ["React", "Mic", "Square", "Pause", "Play", "jsxDEV", "_jsxDEV", "RecordingButton", "recordingState", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "isRecording", "isPaused", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingButton.tsx"], "sourcesContent": ["import React from 'react';\nimport { Mic, Square, Pause, Play } from 'lucide-react';\nimport { RecordingState } from '../types/meeting';\n\ninterface RecordingButtonProps {\n  recordingState: RecordingState;\n  onStartRecording: () => void;\n  onStopRecording: () => void;\n  onPauseRecording: () => void;\n  onResumeRecording: () => void;\n}\n\nexport const RecordingButton: React.FC<RecordingButtonProps> = ({\n  recordingState,\n  onStartRecording,\n  onStopRecording,\n  onPauseRecording,\n  onResumeRecording,\n}) => {\n  const { isRecording, isPaused } = recordingState;\n\n  if (!isRecording) {\n    return (\n      <button\n        onClick={onStartRecording}\n        className=\"flex items-center justify-center w-14 h-14 bg-gradient-to-br from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-red-200/50 border border-white/30 ring-1 ring-white/15 hover:border-red-300/40 pulse-subtle\"\n      >\n        <Mic className=\"w-6 h-6\" />\n      </button>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center space-x-3\">\n      {isPaused ? (\n        <button\n          onClick={onResumeRecording}\n          className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-green-500 via-green-600 to-emerald-600 hover:from-green-600 hover:via-green-700 hover:to-emerald-700 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-green-200/50 border border-green-400/30 hover:border-green-300/40\"\n        >\n          <Play className=\"w-5 h-5 ml-0.5\" />\n        </button>\n      ) : (\n        <button\n          onClick={onPauseRecording}\n          className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-yellow-500 via-yellow-600 to-orange-600 hover:from-yellow-600 hover:via-yellow-700 hover:to-orange-700 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-yellow-200/50 border border-yellow-400/30 hover:border-yellow-300/40\"\n        >\n          <Pause className=\"w-5 h-5\" />\n        </button>\n      )}\n      \n      <button\n        onClick={onStopRecording}\n        className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-gray-600 via-gray-700 to-slate-700 hover:from-gray-700 hover:via-gray-800 hover:to-slate-800 text-white rounded-3xl shadow-primary hover:shadow-gradient transition-smooth focus:outline-none focus:ring-4 focus:ring-gray-300/50 border border-gray-500/30 hover:border-gray-400/40\"\n      >\n        <Square className=\"w-5 h-5\" />\n      </button>\n    </div>\n  );\n};"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWxD,OAAO,MAAMC,eAA+C,GAAGA,CAAC;EAC9DC,cAAc;EACdC,gBAAgB;EAChBC,eAAe;EACfC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EACJ,MAAM;IAAEC,WAAW;IAAEC;EAAS,CAAC,GAAGN,cAAc;EAEhD,IAAI,CAACK,WAAW,EAAE;IAChB,oBACEP,OAAA;MACES,OAAO,EAAEN,gBAAiB;MAC1BO,SAAS,EAAC,uYAAuY;MAAAC,QAAA,eAEjZX,OAAA,CAACL,GAAG;QAACe,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAEb;EAEA,oBACEf,OAAA;IAAKU,SAAS,EAAC,6BAA6B;IAAAC,QAAA,GACzCH,QAAQ,gBACPR,OAAA;MACES,OAAO,EAAEH,iBAAkB;MAC3BI,SAAS,EAAC,mWAAmW;MAAAC,QAAA,eAE7WX,OAAA,CAACF,IAAI;QAACY,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,gBAETf,OAAA;MACES,OAAO,EAAEJ,gBAAiB;MAC1BK,SAAS,EAAC,wWAAwW;MAAAC,QAAA,eAElXX,OAAA,CAACH,KAAK;QAACa,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACT,eAEDf,OAAA;MACES,OAAO,EAAEL,eAAgB;MACzBM,SAAS,EAAC,wVAAwV;MAAAC,QAAA,eAElWX,OAAA,CAACJ,MAAM;QAACc,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACC,EAAA,GA9CWf,eAA+C;AAAA,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}