{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\TodosPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Plus, Edit, Trash2, X, Check, Search, ChevronDown, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TodosPage = () => {\n  _s();\n  const [todos, setTodos] = useState([{\n    id: 1,\n    text: 'Pasir<PERSON><PERSON><PERSON> prezentacijai',\n    completed: false,\n    date: '2024-01-20',\n    priority: 'high',\n    project: 'Aexn CRM analitika',\n    owner: '<PERSON>'\n  }, {\n    id: 2,\n    text: 'Susitikimas su klientu',\n    completed: true,\n    date: '2024-01-18',\n    priority: 'medium',\n    project: 'Aexn | <PERSON><PERSON><PERSON>',\n    owner: '<PERSON><PERSON>'\n  }, {\n    id: 3,\n    text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokument<PERSON>',\n    completed: false,\n    date: '2024-01-22',\n    priority: 'low',\n    project: 'Aexn_analitika',\n    owner: 'Ona Onaitytė'\n  }, {\n    id: 4,\n    text: 'Planuoti kitą savaitę',\n    completed: false,\n    priority: 'medium',\n    project: 'Aexn_analitika',\n    owner: 'Jonas Jonaitis'\n  }]);\n  const [editingTodo, setEditingTodo] = useState(null);\n  const [editingText, setEditingText] = useState('');\n  const [editingProject, setEditingProject] = useState('');\n  const [editingOwner, setEditingOwner] = useState('');\n  const [newTodoText, setNewTodoText] = useState('');\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\n  const [newTodoDate, setNewTodoDate] = useState('');\n  const [newTodoPriority, setNewTodoPriority] = useState('medium');\n  const [newTodoProject, setNewTodoProject] = useState('');\n  const [newTodoOwner, setNewTodoOwner] = useState('');\n  const [search, setSearch] = useState('');\n  const [filterProject, setFilterProject] = useState('');\n  const [collapsedProjects, setCollapsedProjects] = useState({});\n\n  // Funkcijos\n  const toggleTodo = id => {\n    console.log('Toggle todo:', id); // Debug\n    setTodos(prev => prev.map(todo => todo.id === id ? {\n      ...todo,\n      completed: !todo.completed\n    } : todo));\n  };\n  const deleteTodo = id => {\n    console.log('Delete todo:', id); // Debug\n    setTodos(prev => prev.filter(todo => todo.id !== id));\n  };\n  const startEditingTodo = todo => {\n    console.log('Start editing:', todo); // Debug\n    setEditingTodo(todo.id);\n    setEditingText(todo.text);\n    setEditingProject(todo.project || '');\n    setEditingOwner(todo.owner || '');\n  };\n  const saveEditedTodo = () => {\n    console.log('Save edited todo'); // Debug\n    if (editingTodo && editingText.trim()) {\n      setTodos(prev => prev.map(todo => todo.id === editingTodo ? {\n        ...todo,\n        text: editingText.trim(),\n        project: editingProject.trim() || undefined,\n        owner: editingOwner.trim() || undefined\n      } : todo));\n      cancelEditing();\n    }\n  };\n  const cancelEditing = () => {\n    setEditingTodo(null);\n    setEditingText('');\n    setEditingProject('');\n    setEditingOwner('');\n  };\n  const addNewTodo = () => {\n    console.log('Add new todo'); // Debug\n    if (newTodoText.trim()) {\n      const newTodo = {\n        id: Date.now(),\n        text: newTodoText.trim(),\n        completed: false,\n        date: newTodoDate || undefined,\n        priority: newTodoPriority,\n        project: newTodoProject.trim() || undefined,\n        owner: newTodoOwner.trim() || undefined\n      };\n      setTodos(prev => [...prev, newTodo]);\n      cancelNewTodo();\n    }\n  };\n  const cancelNewTodo = () => {\n    setShowNewTodoForm(false);\n    setNewTodoText('');\n    setNewTodoDate('');\n    setNewTodoPriority('medium');\n    setNewTodoProject('');\n    setNewTodoOwner('');\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-400';\n      case 'medium':\n        return 'text-yellow-400';\n      case 'low':\n        return 'text-green-400';\n      default:\n        return 'text-white/60';\n    }\n  };\n  const getPriorityLabel = priority => {\n    switch (priority) {\n      case 'high':\n        return 'Aukštas';\n      case 'medium':\n        return 'Vidutinis';\n      case 'low':\n        return 'Žemas';\n      default:\n        return 'Nežinomas';\n    }\n  };\n  const toggleCollapse = project => {\n    console.log('Toggle collapse:', project); // Debug\n    setCollapsedProjects(prev => ({\n      ...prev,\n      [project]: !prev[project]\n    }));\n  };\n  const completeAllInProject = project => {\n    console.log('Complete all in project:', project); // Debug\n    setTodos(prev => prev.map(todo => todo.project === project ? {\n      ...todo,\n      completed: true\n    } : todo));\n  };\n  const handleKeyPress = (e, action) => {\n    if (e.key === 'Enter') {\n      action();\n    }\n  };\n\n  // Memoized values\n  const uniqueProjects = useMemo(() => {\n    const projects = todos.map(todo => todo.project).filter((project, index, array) => project && array.indexOf(project) === index).sort();\n    return projects;\n  }, [todos]);\n  const filteredTodos = useMemo(() => {\n    return todos.filter(todo => {\n      const matchesSearch = search === '' || todo.text.toLowerCase().includes(search.toLowerCase()) || todo.owner && todo.owner.toLowerCase().includes(search.toLowerCase()) || todo.project && todo.project.toLowerCase().includes(search.toLowerCase());\n      const matchesProject = filterProject === '' || todo.project === filterProject;\n      return matchesSearch && matchesProject;\n    });\n  }, [todos, search, filterProject]);\n  const groupedTodos = useMemo(() => {\n    return filteredTodos.reduce((groups, todo) => {\n      const key = todo.project || 'Kiti projektai';\n      if (!groups[key]) groups[key] = [];\n      groups[key].push(todo);\n      return groups;\n    }, {});\n  }, [filteredTodos]);\n  const stats = useMemo(() => {\n    const projectCount = Object.keys(groupedTodos).length;\n    const totalCount = filteredTodos.length;\n    const doneCount = filteredTodos.filter(t => t.completed).length;\n    const undoneCount = totalCount - doneCount;\n    const progress = totalCount > 0 ? Math.round(doneCount / totalCount * 100) : 0;\n    return {\n      projectCount,\n      totalCount,\n      doneCount,\n      undoneCount,\n      progress\n    };\n  }, [filteredTodos, groupedTodos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-6 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white text-2xl font-bold\",\n            children: stats.projectCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Projekt\\u0173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white text-2xl font-bold\",\n            children: stats.totalCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"U\\u017Eduo\\u010Di\\u0173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-green-400 text-2xl font-bold\",\n            children: stats.doneCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Atlikta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-red-400 text-2xl font-bold\",\n            children: stats.undoneCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Neatlikta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col gap-2 min-w-[200px]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white/60 text-sm\",\n            children: \"Progresas:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white font-semibold\",\n            children: [stats.progress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-white/20 rounded-full h-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full transition-all duration-500\",\n            style: {\n              width: `${stats.progress}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col md:flex-row gap-4 items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2 items-center w-full md:w-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative w-full md:w-64\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 pointer-events-none z-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: search,\n            onChange: e => setSearch(e.target.value),\n            placeholder: \"Ie\\u0161koti u\\u017Eduoties, asmens ar projekto...\",\n            className: \"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterProject,\n          onChange: e => setFilterProject(e.target.value),\n          className: \"bg-white/10 border border-white/20 rounded-xl px-3 py-3 text-white focus:outline-none focus:border-blue-400 min-w-[150px]\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Visi projektai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), uniqueProjects.map(project => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: project,\n            children: project\n          }, project, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          console.log('Show new todo form'); // Debug\n          setShowNewTodoForm(true);\n        },\n        className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), \"Prid\\u0117ti u\\u017Eduot\\u012F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: Object.keys(groupedTodos).length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-12 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/60 text-lg mb-2\",\n          children: \"U\\u017Eduo\\u010Di\\u0173 nerasta\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white/40 text-sm\",\n          children: search || filterProject ? 'Pakeiskite paieškos kriterijus' : 'Pridėkite pirmą užduotį'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this) : Object.entries(groupedTodos).map(([project, projectTodos]) => {\n        const projectDone = projectTodos.filter(t => t.completed).length;\n        const projectTotal = projectTodos.length;\n        const projectProgress = projectTotal > 0 ? Math.round(projectDone / projectTotal * 100) : 0;\n        const isCollapsed = collapsedProjects[project];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-2 mt-4 border-b border-white/20 pb-2 pl-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleCollapse(project),\n                className: \"focus:outline-none hover:bg-white/10 p-1 rounded-lg transition-colors\",\n                \"aria-label\": isCollapsed ? 'Išplėsti projektą' : 'Suskleisti projektą',\n                children: isCollapsed ? /*#__PURE__*/_jsxDEV(ChevronRight, {\n                  className: \"h-5 w-5 text-white/60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                  className: \"h-5 w-5 text-white/60\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white/80 text-base font-semibold\",\n                children: project\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-white/50 bg-white/10 px-2 py-1 rounded-full\",\n                children: [projectDone, \" / \", projectTotal, \" atlikta\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-32 bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-500\",\n                  style: {\n                    width: `${projectProgress}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this), projectTotal > projectDone && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => completeAllInProject(project),\n              className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md text-sm\",\n              children: \"Atlikti visas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 rounded-xl overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full text-sm text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"bg-white/10\",\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"U\\u017Eduotis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"Atsakingas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"Prioritetas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"B\\u016Bsena\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left font-medium\",\n                    children: \"Veiksmai\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: projectTodos.map(todo => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: `border-b border-white/5 hover:bg-white/5 transition-colors ${todo.completed ? 'bg-green-500/5 text-white/70' : ''}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: `px-4 py-3 ${todo.completed ? 'line-through' : ''}`,\n                    children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingText,\n                      onChange: e => setEditingText(e.target.value),\n                      onKeyPress: e => handleKeyPress(e, saveEditedTodo),\n                      className: \"w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400\",\n                      autoFocus: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"block py-1\",\n                      children: todo.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingOwner,\n                      onChange: e => setEditingOwner(e.target.value),\n                      onKeyPress: e => handleKeyPress(e, saveEditedTodo),\n                      className: \"w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-white/80\",\n                      children: todo.owner || '—'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-white/60\",\n                      children: todo.date || '—'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`,\n                      children: getPriorityLabel(todo.priority)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleTodo(todo.id),\n                      className: `relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${todo.completed ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg focus:ring-green-400' : 'bg-white/20 focus:ring-white/40'}`,\n                      \"aria-label\": \"Pa\\u017Eym\\u0117ti kaip atlikt\\u0105\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center ${todo.completed ? 'translate-x-4 bg-green-400' : ''}`,\n                        children: todo.completed && /*#__PURE__*/_jsxDEV(Check, {\n                          className: \"h-3 w-3 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 382,\n                          columnNumber: 54\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-2\",\n                      children: editingTodo === todo.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: saveEditedTodo,\n                          className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white p-2 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\",\n                          title: \"I\\u0161saugoti pakeitimus\",\n                          children: /*#__PURE__*/_jsxDEV(Check, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 395,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 390,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: cancelEditing,\n                          className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\",\n                          title: \"At\\u0161aukti redagavim\\u0105\",\n                          children: /*#__PURE__*/_jsxDEV(X, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 402,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 397,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => startEditingTodo(todo),\n                          className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white p-2 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\",\n                          title: \"Redaguoti u\\u017Eduot\\u012F\",\n                          children: /*#__PURE__*/_jsxDEV(Edit, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 412,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 407,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => deleteTodo(todo.id),\n                          className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\",\n                          title: \"I\\u0161trinti u\\u017Eduot\\u012F\",\n                          children: /*#__PURE__*/_jsxDEV(Trash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 419,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 414,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 29\n                  }, this)]\n                }, todo.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 19\n          }, this)]\n        }, project, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), stats.totalCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/5 rounded-xl p-4 border border-white/10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-white/60 text-sm\",\n        children: [\"Rodoma: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white font-medium\",\n          children: stats.totalCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 21\n        }, this), \" u\\u017Eduo\\u010Di\\u0173 \\u2022 U\\u017Ebaigta: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-green-400 font-medium\",\n          children: stats.doneCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 23\n        }, this), \" \\u2022 Liko: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-400 font-medium\",\n          children: stats.undoneCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 19\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 9\n    }, this), showNewTodoForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white mb-4\",\n          children: \"Prid\\u0117ti nauj\\u0105 u\\u017Eduot\\u012F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-white/70 mb-2\",\n              children: \"U\\u017Eduoties pavadinimas *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoText,\n              onChange: e => setNewTodoText(e.target.value),\n              onKeyPress: e => handleKeyPress(e, addNewTodo),\n              placeholder: \"\\u012Eveskite u\\u017Eduoties pavadinim\\u0105...\",\n              className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-white/70 mb-2\",\n              children: \"Atsakingas asmuo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoOwner,\n              onChange: e => setNewTodoOwner(e.target.value),\n              placeholder: \"\\u012Eveskite atsakingo asmens vard\\u0105...\",\n              className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-white/70 mb-2\",\n              children: \"Projektas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newTodoProject,\n              onChange: e => setNewTodoProject(e.target.value),\n              placeholder: \"\\u012Eveskite projekto pavadinim\\u0105...\",\n              className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n              list: \"existing-projects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"datalist\", {\n              id: \"existing-projects\",\n              children: uniqueProjects.map(project => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: project\n              }, project, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-white/70 mb-2\",\n                children: \"Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: newTodoDate,\n                onChange: e => setNewTodoDate(e.target.value),\n                className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-white/70 mb-2\",\n                children: \"Prioritetas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newTodoPriority,\n                onChange: e => setNewTodoPriority(e.target.value),\n                className: \"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"low\",\n                  children: \"\\u017Demas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"medium\",\n                  children: \"Vidutinis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high\",\n                  children: \"Auk\\u0161tas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3 mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: cancelNewTodo,\n            className: \"flex-1 bg-white/10 text-white px-4 py-3 rounded-xl font-medium hover:bg-white/20 transition-all duration-200 border border-white/20\",\n            children: \"At\\u0161aukti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: addNewTodo,\n            disabled: !newTodoText.trim(),\n            className: \"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: \"Prid\\u0117ti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n};\n_s(TodosPage, \"ft9RMlYBEXBUotah2qK6YVT8jro=\");\n_c = TodosPage;\nexport default TodosPage;\nvar _c;\n$RefreshReg$(_c, \"TodosPage\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "Plus", "Edit", "Trash2", "X", "Check", "Search", "ChevronDown", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TodosPage", "_s", "todos", "<PERSON><PERSON><PERSON><PERSON>", "id", "text", "completed", "date", "priority", "project", "owner", "editingTodo", "setEditingTodo", "editingText", "setEditingText", "editingProject", "setEditingProject", "<PERSON><PERSON><PERSON><PERSON>", "setEditingOwner", "newTodoText", "setNewTodoText", "showNewTodoForm", "setShowNewTodoForm", "newTodoDate", "setNewTodoDate", "newTodoPriority", "setNewTodoPriority", "newTodoProject", "setNewTodoProject", "newTodoOwner", "setNewTodoOwner", "search", "setSearch", "filterProject", "setFilterProject", "collapsedProjects", "setCollapsedProjects", "toggleTodo", "console", "log", "prev", "map", "todo", "deleteTodo", "filter", "startEditingTodo", "saveEditedTodo", "trim", "undefined", "cancelEditing", "addNewTodo", "newTodo", "Date", "now", "cancelNewTodo", "getPriorityColor", "getPriorityLabel", "toggleCollapse", "completeAllInProject", "handleKeyPress", "e", "action", "key", "uniqueProjects", "projects", "index", "array", "indexOf", "sort", "filteredTodos", "matchesSearch", "toLowerCase", "includes", "matchesProject", "groupedTodos", "reduce", "groups", "push", "stats", "projectCount", "Object", "keys", "length", "totalCount", "doneCount", "t", "undoneCount", "progress", "Math", "round", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "type", "value", "onChange", "target", "placeholder", "onClick", "entries", "projectTodos", "projectDone", "projectTotal", "projectProgress", "isCollapsed", "onKeyPress", "autoFocus", "title", "list", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TodosPage.tsx"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\r\nimport { Plus, Edit, Trash2, X, Check, Search, ChevronDown, ChevronRight } from 'lucide-react';\r\n\r\ninterface Todo {\r\n  id: number;\r\n  text: string;\r\n  completed: boolean;\r\n  date?: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n  project?: string;\r\n  owner?: string;\r\n}\r\n\r\nconst TodosPage: React.FC = () => {\r\n  const [todos, setTodos] = useState<Todo[]>([\r\n    { id: 1, text: '<PERSON>sir<PERSON><PERSON><PERSON> prezenta<PERSON>', completed: false, date: '2024-01-20', priority: 'high', project: 'Aexn CRM analitika', owner: '<PERSON>' },\r\n    { id: 2, text: 'Susitikimas su klientu', completed: true, date: '2024-01-18', priority: 'medium', project: 'Aexn | Analitika', owner: '<PERSON><PERSON>' },\r\n    { id: 3, text: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokumentus', completed: false, date: '2024-01-22', priority: 'low', project: 'Aexn_analitika', owner: '<PERSON><PERSON>' },\r\n    { id: 4, text: 'Planuoti kitą savaitę', completed: false, priority: 'medium', project: 'Aexn_analitika', owner: '<PERSON>aitis' }\r\n  ]);\r\n  \r\n  const [editingTodo, setEditingTodo] = useState<number | null>(null);\r\n  const [editingText, setEditingText] = useState('');\r\n  const [editingProject, setEditingProject] = useState('');\r\n  const [editingOwner, setEditingOwner] = useState('');\r\n  const [newTodoText, setNewTodoText] = useState('');\r\n  const [showNewTodoForm, setShowNewTodoForm] = useState(false);\r\n  const [newTodoDate, setNewTodoDate] = useState('');\r\n  const [newTodoPriority, setNewTodoPriority] = useState<'low' | 'medium' | 'high'>('medium');\r\n  const [newTodoProject, setNewTodoProject] = useState('');\r\n  const [newTodoOwner, setNewTodoOwner] = useState('');\r\n  const [search, setSearch] = useState('');\r\n  const [filterProject, setFilterProject] = useState('');\r\n  const [collapsedProjects, setCollapsedProjects] = useState<Record<string, boolean>>({});\r\n\r\n  // Funkcijos\r\n  const toggleTodo = (id: number) => {\r\n    console.log('Toggle todo:', id); // Debug\r\n    setTodos(prev => prev.map(todo => \r\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\r\n    ));\r\n  };\r\n\r\n  const deleteTodo = (id: number) => {\r\n    console.log('Delete todo:', id); // Debug\r\n    setTodos(prev => prev.filter(todo => todo.id !== id));\r\n  };\r\n\r\n  const startEditingTodo = (todo: Todo) => {\r\n    console.log('Start editing:', todo); // Debug\r\n    setEditingTodo(todo.id);\r\n    setEditingText(todo.text);\r\n    setEditingProject(todo.project || '');\r\n    setEditingOwner(todo.owner || '');\r\n  };\r\n\r\n  const saveEditedTodo = () => {\r\n    console.log('Save edited todo'); // Debug\r\n    if (editingTodo && editingText.trim()) {\r\n      setTodos(prev => prev.map(todo => \r\n        todo.id === editingTodo ? { \r\n          ...todo, \r\n          text: editingText.trim(), \r\n          project: editingProject.trim() || undefined, \r\n          owner: editingOwner.trim() || undefined \r\n        } : todo\r\n      ));\r\n      cancelEditing();\r\n    }\r\n  };\r\n\r\n  const cancelEditing = () => {\r\n    setEditingTodo(null);\r\n    setEditingText('');\r\n    setEditingProject('');\r\n    setEditingOwner('');\r\n  };\r\n\r\n  const addNewTodo = () => {\r\n    console.log('Add new todo'); // Debug\r\n    if (newTodoText.trim()) {\r\n      const newTodo: Todo = {\r\n        id: Date.now(),\r\n        text: newTodoText.trim(),\r\n        completed: false,\r\n        date: newTodoDate || undefined,\r\n        priority: newTodoPriority,\r\n        project: newTodoProject.trim() || undefined,\r\n        owner: newTodoOwner.trim() || undefined\r\n      };\r\n      setTodos(prev => [...prev, newTodo]);\r\n      cancelNewTodo();\r\n    }\r\n  };\r\n\r\n  const cancelNewTodo = () => {\r\n    setShowNewTodoForm(false);\r\n    setNewTodoText('');\r\n    setNewTodoDate('');\r\n    setNewTodoPriority('medium');\r\n    setNewTodoProject('');\r\n    setNewTodoOwner('');\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'text-red-400';\r\n      case 'medium': return 'text-yellow-400';\r\n      case 'low': return 'text-green-400';\r\n      default: return 'text-white/60';\r\n    }\r\n  };\r\n\r\n  const getPriorityLabel = (priority: string) => {\r\n    switch (priority) {\r\n      case 'high': return 'Aukštas';\r\n      case 'medium': return 'Vidutinis';\r\n      case 'low': return 'Žemas';\r\n      default: return 'Nežinomas';\r\n    }\r\n  };\r\n\r\n  const toggleCollapse = (project: string) => {\r\n    console.log('Toggle collapse:', project); // Debug\r\n    setCollapsedProjects(prev => ({ ...prev, [project]: !prev[project] }));\r\n  };\r\n\r\n  const completeAllInProject = (project: string) => {\r\n    console.log('Complete all in project:', project); // Debug\r\n    setTodos(prev => prev.map(todo => \r\n      todo.project === project ? { ...todo, completed: true } : todo\r\n    ));\r\n  };\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {\r\n    if (e.key === 'Enter') {\r\n      action();\r\n    }\r\n  };\r\n\r\n  // Memoized values\r\n  const uniqueProjects = useMemo(() => {\r\n    const projects = todos\r\n      .map(todo => todo.project)\r\n      .filter((project, index, array) => project && array.indexOf(project) === index)\r\n      .sort();\r\n    return projects as string[];\r\n  }, [todos]);\r\n\r\n  const filteredTodos = useMemo(() => {\r\n    return todos.filter(todo => {\r\n      const matchesSearch = search === '' || \r\n        todo.text.toLowerCase().includes(search.toLowerCase()) ||\r\n        (todo.owner && todo.owner.toLowerCase().includes(search.toLowerCase())) ||\r\n        (todo.project && todo.project.toLowerCase().includes(search.toLowerCase()));\r\n      \r\n      const matchesProject = filterProject === '' || todo.project === filterProject;\r\n      \r\n      return matchesSearch && matchesProject;\r\n    });\r\n  }, [todos, search, filterProject]);\r\n\r\n  const groupedTodos = useMemo(() => {\r\n    return filteredTodos.reduce((groups, todo) => {\r\n      const key = todo.project || 'Kiti projektai';\r\n      if (!groups[key]) groups[key] = [];\r\n      groups[key].push(todo);\r\n      return groups;\r\n    }, {} as Record<string, Todo[]>);\r\n  }, [filteredTodos]);\r\n\r\n  const stats = useMemo(() => {\r\n    const projectCount = Object.keys(groupedTodos).length;\r\n    const totalCount = filteredTodos.length;\r\n    const doneCount = filteredTodos.filter(t => t.completed).length;\r\n    const undoneCount = totalCount - doneCount;\r\n    const progress = totalCount > 0 ? Math.round((doneCount / totalCount) * 100) : 0;\r\n    \r\n    return { projectCount, totalCount, doneCount, undoneCount, progress };\r\n  }, [filteredTodos, groupedTodos]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Apžvalgos panelė */}\r\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6\">\r\n        <div className=\"flex flex-wrap gap-6 items-center\">\r\n          <div>\r\n            <div className=\"text-white text-2xl font-bold\">{stats.projectCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Projektų</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-white text-2xl font-bold\">{stats.totalCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Užduočių</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-green-400 text-2xl font-bold\">{stats.doneCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Atlikta</div>\r\n          </div>\r\n          <div>\r\n            <div className=\"text-red-400 text-2xl font-bold\">{stats.undoneCount}</div>\r\n            <div className=\"text-white/60 text-sm\">Neatlikta</div>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex-1 flex flex-col gap-2 min-w-[200px]\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-white/60 text-sm\">Progresas:</span>\r\n            <span className=\"text-white font-semibold\">{stats.progress}%</span>\r\n          </div>\r\n          <div className=\"w-full bg-white/20 rounded-full h-3\">\r\n            <div \r\n              className=\"bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full transition-all duration-500\" \r\n              style={{ width: `${stats.progress}%` }}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filtravimas ir paieška */}\r\n      <div className=\"flex flex-col md:flex-row gap-4 items-center justify-between\">\r\n        <div className=\"flex gap-2 items-center w-full md:w-auto\">\r\n          {/* Paieškos laukas */}\r\n          <div className=\"relative w-full md:w-64\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 pointer-events-none z-10\" />\r\n            <input\r\n              type=\"text\"\r\n              value={search}\r\n              onChange={e => setSearch(e.target.value)}\r\n              placeholder=\"Ieškoti užduoties, asmens ar projekto...\"\r\n              className=\"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n            />\r\n          </div>\r\n          \r\n          {/* Projektų filtras */}\r\n          <select\r\n            value={filterProject}\r\n            onChange={e => setFilterProject(e.target.value)}\r\n            className=\"bg-white/10 border border-white/20 rounded-xl px-3 py-3 text-white focus:outline-none focus:border-blue-400 min-w-[150px]\"\r\n          >\r\n            <option value=\"\">Visi projektai</option>\r\n            {uniqueProjects.map(project => (\r\n              <option key={project} value={project}>{project}</option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n        \r\n        {/* Pridėti užduotį mygtukas */}\r\n        <button\r\n          onClick={() => {\r\n            console.log('Show new todo form'); // Debug\r\n            setShowNewTodoForm(true);\r\n          }}\r\n          className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium flex items-center gap-2 hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md\"\r\n        >\r\n          <Plus className=\"h-4 w-4\" />\r\n          Pridėti užduotį\r\n        </button>\r\n      </div>\r\n\r\n      {/* Užduočių grupės */}\r\n      <div className=\"overflow-x-auto\">\r\n        {Object.keys(groupedTodos).length === 0 ? (\r\n          <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-12 text-center\">\r\n            <div className=\"text-white/60 text-lg mb-2\">Užduočių nerasta</div>\r\n            <div className=\"text-white/40 text-sm\">\r\n              {search || filterProject ? 'Pakeiskite paieškos kriterijus' : 'Pridėkite pirmą užduotį'}\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          Object.entries(groupedTodos).map(([project, projectTodos]) => {\r\n            const projectDone = projectTodos.filter(t => t.completed).length;\r\n            const projectTotal = projectTodos.length;\r\n            const projectProgress = projectTotal > 0 ? Math.round((projectDone / projectTotal) * 100) : 0;\r\n            const isCollapsed = collapsedProjects[project];\r\n\r\n            return (\r\n              <div key={project} className=\"mb-8\">\r\n                <div className=\"flex items-center justify-between mb-2 mt-4 border-b border-white/20 pb-2 pl-1\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <button \r\n                      onClick={() => toggleCollapse(project)} \r\n                      className=\"focus:outline-none hover:bg-white/10 p-1 rounded-lg transition-colors\"\r\n                      aria-label={isCollapsed ? 'Išplėsti projektą' : 'Suskleisti projektą'}\r\n                    >\r\n                      {isCollapsed ? \r\n                        <ChevronRight className=\"h-5 w-5 text-white/60\" /> : \r\n                        <ChevronDown className=\"h-5 w-5 text-white/60\" />\r\n                      }\r\n                    </button>\r\n                    <span className=\"text-white/80 text-base font-semibold\">{project}</span>\r\n                    <span className=\"text-xs text-white/50 bg-white/10 px-2 py-1 rounded-full\">\r\n                      {projectDone} / {projectTotal} atlikta\r\n                    </span>\r\n                    <div className=\"w-32 bg-white/20 rounded-full h-2\">\r\n                      <div \r\n                        className=\"bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-500\" \r\n                        style={{ width: `${projectProgress}%` }}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {projectTotal > projectDone && (\r\n                    <button\r\n                      onClick={() => completeAllInProject(project)}\r\n                      className=\"bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md text-sm\"\r\n                    >\r\n                      Atlikti visas\r\n                    </button>\r\n                  )}\r\n                </div>\r\n\r\n                {!isCollapsed && (\r\n                  <div className=\"bg-white/5 rounded-xl overflow-hidden\">\r\n                    <table className=\"min-w-full text-sm text-white\">\r\n                      <thead>\r\n                        <tr className=\"bg-white/10\">\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Užduotis</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Atsakingas</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Data</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Prioritetas</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Būsena</th>\r\n                          <th className=\"px-4 py-3 text-left font-medium\">Veiksmai</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {projectTodos.map((todo) => (\r\n                          <tr\r\n                            key={todo.id}\r\n                            className={`border-b border-white/5 hover:bg-white/5 transition-colors ${\r\n                              todo.completed ? 'bg-green-500/5 text-white/70' : ''\r\n                            }`}\r\n                          >\r\n                            <td className={`px-4 py-3 ${todo.completed ? 'line-through' : ''}`}>\r\n                              {editingTodo === todo.id ? (\r\n                                <input\r\n                                  type=\"text\"\r\n                                  value={editingText}\r\n                                  onChange={e => setEditingText(e.target.value)}\r\n                                  onKeyPress={e => handleKeyPress(e, saveEditedTodo)}\r\n                                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                                  autoFocus\r\n                                />\r\n                              ) : (\r\n                                <span className=\"block py-1\">{todo.text}</span>\r\n                              )}\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              {editingTodo === todo.id ? (\r\n                                <input\r\n                                  type=\"text\"\r\n                                  value={editingOwner}\r\n                                  onChange={e => setEditingOwner(e.target.value)}\r\n                                  onKeyPress={e => handleKeyPress(e, saveEditedTodo)}\r\n                                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white focus:outline-none focus:border-blue-400\"\r\n                                />\r\n                              ) : (\r\n                                <span className=\"text-white/80\">{todo.owner || '—'}</span>\r\n                              )}\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              <span className=\"text-white/60\">{todo.date || '—'}</span>\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(todo.priority)} bg-white/10`}>\r\n                                {getPriorityLabel(todo.priority)}\r\n                              </span>\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              <button\r\n                                onClick={() => toggleTodo(todo.id)}\r\n                                className={`relative w-10 h-6 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${\r\n                                  todo.completed \r\n                                    ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg focus:ring-green-400' \r\n                                    : 'bg-white/20 focus:ring-white/40'\r\n                                }`}\r\n                                aria-label=\"Pažymėti kaip atliktą\"\r\n                              >\r\n                                <span\r\n                                  className={`absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center ${\r\n                                    todo.completed ? 'translate-x-4 bg-green-400' : ''\r\n                                  }`}\r\n                                >\r\n                                  {todo.completed && <Check className=\"h-3 w-3 text-white\" />}\r\n                                </span>\r\n                              </button>\r\n                            </td>\r\n                            <td className=\"px-4 py-3\">\r\n                              <div className=\"flex gap-2\">\r\n                                {editingTodo === todo.id ? (\r\n                                  <>\r\n                                    <button \r\n                                      onClick={saveEditedTodo} \r\n                                      className=\"bg-gradient-to-r from-green-500 to-emerald-500 text-white p-2 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\"\r\n                                      title=\"Išsaugoti pakeitimus\"\r\n                                    >\r\n                                      <Check className=\"h-4 w-4\" />\r\n                                    </button>\r\n                                    <button \r\n                                      onClick={cancelEditing} \r\n                                      className=\"bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\"\r\n                                      title=\"Atšaukti redagavimą\"\r\n                                    >\r\n                                      <X className=\"h-4 w-4\" />\r\n                                    </button>\r\n                                  </>\r\n                                ) : (\r\n                                  <>\r\n                                    <button \r\n                                      onClick={() => startEditingTodo(todo)} \r\n                                      className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white p-2 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\"\r\n                                      title=\"Redaguoti užduotį\"\r\n                                    >\r\n                                      <Edit className=\"h-4 w-4\" />\r\n                                    </button>\r\n                                    <button \r\n                                      onClick={() => deleteTodo(todo.id)} \r\n                                      className=\"bg-gradient-to-r from-red-500 to-pink-500 text-white p-2 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center\"\r\n                                      title=\"Ištrinti užduotį\"\r\n                                    >\r\n                                      <Trash2 className=\"h-4 w-4\" />\r\n                                    </button>\r\n                                  </>\r\n                                )}\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })\r\n        )}\r\n      </div>\r\n\r\n      {/* Suvestinė */}\r\n      {stats.totalCount > 0 && (\r\n        <div className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n          <div className=\"text-white/60 text-sm\">\r\n            Rodoma: <span className=\"text-white font-medium\">{stats.totalCount}</span> užduočių • \r\n            Užbaigta: <span className=\"text-green-400 font-medium\">{stats.doneCount}</span> • \r\n            Liko: <span className=\"text-red-400 font-medium\">{stats.undoneCount}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Nauja užduoties forma */}\r\n      {showNewTodoForm && (\r\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\">\r\n          <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 w-full max-w-md mx-4\">\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Pridėti naują užduotį</h3>\r\n            \r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-white/70 mb-2\">Užduoties pavadinimas *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoText}\r\n                  onChange={e => setNewTodoText(e.target.value)}\r\n                  onKeyPress={e => handleKeyPress(e, addNewTodo)}\r\n                  placeholder=\"Įveskite užduoties pavadinimą...\"\r\n                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                  autoFocus\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-white/70 mb-2\">Atsakingas asmuo</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoOwner}\r\n                  onChange={e => setNewTodoOwner(e.target.value)}\r\n                  placeholder=\"Įveskite atsakingo asmens vardą...\"\r\n                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-white/70 mb-2\">Projektas</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTodoProject}\r\n                  onChange={e => setNewTodoProject(e.target.value)}\r\n                  placeholder=\"Įveskite projekto pavadinimą...\"\r\n                  className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                  list=\"existing-projects\"\r\n                />\r\n                <datalist id=\"existing-projects\">\r\n                  {uniqueProjects.map(project => (\r\n                    <option key={project} value={project} />\r\n                  ))}\r\n                </datalist>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-white/70 mb-2\">Data</label>\r\n                  <input\r\n                    type=\"date\"\r\n                    value={newTodoDate}\r\n                    onChange={e => setNewTodoDate(e.target.value)}\r\n                    className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-white/70 mb-2\">Prioritetas</label>\r\n                  <select\r\n                    value={newTodoPriority}\r\n                    onChange={e => setNewTodoPriority(e.target.value as 'low' | 'medium' | 'high')}\r\n                    className=\"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\"\r\n                  >\r\n                    <option value=\"low\">Žemas</option>\r\n                    <option value=\"medium\">Vidutinis</option>\r\n                    <option value=\"high\">Aukštas</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex gap-3 mt-6\">\r\n              <button\r\n                onClick={cancelNewTodo}\r\n                className=\"flex-1 bg-white/10 text-white px-4 py-3 rounded-xl font-medium hover:bg-white/20 transition-all duration-200 border border-white/20\"\r\n              >\r\n                Atšaukti\r\n              </button>\r\n              <button\r\n                onClick={addNewTodo}\r\n                disabled={!newTodoText.trim()}\r\n                className=\"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Pridėti\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TodosPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/F,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAS,CACzC;IAAEkB,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,MAAM;IAAEC,OAAO,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC3J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC1J;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,KAAK;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAe,CAAC,EACjJ;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,SAAS,EAAE,KAAK;IAAEE,QAAQ,EAAE,QAAQ;IAAEC,OAAO,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACnI,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAA4B,QAAQ,CAAC;EAC3F,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAA0B,CAAC,CAAC,CAAC;;EAEvF;EACA,MAAMmD,UAAU,GAAIjC,EAAU,IAAK;IACjCkC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEnC,EAAE,CAAC,CAAC,CAAC;IACjCD,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACtC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGsC,IAAI;MAAEpC,SAAS,EAAE,CAACoC,IAAI,CAACpC;IAAU,CAAC,GAAGoC,IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIvC,EAAU,IAAK;IACjCkC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEnC,EAAE,CAAC,CAAC,CAAC;IACjCD,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACI,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACtC,EAAE,KAAKA,EAAE,CAAC,CAAC;EACvD,CAAC;EAED,MAAMyC,gBAAgB,GAAIH,IAAU,IAAK;IACvCJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEG,IAAI,CAAC,CAAC,CAAC;IACrC9B,cAAc,CAAC8B,IAAI,CAACtC,EAAE,CAAC;IACvBU,cAAc,CAAC4B,IAAI,CAACrC,IAAI,CAAC;IACzBW,iBAAiB,CAAC0B,IAAI,CAACjC,OAAO,IAAI,EAAE,CAAC;IACrCS,eAAe,CAACwB,IAAI,CAAChC,KAAK,IAAI,EAAE,CAAC;EACnC,CAAC;EAED,MAAMoC,cAAc,GAAGA,CAAA,KAAM;IAC3BR,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;IACjC,IAAI5B,WAAW,IAAIE,WAAW,CAACkC,IAAI,CAAC,CAAC,EAAE;MACrC5C,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACtC,EAAE,KAAKO,WAAW,GAAG;QACxB,GAAG+B,IAAI;QACPrC,IAAI,EAAEQ,WAAW,CAACkC,IAAI,CAAC,CAAC;QACxBtC,OAAO,EAAEM,cAAc,CAACgC,IAAI,CAAC,CAAC,IAAIC,SAAS;QAC3CtC,KAAK,EAAEO,YAAY,CAAC8B,IAAI,CAAC,CAAC,IAAIC;MAChC,CAAC,GAAGN,IACN,CAAC,CAAC;MACFO,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1BrC,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;IAClBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAMgC,UAAU,GAAGA,CAAA,KAAM;IACvBZ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;IAC7B,IAAIpB,WAAW,CAAC4B,IAAI,CAAC,CAAC,EAAE;MACtB,MAAMI,OAAa,GAAG;QACpB/C,EAAE,EAAEgD,IAAI,CAACC,GAAG,CAAC,CAAC;QACdhD,IAAI,EAAEc,WAAW,CAAC4B,IAAI,CAAC,CAAC;QACxBzC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAEgB,WAAW,IAAIyB,SAAS;QAC9BxC,QAAQ,EAAEiB,eAAe;QACzBhB,OAAO,EAAEkB,cAAc,CAACoB,IAAI,CAAC,CAAC,IAAIC,SAAS;QAC3CtC,KAAK,EAAEmB,YAAY,CAACkB,IAAI,CAAC,CAAC,IAAIC;MAChC,CAAC;MACD7C,QAAQ,CAACqC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEW,OAAO,CAAC,CAAC;MACpCG,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1BhC,kBAAkB,CAAC,KAAK,CAAC;IACzBF,cAAc,CAAC,EAAE,CAAC;IAClBI,cAAc,CAAC,EAAE,CAAC;IAClBE,kBAAkB,CAAC,QAAQ,CAAC;IAC5BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAMyB,gBAAgB,GAAI/C,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAMgD,gBAAgB,GAAIhD,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,WAAW;MACjC,KAAK,KAAK;QAAE,OAAO,OAAO;MAC1B;QAAS,OAAO,WAAW;IAC7B;EACF,CAAC;EAED,MAAMiD,cAAc,GAAIhD,OAAe,IAAK;IAC1C6B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE9B,OAAO,CAAC,CAAC,CAAC;IAC1C2B,oBAAoB,CAACI,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC/B,OAAO,GAAG,CAAC+B,IAAI,CAAC/B,OAAO;IAAE,CAAC,CAAC,CAAC;EACxE,CAAC;EAED,MAAMiD,oBAAoB,GAAIjD,OAAe,IAAK;IAChD6B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE9B,OAAO,CAAC,CAAC,CAAC;IAClDN,QAAQ,CAACqC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACjC,OAAO,KAAKA,OAAO,GAAG;MAAE,GAAGiC,IAAI;MAAEpC,SAAS,EAAE;IAAK,CAAC,GAAGoC,IAC5D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,cAAc,GAAGA,CAACC,CAAsB,EAAEC,MAAkB,KAAK;IACrE,IAAID,CAAC,CAACE,GAAG,KAAK,OAAO,EAAE;MACrBD,MAAM,CAAC,CAAC;IACV;EACF,CAAC;;EAED;EACA,MAAME,cAAc,GAAG5E,OAAO,CAAC,MAAM;IACnC,MAAM6E,QAAQ,GAAG9D,KAAK,CACnBuC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACjC,OAAO,CAAC,CACzBmC,MAAM,CAAC,CAACnC,OAAO,EAAEwD,KAAK,EAAEC,KAAK,KAAKzD,OAAO,IAAIyD,KAAK,CAACC,OAAO,CAAC1D,OAAO,CAAC,KAAKwD,KAAK,CAAC,CAC9EG,IAAI,CAAC,CAAC;IACT,OAAOJ,QAAQ;EACjB,CAAC,EAAE,CAAC9D,KAAK,CAAC,CAAC;EAEX,MAAMmE,aAAa,GAAGlF,OAAO,CAAC,MAAM;IAClC,OAAOe,KAAK,CAAC0C,MAAM,CAACF,IAAI,IAAI;MAC1B,MAAM4B,aAAa,GAAGvC,MAAM,KAAK,EAAE,IACjCW,IAAI,CAACrC,IAAI,CAACkE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,MAAM,CAACwC,WAAW,CAAC,CAAC,CAAC,IACrD7B,IAAI,CAAChC,KAAK,IAAIgC,IAAI,CAAChC,KAAK,CAAC6D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,MAAM,CAACwC,WAAW,CAAC,CAAC,CAAE,IACtE7B,IAAI,CAACjC,OAAO,IAAIiC,IAAI,CAACjC,OAAO,CAAC8D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,MAAM,CAACwC,WAAW,CAAC,CAAC,CAAE;MAE7E,MAAME,cAAc,GAAGxC,aAAa,KAAK,EAAE,IAAIS,IAAI,CAACjC,OAAO,KAAKwB,aAAa;MAE7E,OAAOqC,aAAa,IAAIG,cAAc;IACxC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvE,KAAK,EAAE6B,MAAM,EAAEE,aAAa,CAAC,CAAC;EAElC,MAAMyC,YAAY,GAAGvF,OAAO,CAAC,MAAM;IACjC,OAAOkF,aAAa,CAACM,MAAM,CAAC,CAACC,MAAM,EAAElC,IAAI,KAAK;MAC5C,MAAMoB,GAAG,GAAGpB,IAAI,CAACjC,OAAO,IAAI,gBAAgB;MAC5C,IAAI,CAACmE,MAAM,CAACd,GAAG,CAAC,EAAEc,MAAM,CAACd,GAAG,CAAC,GAAG,EAAE;MAClCc,MAAM,CAACd,GAAG,CAAC,CAACe,IAAI,CAACnC,IAAI,CAAC;MACtB,OAAOkC,MAAM;IACf,CAAC,EAAE,CAAC,CAA2B,CAAC;EAClC,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;EAEnB,MAAMS,KAAK,GAAG3F,OAAO,CAAC,MAAM;IAC1B,MAAM4F,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACP,YAAY,CAAC,CAACQ,MAAM;IACrD,MAAMC,UAAU,GAAGd,aAAa,CAACa,MAAM;IACvC,MAAME,SAAS,GAAGf,aAAa,CAACzB,MAAM,CAACyC,CAAC,IAAIA,CAAC,CAAC/E,SAAS,CAAC,CAAC4E,MAAM;IAC/D,MAAMI,WAAW,GAAGH,UAAU,GAAGC,SAAS;IAC1C,MAAMG,QAAQ,GAAGJ,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAEL,SAAS,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IAEhF,OAAO;MAAEJ,YAAY;MAAEI,UAAU;MAAEC,SAAS;MAAEE,WAAW;MAAEC;IAAS,CAAC;EACvE,CAAC,EAAE,CAAClB,aAAa,EAAEK,YAAY,CAAC,CAAC;EAEjC,oBACE7E,OAAA;IAAK6F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB9F,OAAA;MAAK6F,SAAS,EAAC,wIAAwI;MAAAC,QAAA,gBACrJ9F,OAAA;QAAK6F,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD9F,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAK6F,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAEb,KAAK,CAACC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzElG,OAAA;YAAK6F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAK6F,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAEb,KAAK,CAACK;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvElG,OAAA;YAAK6F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAK6F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEb,KAAK,CAACM;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1ElG,OAAA;YAAK6F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAK6F,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEb,KAAK,CAACQ;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1ElG,OAAA;YAAK6F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlG,OAAA;QAAK6F,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvD9F,OAAA;UAAK6F,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC9F,OAAA;YAAM6F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzDlG,OAAA;YAAM6F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAEb,KAAK,CAACS,QAAQ,EAAC,GAAC;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClD9F,OAAA;YACE6F,SAAS,EAAC,6FAA6F;YACvGM,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAGnB,KAAK,CAACS,QAAQ;YAAI;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAC3E9F,OAAA;QAAK6F,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBAEvD9F,OAAA;UAAK6F,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC9F,OAAA,CAACJ,MAAM;YAACiG,SAAS,EAAC;UAAmG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxHlG,OAAA;YACEqG,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEpE,MAAO;YACdqE,QAAQ,EAAExC,CAAC,IAAI5B,SAAS,CAAC4B,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;YACzCG,WAAW,EAAC,oDAA0C;YACtDZ,SAAS,EAAC;UAAqL;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlG,OAAA;UACEsG,KAAK,EAAElE,aAAc;UACrBmE,QAAQ,EAAExC,CAAC,IAAI1B,gBAAgB,CAAC0B,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;UAChDT,SAAS,EAAC,2HAA2H;UAAAC,QAAA,gBAErI9F,OAAA;YAAQsG,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACvChC,cAAc,CAACtB,GAAG,CAAChC,OAAO,iBACzBZ,OAAA;YAAsBsG,KAAK,EAAE1F,OAAQ;YAAAkF,QAAA,EAAElF;UAAO,GAAjCA,OAAO;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmC,CACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNlG,OAAA;QACE0G,OAAO,EAAEA,CAAA,KAAM;UACbjE,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC;UACnCjB,kBAAkB,CAAC,IAAI,CAAC;QAC1B,CAAE;QACFoE,SAAS,EAAC,gNAAgN;QAAAC,QAAA,gBAE1N9F,OAAA,CAACT,IAAI;UAACsG,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAE9B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BX,MAAM,CAACC,IAAI,CAACP,YAAY,CAAC,CAACQ,MAAM,KAAK,CAAC,gBACrCrF,OAAA;QAAK6F,SAAS,EAAC,kFAAkF;QAAAC,QAAA,gBAC/F9F,OAAA;UAAK6F,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClElG,OAAA;UAAK6F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACnC5D,MAAM,IAAIE,aAAa,GAAG,gCAAgC,GAAG;QAAyB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GAENf,MAAM,CAACwB,OAAO,CAAC9B,YAAY,CAAC,CAACjC,GAAG,CAAC,CAAC,CAAChC,OAAO,EAAEgG,YAAY,CAAC,KAAK;QAC5D,MAAMC,WAAW,GAAGD,YAAY,CAAC7D,MAAM,CAACyC,CAAC,IAAIA,CAAC,CAAC/E,SAAS,CAAC,CAAC4E,MAAM;QAChE,MAAMyB,YAAY,GAAGF,YAAY,CAACvB,MAAM;QACxC,MAAM0B,eAAe,GAAGD,YAAY,GAAG,CAAC,GAAGnB,IAAI,CAACC,KAAK,CAAEiB,WAAW,GAAGC,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC;QAC7F,MAAME,WAAW,GAAG1E,iBAAiB,CAAC1B,OAAO,CAAC;QAE9C,oBACEZ,OAAA;UAAmB6F,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjC9F,OAAA;YAAK6F,SAAS,EAAC,gFAAgF;YAAAC,QAAA,gBAC7F9F,OAAA;cAAK6F,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9F,OAAA;gBACE0G,OAAO,EAAEA,CAAA,KAAM9C,cAAc,CAAChD,OAAO,CAAE;gBACvCiF,SAAS,EAAC,uEAAuE;gBACjF,cAAYmB,WAAW,GAAG,mBAAmB,GAAG,qBAAsB;gBAAAlB,QAAA,EAErEkB,WAAW,gBACVhH,OAAA,CAACF,YAAY;kBAAC+F,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAClDlG,OAAA,CAACH,WAAW;kBAACgG,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE7C,CAAC,eACTlG,OAAA;gBAAM6F,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAElF;cAAO;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxElG,OAAA;gBAAM6F,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,GACvEe,WAAW,EAAC,KAAG,EAACC,YAAY,EAAC,UAChC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlG,OAAA;gBAAK6F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChD9F,OAAA;kBACE6F,SAAS,EAAC,6FAA6F;kBACvGM,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGW,eAAe;kBAAI;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELY,YAAY,GAAGD,WAAW,iBACzB7G,OAAA;cACE0G,OAAO,EAAEA,CAAA,KAAM7C,oBAAoB,CAACjD,OAAO,CAAE;cAC7CiF,SAAS,EAAC,oMAAoM;cAAAC,QAAA,EAC/M;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL,CAACc,WAAW,iBACXhH,OAAA;YAAK6F,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD9F,OAAA;cAAO6F,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC9C9F,OAAA;gBAAA8F,QAAA,eACE9F,OAAA;kBAAI6F,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACzB9F,OAAA;oBAAI6F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7DlG,OAAA;oBAAI6F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/DlG,OAAA;oBAAI6F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzDlG,OAAA;oBAAI6F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChElG,OAAA;oBAAI6F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DlG,OAAA;oBAAI6F,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRlG,OAAA;gBAAA8F,QAAA,EACGc,YAAY,CAAChE,GAAG,CAAEC,IAAI,iBACrB7C,OAAA;kBAEE6F,SAAS,EAAE,8DACThD,IAAI,CAACpC,SAAS,GAAG,8BAA8B,GAAG,EAAE,EACnD;kBAAAqF,QAAA,gBAEH9F,OAAA;oBAAI6F,SAAS,EAAE,aAAahD,IAAI,CAACpC,SAAS,GAAG,cAAc,GAAG,EAAE,EAAG;oBAAAqF,QAAA,EAChEhF,WAAW,KAAK+B,IAAI,CAACtC,EAAE,gBACtBP,OAAA;sBACEqG,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAEtF,WAAY;sBACnBuF,QAAQ,EAAExC,CAAC,IAAI9C,cAAc,CAAC8C,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;sBAC9CW,UAAU,EAAElD,CAAC,IAAID,cAAc,CAACC,CAAC,EAAEd,cAAc,CAAE;sBACnD4C,SAAS,EAAC,oHAAoH;sBAC9HqB,SAAS;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,gBAEFlG,OAAA;sBAAM6F,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEjD,IAAI,CAACrC;oBAAI;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAC/C;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLlG,OAAA;oBAAI6F,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtBhF,WAAW,KAAK+B,IAAI,CAACtC,EAAE,gBACtBP,OAAA;sBACEqG,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAElF,YAAa;sBACpBmF,QAAQ,EAAExC,CAAC,IAAI1C,eAAe,CAAC0C,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;sBAC/CW,UAAU,EAAElD,CAAC,IAAID,cAAc,CAACC,CAAC,EAAEd,cAAc,CAAE;sBACnD4C,SAAS,EAAC;oBAAoH;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/H,CAAC,gBAEFlG,OAAA;sBAAM6F,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEjD,IAAI,CAAChC,KAAK,IAAI;oBAAG;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAC1D;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLlG,OAAA;oBAAI6F,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACvB9F,OAAA;sBAAM6F,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEjD,IAAI,CAACnC,IAAI,IAAI;oBAAG;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACLlG,OAAA;oBAAI6F,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACvB9F,OAAA;sBAAM6F,SAAS,EAAE,kCAAkCnC,gBAAgB,CAACb,IAAI,CAAClC,QAAQ,CAAC,cAAe;sBAAAmF,QAAA,EAC9FnC,gBAAgB,CAACd,IAAI,CAAClC,QAAQ;oBAAC;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLlG,OAAA;oBAAI6F,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACvB9F,OAAA;sBACE0G,OAAO,EAAEA,CAAA,KAAMlE,UAAU,CAACK,IAAI,CAACtC,EAAE,CAAE;sBACnCsF,SAAS,EAAE,mJACThD,IAAI,CAACpC,SAAS,GACV,+EAA+E,GAC/E,iCAAiC,EACpC;sBACH,cAAW,sCAAuB;sBAAAqF,QAAA,eAElC9F,OAAA;wBACE6F,SAAS,EAAE,oIACThD,IAAI,CAACpC,SAAS,GAAG,4BAA4B,GAAG,EAAE,EACjD;wBAAAqF,QAAA,EAEFjD,IAAI,CAACpC,SAAS,iBAAIT,OAAA,CAACL,KAAK;0BAACkG,SAAS,EAAC;wBAAoB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACLlG,OAAA;oBAAI6F,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACvB9F,OAAA;sBAAK6F,SAAS,EAAC,YAAY;sBAAAC,QAAA,EACxBhF,WAAW,KAAK+B,IAAI,CAACtC,EAAE,gBACtBP,OAAA,CAAAE,SAAA;wBAAA4F,QAAA,gBACE9F,OAAA;0BACE0G,OAAO,EAAEzD,cAAe;0BACxB4C,SAAS,EAAC,2MAA2M;0BACrNsB,KAAK,EAAC,2BAAsB;0BAAArB,QAAA,eAE5B9F,OAAA,CAACL,KAAK;4BAACkG,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACTlG,OAAA;0BACE0G,OAAO,EAAEtD,aAAc;0BACvByC,SAAS,EAAC,iMAAiM;0BAC3MsB,KAAK,EAAC,+BAAqB;0BAAArB,QAAA,eAE3B9F,OAAA,CAACN,CAAC;4BAACmG,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB,CAAC;sBAAA,eACT,CAAC,gBAEHlG,OAAA,CAAAE,SAAA;wBAAA4F,QAAA,gBACE9F,OAAA;0BACE0G,OAAO,EAAEA,CAAA,KAAM1D,gBAAgB,CAACH,IAAI,CAAE;0BACtCgD,SAAS,EAAC,uMAAuM;0BACjNsB,KAAK,EAAC,6BAAmB;0BAAArB,QAAA,eAEzB9F,OAAA,CAACR,IAAI;4BAACqG,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTlG,OAAA;0BACE0G,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAACD,IAAI,CAACtC,EAAE,CAAE;0BACnCsF,SAAS,EAAC,iMAAiM;0BAC3MsB,KAAK,EAAC,iCAAkB;0BAAArB,QAAA,eAExB9F,OAAA,CAACP,MAAM;4BAACoG,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA,eACT;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAjGArD,IAAI,CAACtC,EAAE;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkGV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA,GA1JOtF,OAAO;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2JZ,CAAC;MAEV,CAAC;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLjB,KAAK,CAACK,UAAU,GAAG,CAAC,iBACnBtF,OAAA;MAAK6F,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eAC/D9F,OAAA;QAAK6F,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC7B,eAAA9F,OAAA;UAAM6F,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAEb,KAAK,CAACK;QAAU;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,mDAChE,eAAAlG,OAAA;UAAM6F,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEb,KAAK,CAACM;QAAS;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,kBACzE,eAAAlG,OAAA;UAAM6F,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAEb,KAAK,CAACQ;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA1E,eAAe,iBACdxB,OAAA;MAAK6F,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC/F9F,OAAA;QAAK6F,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvG9F,OAAA;UAAI6F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhFlG,OAAA;UAAK6F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9F,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAO6F,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/FlG,OAAA;cACEqG,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEhF,WAAY;cACnBiF,QAAQ,EAAExC,CAAC,IAAIxC,cAAc,CAACwC,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;cAC9CW,UAAU,EAAElD,CAAC,IAAID,cAAc,CAACC,CAAC,EAAEV,UAAU,CAAE;cAC/CoD,WAAW,EAAC,iDAAkC;cAC9CZ,SAAS,EAAC,gLAAgL;cAC1LqB,SAAS;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlG,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAO6F,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxFlG,OAAA;cACEqG,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEtE,YAAa;cACpBuE,QAAQ,EAAExC,CAAC,IAAI9B,eAAe,CAAC8B,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;cAC/CG,WAAW,EAAC,8CAAoC;cAChDZ,SAAS,EAAC;YAAgL;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3L,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlG,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAO6F,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjFlG,OAAA;cACEqG,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExE,cAAe;cACtByE,QAAQ,EAAExC,CAAC,IAAIhC,iBAAiB,CAACgC,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;cACjDG,WAAW,EAAC,2CAAiC;cAC7CZ,SAAS,EAAC,gLAAgL;cAC1LuB,IAAI,EAAC;YAAmB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFlG,OAAA;cAAUO,EAAE,EAAC,mBAAmB;cAAAuF,QAAA,EAC7B5B,cAAc,CAACtB,GAAG,CAAChC,OAAO,iBACzBZ,OAAA;gBAAsBsG,KAAK,EAAE1F;cAAQ,GAAxBA,OAAO;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmB,CACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENlG,OAAA;YAAK6F,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC9F,OAAA;cAAA8F,QAAA,gBACE9F,OAAA;gBAAO6F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5ElG,OAAA;gBACEqG,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE5E,WAAY;gBACnB6E,QAAQ,EAAExC,CAAC,IAAIpC,cAAc,CAACoC,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;gBAC9CT,SAAS,EAAC;cAA2J;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlG,OAAA;cAAA8F,QAAA,gBACE9F,OAAA;gBAAO6F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFlG,OAAA;gBACEsG,KAAK,EAAE1E,eAAgB;gBACvB2E,QAAQ,EAAExC,CAAC,IAAIlC,kBAAkB,CAACkC,CAAC,CAACyC,MAAM,CAACF,KAAkC,CAAE;gBAC/ET,SAAS,EAAC,2JAA2J;gBAAAC,QAAA,gBAErK9F,OAAA;kBAAQsG,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClG,OAAA;kBAAQsG,KAAK,EAAC,QAAQ;kBAAAR,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzClG,OAAA;kBAAQsG,KAAK,EAAC,MAAM;kBAAAR,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlG,OAAA;UAAK6F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9F,OAAA;YACE0G,OAAO,EAAEjD,aAAc;YACvBoC,SAAS,EAAC,qIAAqI;YAAAC,QAAA,EAChJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlG,OAAA;YACE0G,OAAO,EAAErD,UAAW;YACpBgE,QAAQ,EAAE,CAAC/F,WAAW,CAAC4B,IAAI,CAAC,CAAE;YAC9B2C,SAAS,EAAC,+OAA+O;YAAAC,QAAA,EAC1P;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9F,EAAA,CAhhBID,SAAmB;AAAAmH,EAAA,GAAnBnH,SAAmB;AAkhBzB,eAAeA,SAAS;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}